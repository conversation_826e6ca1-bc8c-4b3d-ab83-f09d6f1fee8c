<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonRankDao">

    <select id="carbonRankList" resultType="com.lvpuhui.gic.wxapp.carbon.dto.CarbonRankUserVo">
        SELECT u.nick_name  AS nickName,
                u.avatar_url AS avatarUrl,
                r.amount_sum as estimateAmountSum,
                r.`amount`,
                r.`rank`,
                u.mobile_sha256
         FROM (SELECT * FROM gl_carbon_rank ORDER BY `rank` ASC LIMIT 20) r
                  INNER JOIN gl_user u ON r.user_id = u.id
    </select>
    <select id="selectByUserId" resultType="com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonRankDO">
        select * from gl_carbon_rank where user_id = #{userId}
    </select>
    <select id="getCurrentUserRank" resultType="com.lvpuhui.gic.wxapp.carbon.dto.CarbonRankUserVo">
        SELECT u.nick_name  AS nickName,
               u.avatar_url AS avatarUrl,
               r.amount_sum as estimateAmountSum,
               r.`amount`,
               r.`rank`,
               u.mobile_sha256
        FROM gl_carbon_rank r
                 INNER JOIN gl_user u ON r.user_id = u.id
        WHERE u.mobile_sha256 = #{mobileSha256}
    </select>
</mapper>
