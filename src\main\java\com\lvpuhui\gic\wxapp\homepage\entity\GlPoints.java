package com.lvpuhui.gic.wxapp.homepage.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 积分流水表(GlPoints)表实体类
 *
 * <AUTHOR>
 * @since 2022-05-06 13:45:10
 */
@Data
@SuppressWarnings("serial")
public class GlPoints extends Model<GlPoints> {
    @TableId(type = IdType.ASSIGN_ID)
    /**自增id*/
    private Long id;
    /**用户id(sha256)*/
    private String mobileSha256;
    /**积分产生类型 1绿色消费 2绿色行为 3探店打卡 4绿色问卷 5积分兑换（负）*/
    private Integer type;
    /**积分点数（+/-）*/
    @TableField("`point`")
    private Double point;
    /**关联数据表（behavior, consumption, tickoff, exchange）*/
    private String tableName;
    /**关联id*/
    private Long sourceId;
    /**企业名称*/
    private String companyName;
    /**企业id*/
    private Long companyId;
    /**创建时间*/
    private Date created;
    private String orderId;
    private String description;

}