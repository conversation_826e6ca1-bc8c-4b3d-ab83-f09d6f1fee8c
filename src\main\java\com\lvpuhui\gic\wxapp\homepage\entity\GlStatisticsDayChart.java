package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 每日图表统计表(gl_statistics_day_chart)实体类
 * <AUTHOR>
 * @since 2022-07-18
 */
@Data
public class GlStatisticsDayChart extends Model<GlStatisticsDayChart> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 新增用户数
     */
    private Long addUser;

    /**
     * 每天减排量总额
     */
    private BigDecimal emissionSum;

    /**
     * 每天消费金额总额
     */
    private BigDecimal consumeSum;

    /**
     * 每天积分发放总额
     */
    private Long pointsGrant;

    /**
     * 每天消费积分发放总额
     */
    private Long consumePoints;

    /**
     * 每天减排积分发放总额
     */
    private Long emissionPoints;

    /**
     * 每天活动积分发放总额
     */
    private Long activityPoints;

    /**
     * 减排人数
     */
    private Long emissionUserNumber;

    /**
     * 减排次数
     */
    private Long emissionNumber;

    /**
     * 每天消耗积分总额
     */
    private Long expendPoints;

    /**
     * 统计时间
     */
    private String statisticsTime;
}
