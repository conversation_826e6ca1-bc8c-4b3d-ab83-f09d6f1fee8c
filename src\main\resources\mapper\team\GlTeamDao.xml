<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.team.dao.GlTeamDao">
    <update id="updateIncrementEmission">
        UPDATE gl_team SET emission = IFNULL(emission,0) + #{emission},member_count = IFNULL(member_count,0) + 1 WHERE id = #{teamId}
    </update>
    <update id="updateDeductEmission">
        UPDATE gl_team SET emission = IF(emission - #{emission} &lt; 0,0,emission - #{emission}),member_count = IF(member_count - 1 &lt; 0,0,member_count - 1) WHERE id = #{teamId}
    </update>

    <select id="getTeamList" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamListVo">
        SELECT
            gt.id,
            gt.team_name,
            gt.team_logo,
            gt.member_count,
            gt.certification_status as certification,
            gt.emission,
            gu.nick_name,
            gu.avatar_url,
            gt.type
        FROM
            gl_team gt
                INNER JOIN gl_user gu ON gt.captain_id = gu.id
        WHERE gt.`status` = 0 AND gt.join_mode = 0
        <if test="teamListDto.keywords != null and teamListDto.keywords != ''">
            AND gt.team_name LIKE concat('%',#{teamListDto.keywords},'%')
        </if>
        ORDER BY
            gt.member_count DESC
            LIMIT #{teamListDto.start},#{teamListDto.size}
    </select>
</mapper>

