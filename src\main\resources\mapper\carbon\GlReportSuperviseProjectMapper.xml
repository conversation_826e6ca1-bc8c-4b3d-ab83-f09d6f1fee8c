<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.beecgreenlife.carbon.dao.GlReportSuperviseProjectMapper">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.carbon.entity.GlReportSuperviseProjectDO">
    <!--@mbg.generated-->
    <!--@Table gl_report_supervise_project-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supervise_id" jdbcType="BIGINT" property="superviseId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="points" jdbcType="BIGINT" property="points" />
    <result column="rank" jdbcType="INTEGER" property="rank" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, supervise_id, `name`, logo, points, `rank`, description, deleted, created, updated
  </sql>


</mapper>