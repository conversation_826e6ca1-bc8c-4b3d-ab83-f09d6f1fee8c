package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "举报提交dto")
public class ReportSubmitDto {

    /**
     * 举报类型
     */
    @Schema(description = "举报类型")
    @NotNull(message = "请选择举报的类型")
    private Long reportType;

    /**
     * 举报位置的经度
     */
    @Schema(description = "举报位置的纬度")
    @NotNull(message = "请您打开地理位置")
    private BigDecimal reportLongitude;

    /**
     * 举报位置的纬度
     */
    @Schema(description = "举报位置的纬度")
    @NotNull(message = "请您打开地理位置")
    private BigDecimal reportLatitude;

    @Schema(description = "举报提交资源数组")
    @Size(message = "请您上传文件",min = 1)
    private List<ReportSubmitResourceDto> submitResourceDtos;

    @Data
    @Schema(description = "举报提交资源dto")
    public static class ReportSubmitResourceDto{

        /**
         * 资源类型 0：图片 1：视频
         */
        @Schema(description = "资源类型 0：图片 1：视频")
        @NotNull(message = "请选择资源类型")
        @Min(value = 0,message = "资源类型错误")
        @Max(value = 1,message = "资源类型错误")
        private Integer mediaType;

        /**
         * 资源地址
         */
        @Schema(description = "资源地址")
        @NotBlank(message = "请上传文件")
        private String mediaUrl;
    }
}
