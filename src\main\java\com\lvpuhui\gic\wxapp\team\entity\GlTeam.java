package com.lvpuhui.gic.wxapp.team.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 战队表(GlTeam)表实体类
 *
 * <AUTHOR>
 * @since 2023-05-24 11:24:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_team")
public class GlTeam extends Model<GlTeam> {

    /**
     * 战队ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 战队名称
     */
    private String teamName;

    /**
     * 队长ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long captainId;

    /**
     * 战队Logo
     */
    private String teamLogo;

    /**
     * 战队简介
     */
    private String teamDescription;

    /**
     * 邀请码
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String inviteCode;

    /**
     * 加入方式,open:0,invite_only:1
     */
    private Integer joinMode;

    /**
     * 战队状态:active:0, inactive:1
     */
    private Integer status;

    /**
     * 是否已认证 0：未认证  1：已认证
     */
    private Integer certificationStatus;

    /**
     * 成员人数
     */
    private Integer memberCount;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 类型 0:企业 1:学校 2:政府 3:其他
     */
    private Integer type;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}

