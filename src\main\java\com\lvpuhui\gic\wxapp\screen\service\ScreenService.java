package com.lvpuhui.gic.wxapp.screen.service;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbonbook.dto.CarbonBooksNewVo;
import com.lvpuhui.gic.wxapp.homepage.dto.RankAccumulateList;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenAccumulateRankDto;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenCarbonBooksMobileDto;

/**
 * 屏幕相关服务接口
 * <AUTHOR>
 */
public interface ScreenService {

    /**
     * 首页减排量统计接口
     * @return 减排量统计结果
     */
    R getEmission();

    /**
     * 累积排行接口
     * @param screenAccumulateRankDto 累积排行请求参数
     * @return 累积排行结果
     */
    RankAccumulateList accumulateRank(ScreenAccumulateRankDto screenAccumulateRankDto);

    /**
     * 碳账本接口
     * @param screenCarbonBooksMobileDto 碳账本请求参数
     * @return 碳账本结果
     */
    CarbonBooksNewVo carbonBooksMobile(ScreenCarbonBooksMobileDto screenCarbonBooksMobileDto);
}
