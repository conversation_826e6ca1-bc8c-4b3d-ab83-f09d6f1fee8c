package com.lvpuhui.gic.wxapp.homepage.enums;

import lombok.Getter;

/**
 * 打卡类型位置开关状态枚举
 * <AUTHOR>
 * @since 2022年05月05日 18:20:00
 */
@Getter
public enum TickoffTypeSwitch {
    OFF(0,"不判断地理位置"),
    ON(1,"判断地理位置"),
    ;

    private Integer state;

    private String describe;

    TickoffTypeSwitch(Integer state, String describe) {
        this.state = state;
        this.describe = describe;
    }
}
