package com.lvpuhui.gic.wxapp.carbon.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbon.dto.IncomeTakingVo;
import com.lvpuhui.gic.wxapp.carbon.dto.TakingCertificateVo;
import com.lvpuhui.gic.wxapp.carbon.dto.TakingVo;
import com.lvpuhui.gic.wxapp.carbon.service.CarbonBillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tags(value = {@Tag(name = "收入提现1.0")})
@RestController
@RequestMapping("")
public class CarbonBillController {

    @Resource
    private CarbonBillService carbonBillService;

    @Operation(summary = "收入提现接口", description = "收入提现接口", tags = { "收入提现1.0" })
    @GetMapping("/income_taking")
    public R<IncomeTakingVo> incomeTaking() {
        IncomeTakingVo incomeTakingVo = carbonBillService.incomeTaking();
        return R.ok(incomeTakingVo);
    }

    @Operation(summary = "提现接口", description = "提现接口", tags = { "收入提现1.0" })
    @GetMapping("/taking")
    public R<TakingVo> taking() {
        TakingVo takingVo = carbonBillService.taking();
        return R.ok(takingVo);
    }

    @Operation(summary = "提现凭证列表接口", description = "提现凭证列表接口", tags = { "收入提现1.0" })
    @GetMapping("/taking_certificate")
    public R<List<TakingCertificateVo>> takingCertificate() {
        List<TakingCertificateVo> takingCertificateVos = carbonBillService.takingCertificate();
        return R.ok(takingCertificateVos);
    }

    @Operation(summary = "提现凭证详情接口", description = "提现凭证详情接口", tags = { "收入提现1.0" })
    @GetMapping("/takingDetail")
    public R<TakingVo> takingDetail(@RequestParam("id") @Schema(description = "提现凭证ID") Long id) {
        TakingVo takingVo = carbonBillService.takingDetail(id);
        return R.ok(takingVo);
    }
}
