package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "碳汇明细返回值")
public class CarbonDetailsVo {

    /**
     * 今年预计收益
     */
    @Schema(description = "今年预计收益")
    private BigDecimal amount;

    /**
     * 可提现金额
     */
    @Schema(description = "可提现金额")
    private BigDecimal remainAmount;

    /**
     * 面积
     */
    @Schema(description = "面积")
    private BigDecimal area;

    /**
     * 预计碳汇减排量
     */
    @Schema(description = "今年预计减排量")
    private BigDecimal emission;

    /**
     * 补贴价格
     */
    @Schema(description = "补贴价格")
    private BigDecimal subsidyPrice;

    @Schema(description = "补贴价格单位")
    private String subsidySingle;

    /**
     * 奖惩累计
     */
    @Schema(description = "奖惩累计")
    private BigDecimal rewordAmount;

    /**
     * 日志详情
     */
    @Schema(description = "日志详情")
    private List<OperationVo> logList;


}
