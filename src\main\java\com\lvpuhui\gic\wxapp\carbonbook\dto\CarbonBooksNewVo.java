package com.lvpuhui.gic.wxapp.carbonbook.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 新碳账本返回值
 */
@Data
@Accessors(chain = true)
public class CarbonBooksNewVo{

    /**
     * 格式化减排量
     */
    private String emission;

    /**
     * 减排量
     */
    private Double emissionNum = 0.0;

    /**
     * 积分
     */
    private Integer point = 0;

    /**
     * 减排级别名称
     */
    private String title;

    /**
     * 排行
     */
    private Long rank;

    /**
     * 打败了多少人的占比
     */
    private Double radio;

    /**
     * 行为减排量
     */
    private List<EmissionInfoVo> sceneList;

    @Data
    public static class EmissionInfoVo{

        /**
         * 行为ID
         */
        private Integer id;

        /**
         * 行为名称
         */
        private String name;

        /**
         * 格式化行为减排量
         */
        private String emission;

        /**
         * 行为减排量
         */
        private Double emissionNum;

        /**
         * 占比
         */
        private Double ratio;
    }
}