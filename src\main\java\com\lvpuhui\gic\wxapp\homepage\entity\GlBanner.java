package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 轮播图表(GlBanner)表实体类
 * <AUTHOR>
 * @since 2022年05月05日 17:49:00
 */
@Data
public class GlBanner extends Model<GlBanner> {

    @TableId(type = IdType.AUTO)
    /**轮播图ID*/
    private Long id;
    /**banner封面图*/
    private String coverImage;
    /**banner标题*/
    private String title;
    /**跳转地址*/
    private String jumpUrl;
    /**banner状态(0：保存  1：发布)*/
    private Integer state;
    /**位置权重*/
    private Integer sequence;
    /**创建时间*/
    private Date created;
    /**创建者ID*/
    private Long creator;
    /**更新时间*/
    private Date updated;
    /**修改者ID*/
    private Long updator;
    /**删除 0未删除 1已删除*/
    private Integer deleted;

    /**
     * 小程序appId
     */
    private String appId;
}
