package com.lvpuhui.gic.wxapp.infrastructure.utils;

import com.alibaba.fastjson.JSON;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.infrastructure.dto.User;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppAuthException;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.Objects;

/**
 * 用户信息类
 * <AUTHOR>
 * @since 2023年03月17日 16:12:00
 */
public class UserUtils {

    public static Long getUserId(){
        return getUser().getId();
    }

    public static String getMobileSha256(){
        return getUser().getMobileSha256();
    }

    public static User getUser(){
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if(Objects.isNull(requestAttributes)){
            throw new GicWxAppAuthException("请您重新进入小程序");
        }
        Object userObject = requestAttributes.getAttribute(Global.USER, RequestAttributes.SCOPE_REQUEST);
        if(Objects.isNull(userObject)){
            throw new GicWxAppException("请您使用正确渠道使用小程序");
        }
        return JSON.parseObject(userObject.toString(), User.class);
    }

    public static boolean userIsAuth(){
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if(Objects.isNull(requestAttributes)){
            return false;
        }
        Object userObject = requestAttributes.getAttribute(Global.USER, RequestAttributes.SCOPE_REQUEST);
        if(Objects.isNull(userObject)){
            return false;
        }
        return true;
    }
}
