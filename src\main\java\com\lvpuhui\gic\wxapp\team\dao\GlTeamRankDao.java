package com.lvpuhui.gic.wxapp.team.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.team.dto.TeamRankListVo;
import com.lvpuhui.gic.wxapp.team.entity.GlTeamRank;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 战队排行表(GlTeamRank)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-24 11:24:26
 */
public interface GlTeamRankDao extends BaseMapper<GlTeamRank> {

    /**
     * 查询前20的战队
     */
    List<TeamRankListVo> selectTeamRanks();

    /**
     * 查询战队排行榜信息
     * @param teamId 战队ID
     */
    TeamRankListVo selectTeamRankByTeamId(@Param("teamId") Long teamId);

    /**
     * 扣减排行榜战队的减排量
     * @param emission 减排量
     * @param teamId 战队ID
     */
    int updateDeductEmission(@Param("emission") BigDecimal emission,@Param("teamId") Long teamId);

    /**
     * 增加排行榜战队的减排量
     * @param emission 减排量
     * @param teamId 战队ID
     */
    int updateIncrementEmission(@Param("emission") BigDecimal emission,@Param("teamId") Long teamId);
}
