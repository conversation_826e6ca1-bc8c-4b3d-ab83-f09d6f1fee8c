package com.lvpuhui.gic.wxapp.treeplanting.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * Data Transfer Object for Tree Planting Information.
 */
@Data
public class TreePlantingDTO {

    @Schema(description = "单位名称")
    @NotBlank(message = "您的单位为空")
    @Size(max = 100, message = "单位名称长度过长")
    private String unitName;

    @Schema(description = "植树棵树")
    @NotNull(message = "请输入您的植树数量(棵)")
    @Min(value = 1, message = "植树数量不能小于1")
    @Max(value = Integer.MAX_VALUE, message = "植树数量过大")
    private Integer treeCount;

    @Schema(description = "树种")
    @NotBlank(message = "请选择树种")
    @Size(max = 50, message = "树种名称长度过长")
    private String treeSpecies;

    @Schema(description = "植树前图片的URL")
    @Size(max = 255, message = "植树前图片的URL非法")
    private String treeImageBeforeUrl;

    @Schema(description = "植树后图片的URL")
    @Size(max = 255, message = "植树后图片的URL非法")
    private String treeImageAfterUrl;

    @Schema(description = "植树地理位置")
    @Size(max = 255, message = "植树地理位置非法")
    private String location;

    @Schema(description = "植树位置纬度")
    private Double latitude;

    @Schema(description = "植树位置经度")
    private Double longitude;
}
