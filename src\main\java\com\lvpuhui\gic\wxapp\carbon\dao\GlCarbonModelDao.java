package com.lvpuhui.gic.wxapp.carbon.dao;

import com.lvpuhui.gic.wxapp.carbon.dto.CarbonModelMessgae;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 碳汇项目实例（用户申请） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
public interface GlCarbonModelDao extends BaseMapper<GlCarbonModelDO> {


    /**
     * 根据用户id查询所有model信息
     * @param userId
     * @return
     */
    List<CarbonModelMessgae> selectByUserId(@Param("userId")Long userId);

    List<GlCarbonModelDO> selectAll(@Param("userId")Long userId);

    List<GlCarbonModelDO> selectByArea(@Param("userId")Long userId);

    long selectByProjectId(@Param("projectId")Long projectId);

    BigDecimal sumAreaByUser(@Param("userId")Long userId);

    List<GlCarbonModelDO> selectByUserIdTotalEmission(@Param("userId")Long userId);
}
