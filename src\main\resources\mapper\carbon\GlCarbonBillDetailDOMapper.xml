<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonBillDetailDao">

    <select id="selectBillDetailByUserId" resultType="com.lvpuhui.gic.wxapp.carbon.dto.TakingCertificateVo">
        SELECT
            cbd.id,
            cbd.user_name,
            cbd.withdraw_time,
            cbd.amount,
            cbd.`status`,
            cbd.receive_time,
            cb.receive_end_time
        FROM
            gl_carbon_bill_detail cbd
                INNER JOIN gl_carbon_bill cb ON cbd.bill_id = cb.id
        WHERE
            cbd.user_id = #{userId}
    </select>
</mapper>
