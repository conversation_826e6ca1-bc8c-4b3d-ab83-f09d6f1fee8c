package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 门店位置表(gl_outlets_position)实体类
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class GlOutletsPosition extends Model<GlOutletsPosition> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 打卡类别ID
     */
    private Long typeId;

    /**
     * 纬度(高德)
     */
    private Double latitude;

    /**
     * 经度(高德)
     */
    private Double longitude;

    /**
     * 门店名称
     */
    private String outletsName;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;
}
