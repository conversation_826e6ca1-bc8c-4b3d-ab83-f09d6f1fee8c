package com.lvpuhui.gic.wxapp.homepage.service;

import com.lvpuhui.gic.wxapp.homepage.entity.GlRank;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.dto.RankAccumulateListDto;
import com.lvpuhui.gic.wxapp.homepage.dto.RankAccumulateList;
import com.lvpuhui.gic.wxapp.homepage.dto.RankInviteList;

/**
 * 累积排行榜 服务类
 * <AUTHOR>
 * @since 2022-07-25
 */
public interface GlRankService extends IService<GlRank> {

    /**
     * 累积排行接口
     */
    RankAccumulateList accumulateRank(RankAccumulateListDto rankAccumulateListDto);

    RankInviteList getRankInviteList(RankAccumulateListDto rankAccumulateListDto);
}
