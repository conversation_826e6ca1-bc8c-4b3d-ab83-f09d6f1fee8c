<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.beecgreenlife.carbon.dao.GlReportSuperviseClassifyMapper">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.carbon.entity.GlReportSuperviseCategoryDO">
    <!--@mbg.generated-->
    <!--@Table gl_report_supervise_classify-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="rank" jdbcType="INTEGER" property="rank" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, deleted, `rank`, created, updated
  </sql>
</mapper>