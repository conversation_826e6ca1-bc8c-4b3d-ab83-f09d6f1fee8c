package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 累积排行榜表(gl_rank)实体类
 * <AUTHOR>
 * @since 2022-07-25
 */
@Data
public class GlRank extends Model<GlRank> {

    @TableId(value = "mobile_sha256",type = IdType.INPUT)
    private String mobileSha256;

    @TableField(value = "`rank`")
    private Long rank;

    private Double emission;

    /**
     * 在整个城市中的排行榜
     */
    private Long cityRank;
}
