package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 排行榜快照表(gl_rank_snapshot)实体类
 * <AUTHOR>
 * @since 2022-07-25
 */
@Data
public class GlRankSnapshot extends Model<GlRankSnapshot> {

    /**
     * 用户手机号sha256
     */
    @TableId(value = "mobile_sha256",type = IdType.INPUT)
    private String mobileSha256;

    /**
     * 排名
     */
    @TableField(value = "`rank`")
    private Long rank;

    /**
     * 在整个城市中的排行榜
     */
    private Long cityRank;

    /**
     * 减排量
     */
    private Double emission;

    /**
     * 创建时间
     */
    private Date created;
}
