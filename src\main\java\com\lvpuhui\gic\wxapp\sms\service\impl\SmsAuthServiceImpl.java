package com.lvpuhui.gic.wxapp.sms.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.lvpuhui.gic.wxapp.base.dao.GlAuthTokenDao;
import com.lvpuhui.gic.wxapp.base.entity.GlAuthTokenDO;
import com.lvpuhui.gic.wxapp.carbonbook.dto.CarbonBooksVo;
import com.lvpuhui.gic.wxapp.carbonbook.service.CarbonBookService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankDao;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRank;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.other.utils.AES256Util;
import com.lvpuhui.gic.wxapp.sms.dto.SmsAuthLoginDto;
import com.lvpuhui.gic.wxapp.sms.dto.SmsAuthSendDto;
import com.lvpuhui.gic.wxapp.sms.dto.SmsAuthVo;
import com.lvpuhui.gic.wxapp.sms.service.SmsAuthService;
import com.lvpuhui.gic.wxapp.sms.utils.SmsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SmsAuthServiceImpl implements SmsAuthService {

    @Resource
    private SmsUtils smsUtils;

    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlPointsService glPointsService;

    @Resource
    private GlRankDao glRankDao;

    @Resource
    private GlAuthTokenDao glAuthTokenDao;

    @Resource
    private CarbonBookService carbonBookService;

    @Resource
    private AES256Util aes256Util;

    private final Cache<Object, Object> cache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    @Override
    public void send(SmsAuthSendDto smsAuthSendDto) {
        String mobile = smsAuthSendDto.getMobile();
        if(!PhoneUtil.isMobile(mobile)){
            throw new GicWxAppException("请输入正确的手机号");
        }
        String code = smsUtils.sendSms(mobile);
        if(StrUtil.isBlank(code)){
            throw new GicWxAppException("发送失败，请您稍后重试");
        }
        cache.put(mobile,code);
    }

    @Override
    public SmsAuthVo login(SmsAuthLoginDto smsAuthLoginDto) {
        String mobile = smsAuthLoginDto.getMobile();
        String code = smsAuthLoginDto.getCode();
        if(!PhoneUtil.isMobile(mobile)){
            throw new GicWxAppException("请输入正确的手机号");
        }
        if(!code.equals(cache.getIfPresent(mobile))){
            throw new GicWxAppException("验证码错误或失效");
        }
        String mobileSha256 = SecureUtil.sha256(mobile);
        String cryptoMobile = aes256Util.encrypt(mobile);
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserByMobileQuery = Wrappers.lambdaQuery();
        glUserByMobileQuery.eq(GlUser::getMobileSha256,mobileSha256);
        glUserByMobileQuery.last("limit 1");
        GlUser mobileGlUser = glUserDao.selectOne(glUserByMobileQuery);
        if(Objects.isNull(mobileGlUser)){
            GlUser glUser = new GlUser();
            glUser.setMobile(cryptoMobile);
            glUser.setMobileSha256(mobileSha256);
            glUser.setCreated(new Date());
            glUser.setUpdated(new Date());
            glUserDao.insert(glUser);
            mobileGlUser = glUser;
        }else {
            mobileGlUser.setMobile(cryptoMobile);
            mobileGlUser.setMobileSha256(mobileSha256);
            mobileGlUser.setUpdated(new Date());
            glUserDao.updateById(mobileGlUser);
        }
        // 用户授权的时候 查询下积分表 算出来 总积分/剩余积分/使用积分
        glPointsService.calcUserPoints(mobileSha256);

        LambdaQueryWrapper<GlAuthTokenDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(GlAuthTokenDO::getUserId,mobileGlUser.getId());
        GlAuthTokenDO glAuthTokenDO = glAuthTokenDao.selectOne(lambdaQueryWrapper);
        if(Objects.isNull(glAuthTokenDO)){
            glAuthTokenDO = new GlAuthTokenDO();
            glAuthTokenDO.setToken(IdUtil.fastSimpleUUID());
            glAuthTokenDO.setUserId(mobileGlUser.getId());
            glAuthTokenDao.insert(glAuthTokenDO);
        }
        GlRank glRank = glRankDao.selectById(mobileSha256);
        if(Objects.isNull(glRank)){
            glRank = new GlRank();
            glRank.setMobileSha256(mobileSha256);
            glRank.setRank(-1L);
            CarbonBooksVo carbonBooksByMobileSha256 = carbonBookService.getCarbonBooksByMobileSha256(mobileSha256);
            if(Objects.nonNull(carbonBooksByMobileSha256)){
                glRank.setEmission(Objects.isNull(carbonBooksByMobileSha256.getEmissionNum())?0:carbonBooksByMobileSha256.getEmissionNum());
            }
            glRankDao.insert(glRank);
        }

        SmsAuthVo smsAuthVo = new SmsAuthVo();
        smsAuthVo.setAvatarUrl(mobileGlUser.getAvatarUrl());
        smsAuthVo.setNickName(mobileGlUser.getNickName());
        smsAuthVo.setOpenid(mobileGlUser.getOpenId());
        smsAuthVo.setMobileSha256(mobileSha256);
        smsAuthVo.setToken(glAuthTokenDO.getToken());
        smsAuthVo.setMobile(String.valueOf(PhoneUtil.hideBetween(mobile)));
        return smsAuthVo;
    }
}