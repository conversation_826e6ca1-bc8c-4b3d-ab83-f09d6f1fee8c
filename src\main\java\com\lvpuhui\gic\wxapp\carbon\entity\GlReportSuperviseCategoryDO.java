package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
    * 监督类型表
    */
@Schema(description="监督类型表")
@Data
@TableName(value = "gl_report_supervise_category")
public class GlReportSuperviseCategoryDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

    /**
     * 类型名称
     */
    @TableField(value = "`name`")
    @Schema(description="类型名称")
    private String name;

    /**
     * 删除状态
     */
    @TableField(value = "deleted")
    @Schema(description="删除状态")
    private Integer deleted;

    /**
     * 排序
     */
    @TableField(value = "`rank`")
    @Schema(description="排序")
    private Integer rank;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private LocalDateTime created;

    /**
     * 修改时间
     */
    @TableField(value = "updated")
    @Schema(description="修改时间")
    private LocalDateTime updated;
}