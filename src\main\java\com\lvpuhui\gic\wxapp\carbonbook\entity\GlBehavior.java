package com.lvpuhui.gic.wxapp.carbonbook.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 绿色行为记录表(GlBehavior)表实体类
 *
 * <AUTHOR>
 * @since 2022-06-23 16:21:36
 */
@Data
@Accessors(chain = true)
@TableName("gl_behavior")
public class GlBehavior extends Model<GlBehavior> {
    /**
    * 自增id
    */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;
    /** 减排行为数据的id */ 
    private String eventId;
    /** 行政区划 */ 
    private String region;
    /** 租户id */ 
    private Long tenantId;
    /** 应用id */ 
    private Integer appId;
    /** 场景id */ 
    private Integer scenarioId;
    /** 行为id */ 
    private Integer behaviorId;
    /** 手机号 */ 
    private String mobileSha256;
    /** 行为发生日期 */ 
    private Date date;
    /** 减排量 */ 
    private Double emission;
    /** 创建时间 */ 
    private Date created;
    /** 减排类型 0 大数据增量 1 绿色消费产生 */ 
    private Integer type;
    /** 删除 0未删除 1已删除 */
    private Integer deleted;
}


