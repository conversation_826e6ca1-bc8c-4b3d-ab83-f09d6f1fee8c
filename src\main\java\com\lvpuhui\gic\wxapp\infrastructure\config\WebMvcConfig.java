package com.lvpuhui.gic.wxapp.infrastructure.config;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.lvpuhui.gic.wxapp.infrastructure.interceptor.AuthHandlerInterceptor;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.TimeZone;

/**
 * 跨域
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

	@Resource
	private AuthHandlerInterceptor authHandlerInterceptor;

	@Override
	public void addCorsMappings(CorsRegistry registry) {
		//跨域配置所有的域名ip都可以
		registry.addMapping("/**").allowedOrigins("*");
	}

	@Override
		public void addInterceptors(InterceptorRegistry registry) {
			// 前台登录用户拦截器
			registry.addInterceptor(authHandlerInterceptor).addPathPatterns("/**");
	}

	@Bean
	public Jackson2ObjectMapperBuilderCustomizer customizer() {
		return builder -> {
			// 全局配置序列化返回 JSON 处理
			JavaTimeModule javaTimeModule = new JavaTimeModule();
			javaTimeModule.addSerializer(Long.class, ToStringSerializer.instance);
			javaTimeModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
			javaTimeModule.addSerializer(BigInteger.class, ToStringSerializer.instance);
			javaTimeModule.addSerializer(BigDecimal.class, ToStringSerializer.instance);
			javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DatePattern.NORM_DATETIME_FORMATTER));
			javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DatePattern.NORM_DATETIME_FORMATTER));
			javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DatePattern.NORM_DATE_FORMATTER));
			javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DatePattern.NORM_DATE_FORMATTER));
			builder.modules(javaTimeModule);
			builder.timeZone(TimeZone.getDefault());
		};
	}
}
