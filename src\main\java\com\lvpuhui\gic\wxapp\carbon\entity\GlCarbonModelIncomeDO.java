package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 碳汇项目实例（用户申请）收入
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_model_income")
public class GlCarbonModelIncomeDO extends Model<GlCarbonModelIncomeDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * modelID
     */
    private Long modelId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 收益名称
     */
    private String name;

    /**
     * 预计收益金额-下一年度清零
     */
    private BigDecimal amount;

    /**
     * 累计收益金额
     */
    private BigDecimal accumulateAmount;

    /**
     * 可提现金额-下一年度清零
     */
    private BigDecimal remainAmount;

    /**
     * 累积提现金额
     */
    private BigDecimal accumulateTakingAmount;

    /**
     * 预计减排量-下一年度清零
     */
    private BigDecimal emission;

    /**
     * 累计减排量
     */
    private BigDecimal accumulateEmission;

    /**
     * 累积补贴-下一年度清零
     */
    private BigDecimal accumulateSubsidyAmount;

    /**
     * 累积奖惩-下一年度清零
     */
    private BigDecimal accumulateRewardAmount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    private Long updator;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
