package com.lvpuhui.gic.wxapp.other.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorService;
import com.lvpuhui.gic.wxapp.other.entity.GlConsumption;
import com.lvpuhui.gic.wxapp.other.entity.GlFile;
import com.lvpuhui.gic.wxapp.other.dao.GlConsumptionDao;
import com.lvpuhui.gic.wxapp.homepage.entity.GlCompany;
import com.lvpuhui.gic.wxapp.homepage.entity.GlCompanyProduct;
import com.lvpuhui.gic.wxapp.homepage.service.GlCompanyProductService;
import com.lvpuhui.gic.wxapp.homepage.service.GlCompanyService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.other.service.GlConsumptionService;
import com.lvpuhui.gic.wxapp.other.service.GlFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 绿色消费记录表 (来源对接企业)(GlConsumption)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 13:45:10
 */
@Service("glConsumptionService")
@Slf4j
public class GlConsumptionServiceImpl extends ServiceImpl<GlConsumptionDao, GlConsumption> implements GlConsumptionService {

    @Resource
    GlPointsService glPointsService;
    @Resource
    GlCompanyService glCompanyService;
    @Resource
    GlFileService glFileService;
    @Resource
    GlCompanyProductService glCompanyProductService;
    @Resource
    GlAppletConfigService glAppletConfigService;
    @Resource
    GlBehaviorService glBehaviorService;

    @Value("${scene-category.green-life}")
    private String greenLife;
    @Value("${scene-category.green-capital}")
    private String greenCapital;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int grant(Long fileId) {
        GlFile file = glFileService.getOne(new LambdaQueryWrapper<GlFile>()
                .eq(GlFile::getId,fileId)
                .eq(GlFile::getDeleted,0));
        if(file ==null){
            throw new GicWxAppException("文件为空");
        }
        if(file.getEmissionState() > 0 || file.getEmissionPointsState()>0 || file.getPointsState() >0){
            throw new GicWxAppException("文件记录已发放对应积分和减排量");
        }
        GlCompanyProduct companyProduct = glCompanyProductService.getOne(new LambdaQueryWrapper<GlCompanyProduct>()
                .eq(GlCompanyProduct::getCompanyId,file.getCompanyId())
                .eq(GlCompanyProduct::getDeleted,0)
                .orderByDesc(GlCompanyProduct::getId)
                .last("LIMIT 1"));
        GlCompany company = glCompanyService.getById(file.getCompanyId());
        if(company == null || companyProduct == null){
            throw new GicWxAppException("对应企业不存在 或未配置企业产品 :"+file.getCompanyId());
        }
        long categoryId = companyProduct.getCategoryId();
        // 非 25 26 的不处理
        if (!(categoryId == Long.parseLong(greenLife) || categoryId == Long.parseLong(greenCapital))){
            log.error("CompanyProduct categoryId  Fail "+companyProduct.getCategoryId());
            throw new GicWxAppException("该企业所属板块不支持绿色消费导入 板块id:"+companyProduct.getCategoryId());
        }
        QueryWrapper<GlConsumption> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id,mobile_sha256 mobileSha256,IFNULL(sum(product_price),0) as productPrice,DATE_FORMAT(sold_time,'%Y-%m-%d') sold_time")
                .eq("file_id", fileId)
                .eq("deleted",0)
                .groupBy("mobile_sha256,DATE_FORMAT(sold_time,'%Y-%m-%d')");
        List<Map<String, Object>> list = listMaps(queryWrapper);
        if(CollUtil.isEmpty(list)){
            throw new GicWxAppException("文件记录为空");
        }
        // 绿享生活版块
        if(categoryId == Long.parseLong(greenLife)){
             handleGreenLife(list,company);
        // 绿动京城版块
        }else if(categoryId == Long.parseLong(greenCapital)){
             handleGreenCapital(list,company);
        }
        // 生成绿色消费对应减排
        List<GlConsumption> consumptions = list(new LambdaQueryWrapper<GlConsumption>()
                .eq(GlConsumption::getFileId,fileId)
                .eq(GlConsumption::getDeleted,0));
        // 计算减排量 减排量对应积分
        glBehaviorService.handleConsumption(consumptions);
        // 更新file 状态
        file.setPointsState(1);
        file.setEmissionState(1);
        file.setEmissionPointsState(1);
        glFileService.update(new LambdaUpdateWrapper<GlFile>()
                .set(GlFile::getPointsState,file.getPointsState())
                .set(GlFile::getEmissionState,file.getEmissionState())
                .set(GlFile::getEmissionPointsState,file.getEmissionPointsState())
                .eq(GlFile::getId,file.getId()));
        // 更新 用户积分
        Set<String> set = list.stream().map(l-> l.get("mobileSha256").toString()).collect(Collectors.toSet());
        if(!CollUtil.isEmpty(set)){
            set.stream().forEach(s->{
                try {
                    glPointsService.calcUserPoints(s);
                } catch (Exception e) {
                    log.error(e.getMessage(),e);
                }
            });
        }
        return list.size();
    }

    /**
     * 处理绿享生活版块
     * @param list
     * @return
     */
    void handleGreenLife(List<Map<String, Object>> list,GlCompany company){
        list.stream().forEach(l->{
            double productPrice = Double.parseDouble(l.get("productPrice").toString());
            log.info("productPrice:{}",productPrice);
            double points =  0;
            if(productPrice>=2000 && productPrice<=5000){
                 points = NumberUtil.round(glAppletConfigService.getPoints(15),0).doubleValue();
            }else if(productPrice>5000 && productPrice<=10000){
                points = NumberUtil.round(glAppletConfigService.getPoints(20),0).doubleValue();
            }else if(productPrice>10000 && productPrice<=20000){
                points = NumberUtil.round(glAppletConfigService.getPoints(25),0).doubleValue();
            }else if(productPrice>20000 ){
                points = NumberUtil.round(glAppletConfigService.getPoints(30),0).doubleValue();
            }
            if(points>0){
                GlConsumption consumption = buildGlConsumption(l,"绿享生活家电消费积分",company);
                glPointsService.grantConsumptionPoints(consumption,points);
            }
        });
    }

    void handleGreenCapital(List<Map<String, Object>> list,GlCompany company){
        list.stream().forEach(l->{
            double productPrice = Double.parseDouble(l.get("productPrice").toString());
            double points =  0;
            if(productPrice<=200000){
                points = NumberUtil.round(glAppletConfigService.getPoints(20),0).doubleValue();
            }else if(productPrice>200000){
                points = NumberUtil.round(glAppletConfigService.getPoints(30),0).doubleValue();
            }
            if(points>0){
                GlConsumption consumption = buildGlConsumption(l,"绿动京城购车消费积分",company);
                glPointsService.grantConsumptionPoints(consumption,points);
            }
        });
    }

    GlConsumption buildGlConsumption(Map<String, Object> l, String productName,GlCompany company){
        GlConsumption consumption = new GlConsumption();
        consumption.setId(Long.parseLong(l.get("id").toString()));
        consumption.setProductName(productName);
        consumption.setOrderId("0");
        consumption.setMobileSha256(l.get("mobileSha256").toString().toLowerCase());
        consumption.setCompanyId(company.getId());
        consumption.setCompanyName(company.getName());
        return consumption;
    }


}