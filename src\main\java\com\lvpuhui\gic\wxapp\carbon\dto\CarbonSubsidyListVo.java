package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CarbonSubsidyListVo {

    private Long id;

    /**
     * 起始补贴量
     */

    @Schema(description = "起始补贴")
    private BigDecimal beginEmission;

    @Schema(description = "终止补贴")
    private BigDecimal endEmission;

    @Schema(description = "补贴名称")
    private String name;

    @Schema(description = "补贴限额")
    private BigDecimal subsidyQuota;

    @Schema(description = "补贴主体 0-按业主，1-按地")
    private Integer subject;

    @Schema(description = "补贴判断条件 0-按面积，1-案件排量")
    private Integer condition;

    @Schema(description = "补贴年度")
    private Integer year;

    /**
     * 补贴价格
     */
    @Schema(description = "补贴价格")
    private BigDecimal subsidyPrice;

}
