package com.lvpuhui.gic.wxapp.carbon.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.lvpuhui.gic.wxapp.carbon.dao.*;
import com.lvpuhui.gic.wxapp.carbon.dto.*;
import com.lvpuhui.gic.wxapp.carbon.entity.*;
import com.lvpuhui.gic.wxapp.carbon.enums.CarbonManagerStatus;
import com.lvpuhui.gic.wxapp.carbon.enums.SubsidyGearCondition;
import com.lvpuhui.gic.wxapp.carbon.enums.SubsidyGearSubject;
import com.lvpuhui.gic.wxapp.carbon.service.CarbonManagerService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.other.utils.AES256Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CarbonManagerServiceImpl implements CarbonManagerService {

    @Resource
    private GlCarbonModelIncomeDao glCarbonModelIncomeDao;

    @Resource
    private GlCarbonModelDao glCarbonModelDao;

    @Resource
    private GlCarbonModelOperationDao glCarbonModelOperationDao;

    @Resource
    private GlCarbonRankDao glCarbonRankDao;

    @Resource
    private GlCarbonModelIncomeDetailDao glCarbonModelIncomeDetailDao;

    @Resource
    private GlCarbonSubsidyGearDao glCarbonSubsidyGearDao;

    @Resource
    private GlCarbonEmissionFactorDao glCarbonEmissionFactorDao;

    @Resource
    private GlCarbonProjectDao glCarbonProjectDao;

    @Resource
    private GlCarbonModelPictureDao glCarbonModelPictureDao;

    @Resource
    private AES256Util aes256Util;






    @Override
    public CarbonManagerVo carbonManager() {
        Long userId = UserUtils.getUserId();
        CarbonManagerVo carbonManagerVo = new CarbonManagerVo();
        BigDecimal countAmount = glCarbonModelIncomeDao.queryAllModel(userId);
        BigDecimal remainAmount = glCarbonModelIncomeDao.queryAllRemainByUserId(userId);
        carbonManagerVo.setAmount(countAmount);
        carbonManagerVo.setRemainAmount(remainAmount);
        GlCarbonRankDO glCarbonRankDO = glCarbonRankDao.selectByUserId(userId);
        if(Objects.nonNull(glCarbonRankDO)){
            carbonManagerVo.setRank(glCarbonRankDO.getRank());
        }
        List<CarbonModelMessgae> list = glCarbonModelDao.selectByUserId(userId);
        if (list.size() > 0){
            for (CarbonModelMessgae item:list) {
                BigDecimal reward = glCarbonModelIncomeDetailDao.sumReward(item.getModelId());
                item.setRewardAmount(reward);
                BigDecimal pun = glCarbonModelIncomeDetailDao.punAmount(item.getModelId());
                item.setPunAmount(pun);
            }
        }
        carbonManagerVo.setMeList(list);
        return carbonManagerVo;
    }


    public long getBeginTimeToYearEndDays(LocalDateTime beginTime){
        if(beginTime.getYear() > DateUtil.thisYear()){
            return 0;
        }
        if(beginTime.getYear() < DateUtil.thisYear()){
            // 如果给定的时间不是今年
            return DateUtil.lengthOfYear(DateUtil.thisYear());
        }
        // 获取本年最后一天
        LocalDateTime lastDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);
        // 获取审核时间和本年最后一天想差天数
        return ChronoUnit.DAYS.between(beginTime,lastDayOfYear);
    }

    @Override
    public CarbonProjectIncomeVo carbonProjectIncome() {
        //获取当前年份有多少天
        int days = DateUtil.lengthOfYear(DateUtil.year(new Date()));
        //获取当前年份
        int year = DateUtil.year(new Date());
        BigDecimal totalEmissionThisYear = null;
        Long userId = UserUtils.getUserId();
        CarbonProjectIncomeVo carbonProjectIncomeVo = new CarbonProjectIncomeVo();
        List<CarbonModelIncome> listProject = new ArrayList<>();
        //今年预计收入、预计减排量、预计今年奖惩
        CarbonManagrModel carbonManagrModel = glCarbonModelIncomeDao.sumData(userId);
        if(Objects.isNull(carbonManagrModel)){
            return null;
        }
        carbonProjectIncomeVo.setAmount(carbonManagrModel.getModelAmount());
        carbonProjectIncomeVo.setEmission(carbonManagrModel.getModelEmission());
        carbonProjectIncomeVo.setRewordAmount(carbonManagrModel.getRewardPunAmount());
        List<GlCarbonSubsidyGearDO> subsidyList = glCarbonSubsidyGearDao.selectByYear(year);
        //计算补贴均价
        if(subsidyList.size() > 0){
            int condition = subsidyList.get(0).getCondition();
            int subject = subsidyList.get(0).getSubject();
            //按面积
            if(condition == SubsidyGearCondition.AREA.getCode()){
                carbonProjectIncomeVo.setSingleName("补贴价格 (元/亩)");
                //查询出所有正常的model
                List<GlCarbonModelDO> modelListArea = glCarbonModelDao.selectByArea(userId);
                for (GlCarbonModelDO model:modelListArea) {
                    //保存每个项目实例信息
                    CarbonModelIncome carbonModelIncome = new CarbonModelIncome();
                    GlCarbonModelIncomeDO income = glCarbonModelIncomeDao.selectByModelId(model.getId());
                    carbonModelIncome.setModelAmount(income.getAmount());
                    carbonModelIncome.setProjectId(model.getProjectId());
                    GlCarbonProjectDO projectDO = glCarbonProjectDao.selectById(model.getProjectId());
                    carbonModelIncome.setProjectName(projectDO.getName());
                    carbonModelIncome.setModelId(model.getId());
                    carbonModelIncome.setModelName(model.getName());
                    carbonModelIncome.setArea(model.getArea());
                    carbonModelIncome.setEmission(income.getEmission());
                    carbonModelIncome.setRewordAmount(income.getAccumulateRewardAmount());
                    carbonModelIncome.setTotalEmission(model.getArea());
                    for (GlCarbonSubsidyGearDO subsidy: subsidyList) {
                        BigDecimal startData = subsidy.getBeginEmission();
                        BigDecimal endData = subsidy.getEndEmission();
                        int startIndex = model.getArea().compareTo(startData);
                        int endIndex = model.getArea().compareTo(endData);
                        int maxIndex = endData.compareTo(new BigDecimal("0.00"));
                        if (startIndex >= 0 && (endIndex < 0 || maxIndex == 0)) {
                            BigDecimal singePrice = model.getArea().multiply(subsidy.getSubsidyPrice());
                            int index = singePrice.compareTo(subsidy.getSubsidyQuota());
                            if(index < 0){
                                carbonModelIncome.setTotalPrice(singePrice);
                            }else {
                                carbonModelIncome.setTotalPrice(subsidy.getSubsidyQuota());
                            }
                            carbonModelIncome.setSubsidyPrice(subsidy.getSubsidyPrice());
                            carbonModelIncome.setBeginEmission(subsidy.getBeginEmission());
                            carbonModelIncome.setEndEmission(subsidy.getEndEmission());
                            carbonModelIncome.setSubject(subsidy.getSubject());
                            carbonModelIncome.setYear(subsidy.getYear());
                            carbonModelIncome.setCondition(subsidy.getCondition());
                            carbonModelIncome.setSubsidyQuota(subsidy.getSubsidyQuota());
                        }
                    }
                    listProject.add(carbonModelIncome);
                }
                //判断补贴条件是否按人
                if(SubsidyGearSubject.PEOPLE.getCode().equals(subject)){
                    BigDecimal totalPeopleEmission = listProject.stream().map(CarbonModelIncome :: getTotalEmission).reduce(BigDecimal.ZERO ,BigDecimal::add);
                    //找出总减排量对应的补贴档位就是平均补贴价格
                    for (GlCarbonSubsidyGearDO subsidy: subsidyList) {
                        BigDecimal startData = subsidy.getBeginEmission();
                        BigDecimal endData = subsidy.getEndEmission();
                        int startIndex = totalPeopleEmission.compareTo(startData);
                        int endIndex = totalPeopleEmission.compareTo(endData);
                        int maxIndex = endData.compareTo(new BigDecimal("0.00"));
                        if (startIndex >= 0 && (endIndex < 0 || maxIndex == 0)) {
                            BigDecimal singePrice = totalPeopleEmission.multiply(subsidy.getSubsidyPrice());
                            int index = singePrice.compareTo(subsidy.getSubsidyQuota());
                            if (index < 0) {
                                //如果补贴钱小于补贴限额，那么平均价格就是当前补贴档位的补贴价格
                                carbonProjectIncomeVo.setSubsidyPrice(subsidy.getSubsidyPrice());
                            } else {
                                //如果补贴钱大于补贴限额，那么平均价格就是补贴限额的钱除以总亩数
                                BigDecimal avgPrice = subsidy.getSubsidyQuota().divide(totalPeopleEmission,2,BigDecimal.ROUND_HALF_UP);
                                carbonProjectIncomeVo.setSubsidyPrice(avgPrice);
                            }
                        }
                    }
                    for (CarbonModelIncome list:listProject) {
                        list.setSubsidyPrice(carbonProjectIncomeVo.getSubsidyPrice());
                    }
                }else {
                    //按地
                    BigDecimal totalEmission = listProject.stream().map(CarbonModelIncome :: getTotalEmission).reduce(BigDecimal.ZERO ,BigDecimal::add);
                    BigDecimal totalAmount = listProject.stream().map(CarbonModelIncome :: getTotalPrice).reduce(BigDecimal.ZERO ,BigDecimal::add);
                    BigDecimal countPrice = totalAmount.divide(totalEmission,2, BigDecimal.ROUND_HALF_UP);
                    carbonProjectIncomeVo.setSubsidyPrice(countPrice);
                }
            }else {
                carbonProjectIncomeVo.setSingleName("补贴价格 (元/吨)");
                //查询所有model信息
                List<GlCarbonModelDO> modelList = glCarbonModelDao.selectAll(userId);
                for (GlCarbonModelDO model:modelList) {
                    //保存每个项目实例信息
                    CarbonModelIncome carbonModelIncome = new CarbonModelIncome();
                    GlCarbonModelIncomeDO income = glCarbonModelIncomeDao.selectByModelId(model.getId());
                    carbonModelIncome.setModelAmount(income.getAmount());
                    carbonModelIncome.setProjectId(model.getProjectId());
                    GlCarbonProjectDO projectDO = glCarbonProjectDao.selectById(model.getProjectId());
                    carbonModelIncome.setProjectName(projectDO.getName());
                    carbonModelIncome.setModelId(model.getId());
                    carbonModelIncome.setModelName(model.getName());
                    carbonModelIncome.setArea(model.getArea());
                    carbonModelIncome.setEmission(income.getEmission());
                    carbonModelIncome.setRewordAmount(income.getAccumulateRewardAmount());
                    if(Objects.nonNull(model.getFactorId())){
                        GlCarbonEmissionFactorDO factorDO = glCarbonEmissionFactorDao.selectById(model.getFactorId());
                        //如果model为停用、年审中、审核不通过，则计算截止日期之前的总减排量
                        if(model.getStatus().equals(CarbonManagerStatus.STOP.getCode()) ||
                                model.getStatus().equals(CarbonManagerStatus.YEAR_AUDIT.getCode())
                                ||model.getStatus().equals(CarbonManagerStatus.NO_PASS.getCode())){
                            //查出预计减排量
                            BigDecimal emission = income.getEmission().divide(new BigDecimal("1000000"),6,BigDecimal.ROUND_HALF_EVEN);
                            totalEmissionThisYear = Objects.isNull(emission)?BigDecimal.ZERO:emission;
                        }else {
                            //计算今年剩余天数
                            long residueDay = getBeginTimeToYearEndDays(model.getAudited());
                            BigDecimal tolalEmission365 = model.getArea().multiply(factorDO.getFactor());
                            BigDecimal emissionEveryDay = tolalEmission365.divide(new BigDecimal(String.valueOf(days)),6,BigDecimal.ROUND_HALF_EVEN);
                            //得到剩余有效天数的总吨数
                            BigDecimal emission = emissionEveryDay.multiply(new BigDecimal(String.valueOf(residueDay)));
                            totalEmissionThisYear = Objects.isNull(emission)?BigDecimal.ZERO:emission;
                        }
                        for (GlCarbonSubsidyGearDO subsidy: subsidyList) {
                            BigDecimal startData = subsidy.getBeginEmission();
                            BigDecimal endData = subsidy.getEndEmission();
                            int startIndex = totalEmissionThisYear.compareTo(startData);
                            int endIndex = totalEmissionThisYear.compareTo(endData);
                            int maxIndex = endData.compareTo(new BigDecimal("0.00"));
                                if (startIndex >= 0 && (endIndex < 0 || maxIndex == 0)) {
                                BigDecimal singePrice = totalEmissionThisYear.multiply(subsidy.getSubsidyPrice());
                                    carbonModelIncome.setTotalEmission(totalEmissionThisYear);
                                    //如果单个碳汇项目的有效吨数大于补贴限额，那么单个model的最终收入为补贴限额价钱
                                    int index = singePrice.compareTo(subsidy.getSubsidyQuota());
                                    if(index < 0){
                                        carbonModelIncome.setTotalPrice(singePrice);
                                    }else {
                                        carbonModelIncome.setTotalPrice(subsidy.getSubsidyQuota());
                                    }
                                    carbonModelIncome.setSubsidyPrice(subsidy.getSubsidyPrice());
                                    carbonModelIncome.setBeginEmission(subsidy.getBeginEmission());
                                    carbonModelIncome.setEndEmission(subsidy.getEndEmission());
                                    carbonModelIncome.setSubject(subsidy.getSubject());
                                    carbonModelIncome.setYear(subsidy.getYear());
                                    carbonModelIncome.setCondition(subsidy.getCondition());
                                    carbonModelIncome.setSubsidyQuota(subsidy.getSubsidyQuota());
                                    log.info("单个model总价钱"+carbonModelIncome.getTotalPrice());
                                    log.info("单个model总减排量"+carbonModelIncome.getTotalEmission());
                                }
                        }
                        listProject.add(carbonModelIncome);
                    }
                }
                if(SubsidyGearSubject.PEOPLE.getCode().equals(subject)){
                    BigDecimal totalPeopleEmission = listProject.stream().map(CarbonModelIncome :: getTotalEmission).reduce(BigDecimal.ZERO ,BigDecimal::add);
                    //找出总减排量对应的补贴档位就是平均补贴价格
                    for (GlCarbonSubsidyGearDO subsidy: subsidyList) {
                        BigDecimal startData = subsidy.getBeginEmission();
                        BigDecimal endData = subsidy.getEndEmission();
                        int startIndex = totalPeopleEmission.compareTo(startData);
                        int endIndex = totalPeopleEmission.compareTo(endData);
                        int maxIndex = endData.compareTo(new BigDecimal("0.00"));
                        if (startIndex >= 0 && (endIndex < 0 || maxIndex == 0)) {
                            BigDecimal singePrice = totalPeopleEmission.multiply(subsidy.getSubsidyPrice());
                            int index = singePrice.compareTo(subsidy.getSubsidyQuota());
                            if (index < 0) {
                                //如果补贴钱小于补贴限额，那么平均价格就是当前补贴档位的补贴价格
                                carbonProjectIncomeVo.setSubsidyPrice(subsidy.getSubsidyPrice());
                            } else {
                                //如果补贴钱大于补贴限额，那么平均价格就是补贴限额的钱除以总亩数
                                BigDecimal avgPrice = subsidy.getSubsidyQuota().divide(totalPeopleEmission,2,BigDecimal.ROUND_HALF_UP);
                                carbonProjectIncomeVo.setSubsidyPrice(avgPrice);
                            }
                        }
                    }
                    for (CarbonModelIncome list:listProject) {
                        list.setSubsidyPrice(carbonProjectIncomeVo.getSubsidyPrice());
                    }
                }else {
                    //按地
                    BigDecimal totalEmission = listProject.stream().map(CarbonModelIncome :: getTotalEmission).reduce(BigDecimal.ZERO ,BigDecimal::add);
                    BigDecimal totalAmount = listProject.stream().map(CarbonModelIncome :: getTotalPrice).reduce(BigDecimal.ZERO ,BigDecimal::add);
                    int index = totalEmission.compareTo(new BigDecimal("0.00"));
                    if(index > 0){
                        BigDecimal countPrice = totalAmount.divide(totalEmission,2, BigDecimal.ROUND_CEILING);
                        carbonProjectIncomeVo.setSubsidyPrice(countPrice);
                    }else {
                        carbonProjectIncomeVo.setSubsidyPrice(new BigDecimal("0.00"));
                    }
                }
            }
        }
        carbonProjectIncomeVo.setProjectList(listProject);
        log.info("平均补贴价格为{}",carbonProjectIncomeVo.getSubsidyPrice());
        return carbonProjectIncomeVo;
    }

    @Override
    public CarbonDetailsVo carbonDetails(Long modelId) {
        //获取当前年份
        int year = DateUtil.year(new Date());
        int days = DateUtil.lengthOfYear(DateUtil.thisYear());
        CarbonDetailsVo carbonDetailsVoc = new CarbonDetailsVo();

        GlCarbonModelIncomeDO income = glCarbonModelIncomeDao.selectByModelId(modelId);
        GlCarbonModelDO model = glCarbonModelDao.selectById(modelId);
        carbonDetailsVoc.setArea(model.getArea());
        carbonDetailsVoc.setEmission(income.getEmission());
        carbonDetailsVoc.setAmount(income.getAmount());
        //累计提现金额为 可提现金额-下一年度清零+累计提现金额
        carbonDetailsVoc.setRemainAmount(income.getRemainAmount().add(income.getAccumulateTakingAmount()));
        carbonDetailsVoc.setRewordAmount(income.getAccumulateRewardAmount());
        List<GlCarbonSubsidyGearDO> subsidyList = glCarbonSubsidyGearDao.selectByYear(year);
        //如果今年创建了补贴档位那么正常保存
        if(subsidyList.size() > 0){
            int subject = subsidyList.get(0).getSubject();
            int condition = subsidyList.get(0).getCondition();
            //如果补贴档位按人
            if(SubsidyGearSubject.PEOPLE.getCode().equals(subject)){
                //按面积
                if(SubsidyGearCondition.AREA.getCode().equals(condition)){
                    BigDecimal totalAreaData = glCarbonModelDao.sumAreaByUser(model.getUserId());
                    BigDecimal totalArea = Objects.isNull(totalAreaData)?BigDecimal.ZERO:totalAreaData;
                    for (GlCarbonSubsidyGearDO subItem: subsidyList) {
                        BigDecimal startData = subItem.getBeginEmission();
                        BigDecimal endData = subItem.getEndEmission();
                        int startIndex = totalArea.compareTo(startData);
                        int endIndex = totalArea.compareTo(endData);
                        int maxIndex = endData.compareTo(new BigDecimal("0.00"));
                        if (startIndex >= 0 && (endIndex < 0 || maxIndex == 0)) {
                            BigDecimal singePrice = totalArea.multiply(subItem.getSubsidyPrice());
                            int index = singePrice.compareTo(subItem.getSubsidyQuota());
                            if (index < 0) {
                                //如果补贴钱小于补贴限额，那么平均价格就是当前补贴档位的补贴价格
                                carbonDetailsVoc.setSubsidyPrice(subItem.getSubsidyPrice());
                                carbonDetailsVoc.setSubsidySingle("元/亩");
                            } else {
                                //如果补贴钱大于补贴限额，那么平均价格就是补贴限额的钱除以总亩数
                                BigDecimal avgPrice = subItem.getSubsidyQuota().divide(totalArea,2,BigDecimal.ROUND_HALF_UP);
                                carbonDetailsVoc.setSubsidyPrice(avgPrice);
                                carbonDetailsVoc.setSubsidySingle("元/亩");
                            }
                        }
                    }
                }else {
                    //按减排量
                    Map<Long,BigDecimal> emissionMap = new HashMap<>();
                    List<GlCarbonModelDO> listModel = glCarbonModelDao.selectByUserIdTotalEmission(model.getUserId());
                    for (GlCarbonModelDO modelItem:listModel) {
                        //查询减排因子
                        if(Objects.nonNull(modelItem.getFactorId())){
                            GlCarbonEmissionFactorDO factorDO = glCarbonEmissionFactorDao.selectById(modelItem.getFactorId());
                            BigDecimal factor = factorDO.getFactor();
                            //查询明细表
                            GlCarbonModelIncomeDO incomeDO = glCarbonModelIncomeDao.selectByModelId(modelItem.getId());
                            BigDecimal totalEmissionThisYear1 = null;
                            //如果是正常的碳汇model
                            if(modelItem.getStatus().equals(CarbonManagerStatus.PASS.getCode())){
                                //计算今年剩余天数
                                long residueDay = getBeginTimeToYearEndDays(modelItem.getAudited());
                                BigDecimal tolalEmission365 = modelItem.getArea().multiply(factor);
                                BigDecimal emissionEveryDay = tolalEmission365.divide(new BigDecimal(String.valueOf(days)),6,BigDecimal.ROUND_HALF_EVEN);
                                //得到剩余有效天数的总吨数
                                totalEmissionThisYear1 = emissionEveryDay.multiply(new BigDecimal(String.valueOf(residueDay)));

                            }else {
                                //年审中、停用、审核未通过
                                totalEmissionThisYear1 = incomeDO.getEmission().divide(new BigDecimal("1000000"),6,BigDecimal.ROUND_HALF_EVEN);
                            }
                            if(!emissionMap.containsKey(modelItem.getUserId())){
                                emissionMap.put(modelItem.getUserId(),totalEmissionThisYear1);
                            }else {
                                //包含的话将同一个userid的减排量累加
                                BigDecimal beforeEmission = emissionMap.get(modelItem.getUserId());
                                BigDecimal afterEmission = beforeEmission.add(totalEmissionThisYear1);
                                emissionMap.put(modelItem.getUserId(),afterEmission);
                            }
                            for (GlCarbonSubsidyGearDO subItem: subsidyList) {
                                BigDecimal startData = subItem.getBeginEmission();
                                BigDecimal endData = subItem.getEndEmission();
                                int startIndex = emissionMap.get(model.getUserId()).compareTo(startData);
                                int endIndex = emissionMap.get(model.getUserId()).compareTo(endData);
                                int maxIndex = endData.compareTo(new BigDecimal("0.00"));
                                if (startIndex >= 0 && (endIndex < 0 || maxIndex == 0)) {
                                    BigDecimal singePrice = emissionMap.get(model.getUserId()).multiply(subItem.getSubsidyPrice());
                                    int index = singePrice.compareTo(subItem.getSubsidyQuota());
                                    if (index < 0) {
                                        //如果补贴钱小于补贴限额，那么平均价格就是当前补贴档位的补贴价格
                                        carbonDetailsVoc.setSubsidyPrice(subItem.getSubsidyPrice());
                                        carbonDetailsVoc.setSubsidySingle("元/吨");
                                    } else {
                                        //如果碳汇项目的累计减排量为0，为了避免报错，那么补贴价格设置为0
                                        int zeroTotalEmission  = emissionMap.get(model.getUserId()).compareTo(new BigDecimal("0.00"));
                                        if(zeroTotalEmission == 0){
                                            carbonDetailsVoc.setSubsidyPrice(new BigDecimal("0.00"));
                                            carbonDetailsVoc.setSubsidySingle("元/吨");
                                        }else {
                                            //如果补贴钱大于补贴限额，那么平均价格就是补贴限额的钱除以总亩数
                                            BigDecimal avgPrice = subItem.getSubsidyQuota().divide(emissionMap.get(model.getUserId()),2,BigDecimal.ROUND_HALF_UP);
                                            carbonDetailsVoc.setSubsidyPrice(avgPrice);
                                            carbonDetailsVoc.setSubsidySingle("元/吨");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }else {
                //按面积分
                if(condition == SubsidyGearCondition.AREA.getCode()){
                    for (GlCarbonSubsidyGearDO subItem: subsidyList) {
                        BigDecimal startData = subItem.getBeginEmission();
                        BigDecimal endData = subItem.getEndEmission();
                        int startIndex = model.getArea().compareTo(startData);
                        int endIndex = model.getArea().compareTo(endData);
                        int maxIndex = endData.compareTo(new BigDecimal("0.00"));
                        if (startIndex >= 0 && (endIndex < 0 || maxIndex == 0)) {
                            BigDecimal singePrice = model.getArea().multiply(subItem.getSubsidyPrice());
                            int index = singePrice.compareTo(subItem.getSubsidyQuota());
                            if (index < 0) {
                                //如果补贴钱小于补贴限额，那么平均价格就是当前补贴档位的补贴价格
                                carbonDetailsVoc.setSubsidyPrice(subItem.getSubsidyPrice());
                                carbonDetailsVoc.setSubsidySingle("元/亩");
                            } else {
                                //如果补贴钱大于补贴限额，那么平均价格就是补贴限额的钱除以总亩数
                                BigDecimal avgPrice = subItem.getSubsidyQuota().divide(model.getArea(),2,BigDecimal.ROUND_HALF_UP);
                                carbonDetailsVoc.setSubsidyPrice(avgPrice);
                                carbonDetailsVoc.setSubsidySingle("元/亩");
                            }
                        }
                    }
                }else {
                    //如果预计减排量为空而且今年的补贴档位是按面积分，那么补贴档位不显示
                    if(Objects.nonNull(model.getFactorId())){
                        GlCarbonEmissionFactorDO factor = glCarbonEmissionFactorDao.selectById(model.getFactorId());
                        BigDecimal totalEmissionThisYear1 = null;
                        //如果是正常的碳汇model
                        if(model.getStatus().equals(CarbonManagerStatus.PASS.getCode())){
                            //计算今年剩余天数
                            long residueDay = getBeginTimeToYearEndDays(model.getAudited());
                            BigDecimal tolalEmission365 = model.getArea().multiply(factor.getFactor());
                            BigDecimal emissionEveryDay = tolalEmission365.divide(new BigDecimal(String.valueOf(days)),6,BigDecimal.ROUND_HALF_EVEN);
                            //得到剩余有效天数的总吨数
                            totalEmissionThisYear1 = emissionEveryDay.multiply(new BigDecimal(String.valueOf(residueDay)));

                        }else {
                            //年审中、停用、审核未通过
                            totalEmissionThisYear1 = income.getEmission().divide(new BigDecimal("1000000"),6,BigDecimal.ROUND_HALF_EVEN);
                        }
                        for (GlCarbonSubsidyGearDO subItem: subsidyList) {
                            BigDecimal startData = subItem.getBeginEmission();
                            BigDecimal endData = subItem.getEndEmission();
                            int startIndex = totalEmissionThisYear1.compareTo(startData);
                            int endIndex = totalEmissionThisYear1.compareTo(endData);
                            int maxIndex = endData.compareTo(new BigDecimal("0.00"));
                            if (startIndex >= 0 && (endIndex < 0 || maxIndex == 0)) {
                                BigDecimal singePrice = totalEmissionThisYear1.multiply(subItem.getSubsidyPrice());
                                int index = singePrice.compareTo(subItem.getSubsidyQuota());
                                if (index < 0) {
                                    //如果补贴钱小于补贴限额，那么平均价格就是当前补贴档位的补贴价格
                                    carbonDetailsVoc.setSubsidyPrice(subItem.getSubsidyPrice());
                                    carbonDetailsVoc.setSubsidySingle("元/吨");
                                } else {
                                    //如果碳汇项目的累计减排量为0，为了避免报错，那么补贴价格设置为0
                                    int zeroTotalEmission  = totalEmissionThisYear1.compareTo(new BigDecimal("0.00"));
                                    if(zeroTotalEmission == 0){
                                        carbonDetailsVoc.setSubsidyPrice(new BigDecimal("0.00"));
                                        carbonDetailsVoc.setSubsidySingle("元/吨");
                                    }else {
                                        //如果补贴钱大于补贴限额，那么平均价格就是补贴限额的钱除以总亩数
                                        BigDecimal avgPrice = subItem.getSubsidyQuota().divide(totalEmissionThisYear1,2,BigDecimal.ROUND_HALF_UP);
                                        carbonDetailsVoc.setSubsidyPrice(avgPrice);
                                        carbonDetailsVoc.setSubsidySingle("元/吨");
                                    }
                                }
                            }
                        }
                    }
                }

            }
        }
        //查询日志
        List<OperationVo> logList = glCarbonModelOperationDao.queryAllLogByModelId(modelId);
        if(logList.size() > 0){
            carbonDetailsVoc.setLogList(logList);
        }
        return carbonDetailsVoc;
    }

    @Override
    public IncomeDayVo inComeDayDetails(Long modelId) {

        IncomeDayVo incomeDayVo = new IncomeDayVo();
        GlCarbonModelIncomeDO income = glCarbonModelIncomeDao.selectByModelId(modelId);
        incomeDayVo.setAmount(income.getAmount());
        incomeDayVo.setAccumulateAmount(income.getAccumulateAmount());
        LocalDate dateTime = LocalDate.now();
        dateTime = dateTime.minus(90, ChronoUnit.DAYS);
        List<InComeDetails> list = glCarbonModelIncomeDetailDao.selectByModelId(modelId,dateTime);
        incomeDayVo.setDayList(list);
        return incomeDayVo;
    }

    @Override
    public BigDecimal getCarbonEveryReward() {

        String yesterday = DateUtil.offsetDay(new DateTime(), -1).toString(DatePattern.NORM_DATE_PATTERN);
        Long userId = UserUtils.getUserId();
        return glCarbonModelIncomeDetailDao.getCarbonEveryReward(yesterday,userId);
    }

    @Override
    public BigDecimal getCarbonEveryModelReward(Long modelId) {
        String yesterday = DateUtil.offsetDay(new DateTime(), -1).toString(DatePattern.NORM_DATE_PATTERN);
        Long userId = UserUtils.getUserId();
        return glCarbonModelIncomeDetailDao.getCarbonEveryModelReward(yesterday,modelId,userId);
    }

    private String calculationModelName(long size,String projectName){
        String str = String.valueOf(size+1);
        String number = StrUtil.fillBefore(str, '0',6);
        return projectName+ " "+number;

    }


    @Override
    public void saveCarbon(CarbonSaveDto carbonSaveDto) {

        long year = DateUtil.year(new Date());
        GlCarbonModelDO glCarbonModelDO = new GlCarbonModelDO();
        long size  = glCarbonModelDao.selectByProjectId(carbonSaveDto.getProjectId());
        GlCarbonProjectDO project = glCarbonProjectDao.selectById(carbonSaveDto.getProjectId());
        String modelName = calculationModelName(size,project.getName());
        glCarbonModelDO.setName(modelName);
        glCarbonModelDO.setOwner(carbonSaveDto.getName());
        glCarbonModelDO.setAddress(carbonSaveDto.getAddress());
        glCarbonModelDO.setYear(year);
        glCarbonModelDO.setCreated(LocalDateTime.now());
        glCarbonModelDO.setArea(carbonSaveDto.getArea());
        String cryptoCard = aes256Util.encrypt(carbonSaveDto.getCard());
        glCarbonModelDO.setCard(cryptoCard);
        glCarbonModelDO.setUserId(UserUtils.getUserId());
        glCarbonModelDO.setStatus(0);
        glCarbonModelDO.setYearStatus(0);
        String phone = UserUtils.getUser().getMobile();
        glCarbonModelDO.setMobile(phone);
        glCarbonModelDO.setProjectId(carbonSaveDto.getProjectId());
        glCarbonModelDao.insert(glCarbonModelDO);
        //保存model金额明细总表income
        Long modelId = glCarbonModelDO.getId();
        GlCarbonModelIncomeDO glCarbonModelIncomeDO = new GlCarbonModelIncomeDO();
        glCarbonModelIncomeDO.setModelId(modelId);
        glCarbonModelIncomeDO.setUserId(UserUtils.getUserId());
        glCarbonModelIncomeDO.setCreated(LocalDateTime.now());
        glCarbonModelIncomeDao.insert(glCarbonModelIncomeDO);
        //保存日志
        GlCarbonModelOperationDO glCarbonModelOperationDO = new GlCarbonModelOperationDO();
        glCarbonModelOperationDO.setCreated(LocalDateTime.now());
        glCarbonModelOperationDO.setModelId(modelId);
        glCarbonModelOperationDO.setUserId(UserUtils.getUserId());
        glCarbonModelOperationDO.setContent("碳汇项目待审核");
        glCarbonModelOperationDO.setType("用户提交");
        glCarbonModelOperationDO.setLog("碳汇项目待审核");
        glCarbonModelOperationDao.insert(glCarbonModelOperationDO);
        for (String urlItem:carbonSaveDto.getUrl()) {
            GlCarbonModelPictureDO glCarbonModelPictureDO = new GlCarbonModelPictureDO();
            glCarbonModelPictureDO.setModelId(modelId);
            glCarbonModelPictureDO.setCreated(LocalDateTime.now());
            glCarbonModelPictureDO.setCreator(UserUtils.getUserId());
            glCarbonModelPictureDO.setStatus(0);
            glCarbonModelPictureDO.setUserId(UserUtils.getUserId());
            glCarbonModelPictureDO.setUrl(urlItem);
            glCarbonModelPictureDao.insert(glCarbonModelPictureDO);
        }


    }

    @Override
    public List<CarbonTypeListVo> getProjectList() {
        return glCarbonProjectDao.getProjectList();
    }



    @Override
    public IncomeDayVo allInComeDayDetails() {

        Long userId = UserUtils.getUserId();
        IncomeDayVo incomeDayVo = new IncomeDayVo();
        CarbonModelAmount carbonModelAmount = glCarbonModelIncomeDao.sumAllAmount(userId);
        incomeDayVo.setAmount(carbonModelAmount.getAmount());
        incomeDayVo.setAccumulateAmount(carbonModelAmount.getAccumulateAmount());
        LocalDate dateTime = LocalDate.now();
        dateTime = dateTime.minus(90, ChronoUnit.DAYS);
        List<InComeDetails> detailsList = glCarbonModelIncomeDetailDao.selectByUserId(userId,dateTime);
        incomeDayVo.setDayList(detailsList);
        return incomeDayVo;
    }




    @Override
    public List<CarbonSubsidyListVo> getAllSubsidyList() {
        int year = DateUtil.year(new Date());
        return glCarbonSubsidyGearDao.selectAll(year);
    }

    private void saveLogOperation(String log,String appLog,String content,Long userId,Long modelId){
        //记录日志
        GlCarbonModelOperationDO glCarbonModelOperationDO = new GlCarbonModelOperationDO();
        glCarbonModelOperationDO.setCreated(LocalDateTime.now());
        glCarbonModelOperationDO.setModelId(modelId);
        glCarbonModelOperationDO.setUserId(userId);
        glCarbonModelOperationDO.setType("系统操作");
        glCarbonModelOperationDO.setContent(content);
        glCarbonModelOperationDO.setLog(log);
        if(!"".equals(appLog)){
            glCarbonModelOperationDO.setAppLog(appLog);
        }
        glCarbonModelOperationDO.setStatus(0);
        glCarbonModelOperationDao.insert(glCarbonModelOperationDO);

    }
}
