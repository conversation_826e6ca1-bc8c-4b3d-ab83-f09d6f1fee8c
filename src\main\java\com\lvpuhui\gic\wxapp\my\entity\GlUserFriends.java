package com.lvpuhui.gic.wxapp.my.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 用户好友表(gl_user_friends)实体类
 * <AUTHOR>
 * @since 2022-07-26
 */
@Data
@Accessors(chain = true)
public class GlUserFriends extends Model<GlUserFriends> {

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.ID_WORKER)
    private Long id;

    /**
     * 手机号sha256
     */
    private String mobileSha256;

    /**
     * 好友手机号sha256
     */
    private String friendsSha256;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 是否删除 0否 1是
     */
    private Integer deleted;
}
