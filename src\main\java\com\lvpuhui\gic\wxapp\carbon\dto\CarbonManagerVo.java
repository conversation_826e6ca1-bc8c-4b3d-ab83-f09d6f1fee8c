package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "碳汇管理返回值")
public class CarbonManagerVo {


    /**
     * 今年预计收益金额
     */
    private BigDecimal amount;

    /**
     * 我得排名
     */

    private Long rank;

    /**
     * 可提现金额
     */

    private BigDecimal remainAmount;


    /**
     * model信息
     */

    private List<CarbonModelMessgae> meList;








}
