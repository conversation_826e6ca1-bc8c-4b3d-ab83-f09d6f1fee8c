package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 举报表媒体资源
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_report_media")
public class GlReportMediaDO extends Model<GlReportMediaDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 举报Id
     */
    private Long reportId;

    /**
     * 资源类型 0：图片 1：视频
     */
    private Integer mediaType;

    /**
     * 资源地址
     */
    private String mediaUrl;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 更新时间
     */
    private LocalDateTime updated;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
