package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;

/**
 * 排行配置dto
 * <AUTHOR>
 * @since 2022年07月26日 11:02:00
 */
@Data
public class RankConfigDto {

    /**
     * 昨日排行
     */
    private YesterdayBean yesterday;

    /**
     * 邀请设置
     */
    private InvitationBean invitation;

    /**
     * 累计排行
     */
    private CumulativeBean cumulative;

    @Data
    public static class YesterdayBean {

        /**
         * 需要的上榜天数
         */
        private Integer days;

        /**
         * 可领取的商品ID
         */
        private String commodityId;

        /**
         * 截止日期
         */
        private String endTime;
    }

    @Data
    public static class InvitationBean {

        /**
         * 积分上限
         */
        private Double maximumPoints;

        /**
         * 邀请获得积分
         */
        private Double points;
    }

    @Data
    public static class CumulativeBean {

        /**
         * 排行前几名
         */
        private Integer top;

        /**
         * 截止日期
         */
        private String endTime;

        /**
         * 领取商品ID
         */
        private String commodityId;
    }
}
