package com.lvpuhui.gic.wxapp.base.service.impl;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lvpuhui.gic.wxapp.base.dao.GlUploadFileDao;
import com.lvpuhui.gic.wxapp.base.dto.AttachmentVo;
import com.lvpuhui.gic.wxapp.base.entity.GlUploadFileDO;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.base.service.GlUploadFileService;
import com.lvpuhui.gic.wxapp.base.storage.StorageServiceFactory;
import com.lvpuhui.gic.wxapp.base.utils.ContentCheckUtils;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.ImageUtils;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023年03月17日 14:56:00
 */
@Slf4j
@Service
public class GlUploadFileServiceImpl implements GlUploadFileService {

    @Resource
    private StorageServiceFactory storageServiceFactory;

    @Resource
    private GlUploadFileDao glUploadFileDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Resource
    private ContentCheckUtils contentCheckUtils;

    @Override
    public AttachmentVo upload(MultipartFile file,boolean contentCheck) {
        if(Objects.isNull(file) || file.isEmpty()){
            throw new GicWxAppException("请上传有效文件");
        }
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        try (InputStream inputStream = file.getInputStream()){
            boolean imageFile = ImageUtils.isImageFile(file.getInputStream());
            if(!imageFile){
                throw new GicWxAppException("请上传图片");
            }
            int fileSize = (int)file.getSize();
            String extName = FileUtil.extName(file.getOriginalFilename());
            String fileName = storageServiceFactory.getStorageService().uploadStream(inputStream,IdUtil.fastSimpleUUID() + "." + extName);
            if (StrUtil.isBlank(fileName)){
                throw new GicWxAppException("上传失败，请您重试！");
            }
            String imagePrefix = glAppletConfigService.getImagePrefix();
            GlUploadFileDO glUploadFileDO = new GlUploadFileDO();
            glUploadFileDO.setFileName(fileName);
            glUploadFileDO.setFileSize(fileSize);
            glUploadFileDO.setCreated(LocalDateTime.now());
            if(UserUtils.userIsAuth()){
                glUploadFileDO.setCreator(UserUtils.getUserId());
            }else {
                glUploadFileDO.setCreator(0L);
            }
            glUploadFileDO.setIp(ServletUtil.getClientIP(request));
            glUploadFileDao.insert(glUploadFileDO);

            if(contentCheck){
                boolean checkImage = contentCheckUtils.checkImage(imagePrefix + fileName);
                if(!checkImage){
                    storageServiceFactory.getStorageService().deleteFile(fileName);
                    throw new GicWxAppException("您上传的图片不符合规范，请重新上传");
                }
            }

            AttachmentVo attachment = new AttachmentVo();
            attachment.setName(imagePrefix + fileName);
            attachment.setLength((long) fileSize);
            attachment.setId(fileName);
            return attachment;
        }catch (Exception e){
            log.error("上传文件异常:",e);
            if(e instanceof GicWxAppException){
                throw (GicWxAppException)e;
            }
            throw new GicWxAppException("上传失败，请您重试！");
        }
    }

    @Override
    public void download(String fileName, HttpServletResponse response) {
        storageServiceFactory.getStorageService().downloadToResponse(fileName,response);
    }

    @Override
    public void useFile(List<String> filenames) {
        if(CollectionUtils.isEmpty(filenames)){
            return;
        }
        LambdaUpdateWrapper<GlUploadFileDO> glUploadFileDOLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        glUploadFileDOLambdaUpdateWrapper.in(GlUploadFileDO::getFileName,filenames);

        GlUploadFileDO glUploadFileDO = new GlUploadFileDO();
        glUploadFileDO.setDeleted(Deleted.UN_DELETE.getDeleted());
        glUploadFileDao.update(glUploadFileDO,glUploadFileDOLambdaUpdateWrapper);
    }
}
