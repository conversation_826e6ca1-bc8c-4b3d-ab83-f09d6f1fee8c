package com.lvpuhui.gic.wxapp.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 微信access_token表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_auth_token")
public class GlAuthTokenDO extends Model<GlAuthTokenDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户token
     */
    private String token;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 过期时间
     */
    private LocalDateTime expired;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 更新时间
     */
    private LocalDateTime updated;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
