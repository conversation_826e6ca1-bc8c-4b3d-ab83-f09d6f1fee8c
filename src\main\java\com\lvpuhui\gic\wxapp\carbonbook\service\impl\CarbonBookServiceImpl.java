package com.lvpuhui.gic.wxapp.carbonbook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonModelEmissionDao;
import com.lvpuhui.gic.wxapp.carbon.dto.CarbonEmissionSumGroupByProjectDto;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelEmissionDO;
import com.lvpuhui.gic.wxapp.carbon.service.GlCarbonModelEmissionService;
import com.lvpuhui.gic.wxapp.carbonbook.dao.GlApplicationDao;
import com.lvpuhui.gic.wxapp.carbonbook.dao.GlBehaviorDao;
import com.lvpuhui.gic.wxapp.carbonbook.dao.GlScenarioDao;
import com.lvpuhui.gic.wxapp.carbonbook.dto.*;
import com.lvpuhui.gic.wxapp.carbonbook.dto.carbonbook.AppletCarbonBookVo;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlApplication;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlScenario;
import com.lvpuhui.gic.wxapp.carbonbook.enums.CarbonBookTitleEnum;
import com.lvpuhui.gic.wxapp.carbonbook.service.CarbonBookService;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorService;
import com.lvpuhui.gic.wxapp.carbonbook.utils.MappingUtils;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankDao;
import com.lvpuhui.gic.wxapp.homepage.dto.RankConfigDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRank;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.infrastructure.sharding.DynamicTableNameHolder;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import com.lvpuhui.gic.wxapp.treeplanting.dao.TreePlantingDAO;
import com.lvpuhui.gic.wxapp.treeplanting.entity.TreePlantingDO;
import com.lvpuhui.gic.wxapp.treeplanting.enums.TreePlantingStatusEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CarbonBookServiceImpl implements CarbonBookService {

    @Resource
    GlUserService glUserService;
    @Resource
    GlAppletConfigService glAppletConfigService;
    @Resource
    GlBehaviorService glBehaviorService;

    @Resource
    private GlApplicationDao glApplicationDao;

    @Resource
    private GlScenarioDao glScenarioDao;

    @Resource
    private MappingUtils mappingUtils;

    @Resource
    private GlCarbonModelEmissionService glCarbonModelEmissionService;

    @Resource
    private TreePlantingDAO treePlantingDAO;

    @Resource
    private GlRankDao glRankDao;

    @Resource
    private GlCarbonModelEmissionDao glCarbonModelEmissionDao;

    @Resource
    private GlBehaviorDao glBehaviorDao;

    @Resource
    private GlPointsService glPointsService;

    @Resource
    private CarbonBookService carbonBookService;


    /**
     * 碳汇行为ID
     */
    private final static Integer CARBON_SINK_ID = -1;

    /**
     * 义务植树行为ID
     */
    private final static Integer TREE = -2;

    @Override
    public PointsEmission myPointsEmission(String mobileSha256) {
        PointsEmission pointsEmission = new PointsEmission();
        CarbonBooksVo carbonBooksVo = getCarbonBooks2(mobileSha256);
        BeanUtil.copyProperties(carbonBooksVo,pointsEmission);
        return pointsEmission;
    }

    @Override
    public CarbonBooksVo getCarbonBooks2(String mobileSha256) {
        mobileSha256 = UserUtils.getMobileSha256();
        CarbonBooksVo carbonBooksVo = new CarbonBooksVo();
        GlUser user = glUserService.getOne(new LambdaQueryWrapper<GlUser>().eq(GlUser::getMobileSha256,mobileSha256));
        if(Objects.nonNull(user)){
            carbonBooksVo.setPoint(user.getPointRemain());
        }
        RankConfigDto rankConfig = glAppletConfigService.getRankConfig();
        Integer top = rankConfig.getCumulative().getTop();
        GlRank glRank = glRankDao.selectById(mobileSha256);
        if(Objects.nonNull(glRank)){
            carbonBooksVo.setRank(glRank.getRank());
            if(Objects.nonNull(glRank.getRank()) && glRank.getRank() > top){
                carbonBooksVo.setRank(glRank.getCityRank());
            }
            if(Objects.nonNull(glRank.getRank()) && glRank.getRank() == -1){
                carbonBooksVo.setRank(null);
            }
        }
        List<SceneVo> sceneVos = glBehaviorService.getBehaviorGroupByScenarios(mobileSha256);
        List<SceneVo> sceneVoList = mappingUtils.buildScenarios(sceneVos);
        // 追加碳汇
        handleScene(sceneVoList,mobileSha256);
        // 追加义务植树
        handleTree(sceneVoList,UserUtils.getUserId());
        if(CollUtil.isEmpty(sceneVoList)){
            sceneVoList.forEach(sceneVo -> sceneVo.setRatio(0D));
            carbonBooksVo.setEmissionNum(0.0);
            carbonBooksVo.setEmission("0kg");
            carbonBooksVo.setTitle("减排新秀");
            carbonBooksVo.setSceneList(sceneVoList);
            return carbonBooksVo;
        }
        double sum = sceneVoList.stream().map(SceneVo::getEmissionNum)
                .collect(Collectors.summarizingDouble(Double::doubleValue)).getSum();
        carbonBooksVo.setEmission(CalcUtil.weightFormat(sum));
        carbonBooksVo.setEmissionNum(sum);
        sceneVoList.forEach(s->{
            if(sum==0){
                s.setRatio(0d);
            }else {
                s.setRatio(CalcUtil.getRatio(s.getEmissionNum(),sum));
            }
        });
        carbonBooksVo.setSceneList(sceneVoList);

        /*
        减排新秀    小于10kg
        减排小生    10kg到20kg
        减排达人    20到50
        减排学委    50到100
        减排砖家    100到1000
        减排教授    1000以上
         */
        double emissionKg = sum==0?0:NumberUtil.div(sum,1000,2);
        if(emissionKg < 10){
            carbonBooksVo.setTitle("减排新秀");
        } else if (emissionKg < 20) {
            carbonBooksVo.setTitle("减排小生");
        }else if (emissionKg < 50) {
            carbonBooksVo.setTitle("减排达人");
        }else if (emissionKg < 100) {
            carbonBooksVo.setTitle("减排学委");
        }else if (emissionKg < 1000) {
            carbonBooksVo.setTitle("减排专家");
        }else if (emissionKg > 1000) {
            carbonBooksVo.setTitle("减排教授");
        }
        return carbonBooksVo;
    }

    @Override
    public SceneTotalVo getSceneDetails2(String mobileSha256, Integer sceneId, Integer page, Integer size) {
        mobileSha256 = UserUtils.getMobileSha256();
        SceneTotalVo sceneTotalVo = new SceneTotalVo();
        page = page == null|| page<1? 1:page;
        size = size == null|| size<1? 20:size;
        int offset = (page-1)*size;
        if(sceneId.equals(9)){
            // 义务植树
            Page<TreePlantingDO> treePlantingDOPage = new Page<>(page,size);
            LambdaQueryWrapper<TreePlantingDO> treePlantingDOLambdaQueryWrapper = Wrappers.lambdaQuery();
            treePlantingDOLambdaQueryWrapper.eq(TreePlantingDO::getPlanterId,UserUtils.getUserId()).eq(TreePlantingDO::getStatus,TreePlantingStatusEnums.PASS.getCode());
            treePlantingDOLambdaQueryWrapper.orderByDesc(TreePlantingDO::getPlantingTime);
            Page<TreePlantingDO> plantingDOPage = treePlantingDAO.selectPage(treePlantingDOPage,treePlantingDOLambdaQueryWrapper);
            sceneTotalVo.setTotal((int) plantingDOPage.getTotal());
            if(CollUtil.isEmpty(plantingDOPage.getRecords())){
                sceneTotalVo.setDetails(Lists.newArrayList());
                return sceneTotalVo;
            }
            double rate = glAppletConfigService.getBehaviorToPoints();
            List<SceneDetailVo> sceneVoList = Lists.newArrayList();
            List<TreePlantingDO> treePlantingDOS = plantingDOPage.getRecords();
            for (TreePlantingDO treePlantingDO : treePlantingDOS) {
                treePlantingDO.setEmission(treePlantingDO.getEmission().multiply(BigDecimal.valueOf(1000000)));
                SceneDetailVo sceneDetailVo = new SceneDetailVo();
                sceneDetailVo.setCampus(treePlantingDO.getTreeName());
                sceneDetailVo.setTitle("义务植树");
                sceneDetailVo.setEmission(CalcUtil.weightFormat(treePlantingDO.getEmission().setScale(2,RoundingMode.HALF_UP).doubleValue()));
                sceneDetailVo.setEmissionNum(treePlantingDO.getEmission().doubleValue());
                String date = treePlantingDO.getPlantingTime().format(DatePattern.NORM_DATE_FORMATTER);
                sceneDetailVo.setDate(date);
                try{
                    sceneDetailVo.setPoint(NumberUtil.round(NumberUtil.mul(sceneDetailVo.getEmissionNum().doubleValue(),rate),0,RoundingMode.UP).doubleValue());
                } catch (Exception e) {
                    sceneDetailVo.setPoint(0d);
                }
                sceneVoList.add(sceneDetailVo);
            }
            sceneTotalVo.setDetails(sceneVoList);
            return sceneTotalVo;
        }
        Map<Long, MappingUtils.SceneConfig> mappings = mappingUtils.getMappings();
        List<Long> scenarios = mappings.get(Long.valueOf(sceneId)).getIds();
        String carbonProjectIds = mappingUtils.getCarbonProjectIds();
        List<Long> carbonLongProjectIds = StrUtil.split(carbonProjectIds, ",").stream().map(Long::parseLong).collect(Collectors.toList());
        if(CollUtil.containsAll(carbonLongProjectIds,scenarios)){
            Page<GlCarbonModelEmissionDO> glCarbonModelEmissionDOPage = new Page<>(page,size);
            // 查询碳汇的数据
            LambdaQueryWrapper<GlCarbonModelEmissionDO> glCarbonModelEmissionDOLambdaQueryWrapper = Wrappers.lambdaQuery();
            glCarbonModelEmissionDOLambdaQueryWrapper.eq(GlCarbonModelEmissionDO::getMobileSha256,mobileSha256);
            glCarbonModelEmissionDOLambdaQueryWrapper.in(GlCarbonModelEmissionDO::getProjectId,scenarios);
            glCarbonModelEmissionDOLambdaQueryWrapper.in(GlCarbonModelEmissionDO::getStatus,0);
            glCarbonModelEmissionDOLambdaQueryWrapper.orderByDesc(GlCarbonModelEmissionDO::getId);
            Page<GlCarbonModelEmissionDO> carbonModelEmissionDOPage = glCarbonModelEmissionService.page(glCarbonModelEmissionDOPage,glCarbonModelEmissionDOLambdaQueryWrapper);
            sceneTotalVo.setTotal((int) carbonModelEmissionDOPage.getTotal());
            if(CollUtil.isEmpty(carbonModelEmissionDOPage.getRecords())){
                sceneTotalVo.setDetails(Lists.newArrayList());
                return sceneTotalVo;
            }
            double rate = glAppletConfigService.getBehaviorToPoints();
            List<SceneDetailVo> sceneVoList = Lists.newArrayList();
            List<GlCarbonModelEmissionDO> glCarbonModelEmissionDOS = carbonModelEmissionDOPage.getRecords();
            List<Long> modelIds = glCarbonModelEmissionDOS.stream().map(GlCarbonModelEmissionDO::getModelId).collect(Collectors.toList());
            Map<Long,String> modelIdNameMap = glCarbonModelEmissionService.getModelIdNameMapByModelIds(modelIds);
            for (GlCarbonModelEmissionDO glCarbonModelEmissionDO : glCarbonModelEmissionDOS) {
                SceneDetailVo sceneDetailVo = new SceneDetailVo();
                sceneDetailVo.setCampus("");
                if(Objects.nonNull(glCarbonModelEmissionDO.getModelId()) && modelIdNameMap.containsKey(glCarbonModelEmissionDO.getModelId())){
                    sceneDetailVo.setCampus(modelIdNameMap.get(glCarbonModelEmissionDO.getModelId()));
                }
                sceneDetailVo.setTitle("碳汇");
                sceneDetailVo.setEmission(CalcUtil.weightFormat(glCarbonModelEmissionDO.getEmission().setScale(2,RoundingMode.HALF_UP).doubleValue()));
                sceneDetailVo.setEmissionNum(glCarbonModelEmissionDO.getEmission().doubleValue());
                String date = DateUtil.format(DateUtil.parse(glCarbonModelEmissionDO.getBatchNo(),DatePattern.PURE_DATE_PATTERN),DatePattern.NORM_DATE_PATTERN);
                sceneDetailVo.setDate(date);
                try{
                    sceneDetailVo.setPoint(NumberUtil.round(NumberUtil.mul(sceneDetailVo.getEmissionNum().doubleValue(),rate),0,RoundingMode.UP).doubleValue());
                } catch (Exception e) {
                    sceneDetailVo.setPoint(0d);
                }
                sceneVoList.add(sceneDetailVo);
            }
            sceneTotalVo.setDetails(sceneVoList);
            return sceneTotalVo;
        }

        String ids = CollUtil.join(scenarios, ",");
        List<SceneDetailDto> sceneDetailDtos = glBehaviorService.getBehaviorDetailGroupByScenarios(mobileSha256,offset,size,ids);
        Integer count = glBehaviorService.getBehaviorDetailCount(mobileSha256,ids);
        sceneTotalVo.setTotal(ObjectUtil.defaultIfNull(count,0));
        if(CollUtil.isEmpty(sceneDetailDtos)){
            sceneTotalVo.setDetails(Lists.newArrayList());
            return sceneTotalVo;
        }
        List<Integer> appIds = sceneDetailDtos.stream().map(SceneDetailDto::getAppId).collect(Collectors.toList());
        List<Integer> scenarioIds = sceneDetailDtos.stream().map(SceneDetailDto::getScenarioId).collect(Collectors.toList());
        List<GlApplication> glApplications = glApplicationDao.selectBatchIds(appIds);
        Map<Long, String> appIdNameMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glApplications)){
            appIdNameMap = glApplications.stream().collect(Collectors.toMap(GlApplication::getId, GlApplication::getName, (k1, k2) -> k1));
        }
        List<GlScenario> glScenarios = glScenarioDao.selectBatchIds(scenarioIds);
        Map<Long, String> scenarioIdNameMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glScenarios)){
            scenarioIdNameMap = glScenarios.stream().collect(Collectors.toMap(GlScenario::getId, GlScenario::getName, (k1, k2) -> k1));
        }
        double rate = glAppletConfigService.getBehaviorToPoints();
        List<SceneDetailVo> sceneVoList = Lists.newArrayList();
        for (SceneDetailDto sceneDetailDto : sceneDetailDtos) {
            SceneDetailVo sceneDetailVo = new SceneDetailVo();
            if(Objects.nonNull(sceneDetailDto.getAppId())){
                Long appId = Long.valueOf(sceneDetailDto.getAppId());
                sceneDetailVo.setCampus(appIdNameMap.get(appId));
            }
            if(Objects.nonNull(sceneDetailDto.getScenarioId())){
                Long scenarioId = Long.valueOf(sceneDetailDto.getScenarioId());
                sceneDetailVo.setTitle(scenarioIdNameMap.get(scenarioId));
            }
            sceneDetailVo.setEmission(CalcUtil.weightFormat(sceneDetailDto.getEmissionNum()));
            sceneDetailVo.setEmissionNum(sceneDetailDto.getEmissionNum());
            sceneDetailVo.setDate(DateUtil.formatDate(sceneDetailDto.getDate()));
            try{
                sceneDetailVo.setPoint(NumberUtil.round(NumberUtil.mul(sceneDetailVo.getEmissionNum().doubleValue(),rate),0,RoundingMode.UP).doubleValue());
            } catch (Exception e) {
                sceneDetailVo.setPoint(0d);
            }
            sceneVoList.add(sceneDetailVo);
        }
        sceneVoList.sort(Comparator.comparing(SceneDetailVo::getDate).reversed());
        sceneTotalVo.setDetails(sceneVoList);
        return sceneTotalVo;
    }

    @Override
    public CarbonBooksNewVo carbonBooksMobile() {
        String mobileSha256 = UserUtils.getMobileSha256();
        CarbonBooksNewVo carbonBooksNewVo = new CarbonBooksNewVo();

        GlUser user = glUserService.getOne(new LambdaQueryWrapper<GlUser>().eq(GlUser::getMobileSha256,mobileSha256));
        if(Objects.nonNull(user)){
            carbonBooksNewVo.setPoint(user.getPointRemain().intValue());
        }
        LambdaQueryWrapper<GlRank> glRankLambdaQueryWrapper = Wrappers.lambdaQuery();
        glRankLambdaQueryWrapper.orderByDesc(GlRank::getRank);
        glRankLambdaQueryWrapper.last("limit 1");
        GlRank glRankLast = glRankDao.selectOne(glRankLambdaQueryWrapper);

        RankConfigDto rankConfig = glAppletConfigService.getRankConfig();
        Integer top = rankConfig.getCumulative().getTop();
        GlRank glRank = glRankDao.selectById(mobileSha256);
        if(Objects.isNull(glRank)){
            glRank = new GlRank();
            glRank.setMobileSha256(mobileSha256);
            glRank.setRank(-1L);
            CarbonBooksVo carbonBooksByMobileSha256 = carbonBookService.getCarbonBooksByMobileSha256(mobileSha256);
            if(Objects.nonNull(carbonBooksByMobileSha256)){
                glRank.setEmission(Objects.isNull(carbonBooksByMobileSha256.getEmissionNum())?0:carbonBooksByMobileSha256.getEmissionNum());
            }
            glRankDao.insert(glRank);
        }
        if(Objects.nonNull(glRank)){
            carbonBooksNewVo.setRank(glRank.getRank());
            if(Objects.nonNull(glRank.getRank())){
                if(glRank.getRank() > top){
                    carbonBooksNewVo.setRank(glRank.getCityRank());
                    carbonBooksNewVo.setRadio(CalcUtil.calcRankRadio(glRank.getCityRank(),glRankLast.getCityRank()));
                }else {
                    carbonBooksNewVo.setRadio(CalcUtil.calcRankRadio(glRank.getRank(),glRankLast.getCityRank()));
                }
                if(Objects.isNull(glRank.getEmission()) || glRank.getEmission() <= 0){
                    carbonBooksNewVo.setRadio(0D);
                }
            }
            if(Objects.nonNull(glRank.getRank()) && glRank.getRank() == -1){
                carbonBooksNewVo.setRank(null);
            }
        }
        List<CarbonBooksNewVo.EmissionInfoVo> emissionInfoVos = Lists.newArrayList();
        // 获取减排量
        List<CarbonBooksNewVo.EmissionInfoVo> emission = getEmission(mobileSha256);
        if(CollUtil.isNotEmpty(emission)){
            emissionInfoVos.addAll(emission);
        }
        BigDecimal sum = emissionInfoVos.stream().map(CarbonBooksNewVo.EmissionInfoVo::getEmissionNum)
                .map(BigDecimal::valueOf)
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        for (CarbonBooksNewVo.EmissionInfoVo emissionInfoVo : emissionInfoVos) {
            if(sum.compareTo(BigDecimal.ZERO) == 0){
                emissionInfoVo.setRatio(0.0);
            }else {
                emissionInfoVo.setRatio(CalcUtil.getRatio(emissionInfoVo.getEmissionNum(), sum.doubleValue()));
            }
        }
        carbonBooksNewVo.setSceneList(emissionInfoVos);
        carbonBooksNewVo.setEmissionNum(sum.doubleValue());
        carbonBooksNewVo.setEmission(CalcUtil.weightFormat(sum.doubleValue()));
        double emissionKg = sum.compareTo(BigDecimal.ZERO) == 0?0:NumberUtil.div(sum.doubleValue(),1000,2);
        carbonBooksNewVo.setTitle(CarbonBookTitleEnum.getTitle(emissionKg).getDesc());
        return carbonBooksNewVo;
    }

    @Override
    public CarbonBooksDetailNewVo carbonBooksMobileDetail(Integer actId, Integer page, Integer size) {
        CarbonBooksDetailNewVo carbonBooksDetailNewVo = new CarbonBooksDetailNewVo();
        page = page == null|| page<1? 1:page;
        size = size == null|| size<1? 20:size;
        String mobileSha256 = UserUtils.getMobileSha256();
        double rate = glAppletConfigService.getBehaviorToPoints();
        Page<SceneDetailDto> voPage = new Page<>(page,size);
        Page<SceneDetailDto> sceneDetailDtoPage = glBehaviorDao.getBehaviorByActId(voPage,mobileSha256,actId);
        carbonBooksDetailNewVo.setTotal((int) sceneDetailDtoPage.getTotal());
        if(CollUtil.isEmpty(sceneDetailDtoPage.getRecords())){
            carbonBooksDetailNewVo.setDetails(Lists.newArrayList());
            return carbonBooksDetailNewVo;
        }
        List<SceneDetailDto> sceneDetailDtos = sceneDetailDtoPage.getRecords();
        List<Integer> appIds = sceneDetailDtos.stream().map(SceneDetailDto::getAppId).collect(Collectors.toList());
        List<Integer> scenarioIds = sceneDetailDtos.stream().map(SceneDetailDto::getScenarioId).collect(Collectors.toList());
        List<GlApplication> glApplications = glApplicationDao.selectBatchIds(appIds);
        Map<Long, String> appIdNameMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glApplications)){
            appIdNameMap = glApplications.stream().collect(Collectors.toMap(GlApplication::getId, GlApplication::getName, (k1, k2) -> k1));
        }
        List<GlScenario> glScenarios = glScenarioDao.selectBatchIds(scenarioIds);
        Map<Long, String> scenarioIdNameMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glScenarios)){
            scenarioIdNameMap = glScenarios.stream().collect(Collectors.toMap(GlScenario::getId, GlScenario::getName, (k1, k2) -> k1));
        }
        List<CarbonBooksDetailNewVo.CarbonBooksDetailVo> carbonBooksDetailVos = Lists.newArrayList();
        for (SceneDetailDto sceneDetailDto : sceneDetailDtos) {
            CarbonBooksDetailNewVo.CarbonBooksDetailVo carbonBooksDetailVo = new CarbonBooksDetailNewVo.CarbonBooksDetailVo();
            if(Objects.nonNull(sceneDetailDto.getAppId())){
                Long appId = Long.valueOf(sceneDetailDto.getAppId());
                carbonBooksDetailVo.setCampus(appIdNameMap.get(appId));
            }
            if(Objects.nonNull(sceneDetailDto.getScenarioId())){
                Long scenarioId = Long.valueOf(sceneDetailDto.getScenarioId());
                carbonBooksDetailVo.setTitle(scenarioIdNameMap.get(scenarioId));
            }
            carbonBooksDetailVo.setEmission(CalcUtil.weightFormat(sceneDetailDto.getEmissionNum()));
            carbonBooksDetailVo.setEmissionNum(sceneDetailDto.getEmissionNum());
            carbonBooksDetailVo.setDate(DateUtil.formatDate(sceneDetailDto.getDate()));
            try{
                carbonBooksDetailVo.setPoint(NumberUtil.round(NumberUtil.mul(carbonBooksDetailVo.getEmissionNum().doubleValue(),rate),0,RoundingMode.UP).doubleValue());
            } catch (Exception e) {
                carbonBooksDetailVo.setPoint(0d);
            }
            carbonBooksDetailVos.add(carbonBooksDetailVo);
        }
        carbonBooksDetailNewVo.setDetails(carbonBooksDetailVos);
        return carbonBooksDetailNewVo;
    }

    @Override
    public CarbonBooksVo getCarbonBooksByMobileSha256(String mobileSha256) {
        CarbonBooksVo carbonBooksVo = new CarbonBooksVo();
        try {
            String index = mobileSha256.substring(0,2).toLowerCase();
            DynamicTableNameHolder.set("gl_behavior_"+index);

            BigDecimal emission = glBehaviorDao.getSumEmissionByMobileSha256(mobileSha256);
            carbonBooksVo.setEmissionNum(Objects.isNull(emission)?0D:emission.doubleValue());
        }finally {
            DynamicTableNameHolder.remove();
        }
        return carbonBooksVo;
    }

    private List<CarbonBooksNewVo.EmissionInfoVo> getEmission(String mobileSha256){
        JSONObject behaviorMap = glPointsService.getBehaviorMap();
        List<AppletCarbonBookVo> appletCarbonBookVos = null;
        try {
            String index = mobileSha256.substring(0,2).toLowerCase();
            DynamicTableNameHolder.set("gl_behavior_"+index);
            appletCarbonBookVos = glBehaviorDao.getSumEmissionGroupByAct(mobileSha256);
            if(CollUtil.isEmpty(appletCarbonBookVos)){
                return null;
            }
            return appletCarbonBookVos.stream()
                    .filter(Objects::nonNull)
                    .map(appletCarbonBookVo -> {
                        if(Objects.isNull(appletCarbonBookVo.getActEmission())){
                            appletCarbonBookVo.setActEmission(0d);
                        }
                        CarbonBooksNewVo.EmissionInfoVo emissionInfoVo = new CarbonBooksNewVo.EmissionInfoVo();
                        emissionInfoVo.setEmission(CalcUtil.weightFormat(appletCarbonBookVo.getActEmission()));
                        emissionInfoVo.setEmissionNum(appletCarbonBookVo.getActEmission());
                        emissionInfoVo.setId(appletCarbonBookVo.getActId().intValue());
                        if(behaviorMap.containsKey(appletCarbonBookVo.getActId().toString())){
                            emissionInfoVo.setName(behaviorMap.getString(appletCarbonBookVo.getActId().toString()));
                        }
                        return emissionInfoVo;
                    }).collect(Collectors.toList());
        }finally {
            DynamicTableNameHolder.remove();
        }
    }

    void handleScene(List<SceneVo> sceneVoList,String mobileSha256){
        List<CarbonEmissionSumGroupByProjectDto> carbonEmissionSumGroupByProjectDtoList = glCarbonModelEmissionService.getEmissionSumGroupByProject(mobileSha256);
        if(CollUtil.isEmpty(carbonEmissionSumGroupByProjectDtoList)){
            return;
        }
        List<Long> carbonProjectIds = Arrays.stream(mappingUtils.getCarbonProjectIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        sceneVoList.forEach(sceneVo -> {
            List<Long> mapping = mappingUtils.getMapping(sceneVo.getId().longValue());
            if(CollUtil.containsAny(carbonProjectIds,mapping)){
                BigDecimal reduce = carbonEmissionSumGroupByProjectDtoList.stream()
                        .filter(carbonEmissionSumGroupByProjectDto -> mapping.contains(carbonEmissionSumGroupByProjectDto.getProjectId()))
                        .map(CarbonEmissionSumGroupByProjectDto::getEmissionSum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                sceneVo.setEmissionNum(reduce.setScale(2,RoundingMode.HALF_UP).doubleValue());
                sceneVo.setEmission(CalcUtil.weightFormat(sceneVo.getEmissionNum()));
            }
        });
    }

    private void handleTree(List<SceneVo> sceneVoList, Long userId) {
        LambdaQueryWrapper<TreePlantingDO> treePlantingDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        treePlantingDOLambdaQueryWrapper.eq(TreePlantingDO::getStatus, TreePlantingStatusEnums.PASS.getCode());
        treePlantingDOLambdaQueryWrapper.eq(TreePlantingDO::getPlanterId,userId);
        List<TreePlantingDO> treePlantingDOS = treePlantingDAO.selectList(treePlantingDOLambdaQueryWrapper);
        if(CollUtil.isEmpty(treePlantingDOS)){
            return;
        }
        for (SceneVo sceneVo : sceneVoList) {
            if(!sceneVo.getId().equals(9)){
                continue;
            }
            BigDecimal reduce = treePlantingDOS.stream().map(TreePlantingDO::getEmission).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            sceneVo.setEmissionNum(reduce.multiply(BigDecimal.valueOf(1000000)).doubleValue());
            sceneVo.setEmission(CalcUtil.weightFormat(sceneVo.getEmissionNum()));
        }
    }
}
