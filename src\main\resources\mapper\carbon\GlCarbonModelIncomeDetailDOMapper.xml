<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonModelIncomeDetailDao">

    <select id="sumReward" resultType="java.math.BigDecimal">
        SELECT SUM(amount) FROM gl_carbon_model_income_detail WHERE `type` = 1 AND amount &gt; 0 AND model_id = #{modelId}

    </select>
    <select id="punAmount" resultType="java.math.BigDecimal">
        SELECT SUM(amount) FROM gl_carbon_model_income_detail WHERE `type` = 1 AND amount &lt; 0 AND model_id = #{modelId}
    </select>
    <select id="selectByModelId" resultType="com.lvpuhui.gic.wxapp.carbon.dto.InComeDetails">
        select
        a.`name`,a.amount,a.income_date,a.remain_amount,b.address,a.`status`
        from gl_carbon_model_income_detail as a left join gl_carbon_model as b
        on a.model_id = b.id
        where a.model_id = #{modelId} and a.income_date >= #{dateTime} order by a.created desc
    </select>
    <select id="getCarbonEveryReward" resultType="java.math.BigDecimal">
        select sum(amount)
        from gl_carbon_model_income_detail
        where
        user_id = #{userId} and income_date = #{yesterday} and status = 1
    </select>
    <select id="getCarbonEveryModelReward" resultType="java.math.BigDecimal">
        select sum(amount)
        from gl_carbon_model_income_detail
        where
        model_id = #{modelId} and income_date = #{yesterday} and status = 1 and user_id = #{userId}
    </select>
    <select id="getUnTakingAmountByUserId" resultType="java.math.BigDecimal">
        SELECT SUM(amount) FROM gl_carbon_model_income_detail WHERE user_id = #{userId} AND `status` = 1 AND taking_status = 0
    </select>
    <select id="selectByUserId" resultType="com.lvpuhui.gic.wxapp.carbon.dto.InComeDetails">
        select
        a.`name`,a.amount,a.income_date,a.remain_amount,b.address,a.`status`
        from gl_carbon_model_income_detail as a left join gl_carbon_model as b
        on a.model_id = b.id
        where a.user_id = #{userId} and a.income_date >= #{dateTime} order by a.created desc
    </select>
</mapper>
