package com.lvpuhui.gic.wxapp.team.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.team.dto.TeamListDto;
import com.lvpuhui.gic.wxapp.team.dto.TeamListVo;
import com.lvpuhui.gic.wxapp.team.entity.GlTeam;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 战队表(GlTeam)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-24 11:24:25
 */
public interface GlTeamDao extends BaseMapper<GlTeam> {

    /**
     * 获取战队列表信息
     * @param teamListDto 参数
     */
    List<TeamListVo> getTeamList(@Param("teamListDto") TeamListDto teamListDto);

    /**
     * 增加战队减排量
     * @param emission 减排量
     * @param teamId 战队ID
     */
    int updateIncrementEmission(@Param("emission") BigDecimal emission,@Param("teamId")Long teamId);

    /**
     * 扣减战队减排量
     * @param emission 减排量
     * @param teamId 战队ID
     */
    int updateDeductEmission(@Param("emission") BigDecimal emission,@Param("teamId")Long teamId);
}
