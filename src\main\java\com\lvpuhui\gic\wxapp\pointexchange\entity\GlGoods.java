package com.lvpuhui.gic.wxapp.pointexchange.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 商品表(GlGoods)表实体类
 * <AUTHOR>
 * @since 2022年5月5日19:15:16
 */
@Data
public class GlGoods extends Model<GlGoods> {

    /**
     * 商品id(主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品封面图
     */
    private String coverImage;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品信息
     */
    private String description;

    /**
     * 注意事项
     */
    private String attention;

    /**
     * 有效期
     */
    private String expired;

    /**
     * 商品价值
     */
    private String price;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 商品类型
     */
    private String type;

    /**
     * 兑换积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 库存数量
     */
    private Integer inventory;

    /**
     * 已兑换数量
     */
    private Integer inventoryConsumed;

    /**
     * 剩余数量
     */
    private Integer inventoryRemain;

    /**
     * 兑换个数限制
     */
    private Integer exchangeLimit;

    /**
     * 兑换地址
     */
    private String url;

    /**
     * 上下架状态(0：未上架  1：上架  2：下架)
     */
    private Integer state;

    /**
     * 位置权重
     */
    private Integer sequence;

    /**
     * 商家电话
     */
    private String contactNumber;

    /**
     * 兑换类型 0:兑换码 1:直接跳转
     */
    private Integer exchangeType;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private Date updated;

    /**
     * 修改者ID
     */
    private Long updator;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;

    /**
     * 小程序appid
     */
    private String appletAppid;

    /**
     * 小程序调转地址
     */
    private String appletUrl;
}
