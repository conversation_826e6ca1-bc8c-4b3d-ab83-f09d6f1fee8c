<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlReportMediaDao">
    <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.carbon.entity.GlReportMediaDO">
        <!--@Table gl_report_media-->
        <id column="id" property="id"/>
        <result column="report_id" property="reportId"/>
        <result column="media_type" property="mediaType"/>
        <result column="media_url" property="mediaUrl"/>
        <result column="created" property="created"/>
        <result column="updated" property="updated"/>
    </resultMap>

<!--auto generated by MybatisCodeHelper on 2023-09-13-->
    <insert id="insertList">
        INSERT INTO gl_report_media(
        id,
        report_id,
        media_type,
        media_url,
        created,
        updated
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.reportId},
            #{element.mediaType},
            #{element.mediaUrl},
            #{element.created},
            #{element.updated}
            )
        </foreach>
    </insert>
</mapper>
