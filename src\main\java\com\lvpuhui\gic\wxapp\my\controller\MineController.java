package com.lvpuhui.gic.wxapp.my.controller;

import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.alipay.dto.AuthCodeDTO;
import com.lvpuhui.gic.wxapp.alipay.serivice.AuthService;
import com.lvpuhui.gic.wxapp.my.dto.*;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 小程序我的-服务
 * <AUTHOR>
 * @since 2022年05月07日 14:26:00
 */
@Tags(value = {@Tag(name = "用户接口1.0")})
@RestController
public class MineController extends ApiController {

    @Autowired
    private GlUserService glUserService;

    @Resource
    private AuthService alipayAuthService;

    /**
     * 获取token是否有效
     */
    @Operation(summary = "获取token是否有效接口", description = "获取token是否有效接口", tags = { "用户接口1.0" })
    @PassToken
    @GetMapping("/token_valid")
    public R<TokenValidDto> getTokenValid(HttpServletRequest request){
        TokenValidDto tokenValidDto = glUserService.getTokenValid(request);
        return R.ok(tokenValidDto);
    }

    /**
     * 保存用户信息接口-lijianguang
     */
    @Operation(summary = "保存用户信息接口", description = "保存用户信息接口", tags = { "用户接口1.0" })
    @PostMapping("/user_auth")
    public R<UserInfo> userAuth(@RequestBody UserAuthDto userAuthDto){
        UserInfo userInfo = glUserService.userAuth(userAuthDto);
        return success(userInfo);
    }

    /**
     * 根据code获取openid和加密手机号接口-lijianguang
     */
    @Operation(summary = "授权接口", description = "授权接口", tags = { "用户接口1.0" })
    @PassToken
    @PostMapping("/code2session")
    public R<CodeOpenid> code2session(@RequestBody @Valid CodeOpenidDto codeOpenidDto){
        CodeOpenid codeOpenid = glUserService.code2session(codeOpenidDto);
        return success(codeOpenid);
    }

    /**
     * 根据authcode获取openid和加密手机号接口-lijianguang
     */
    @PassToken
    @PostMapping("/authtoken")
    public R<CodeOpenid> authAlipayToken(@RequestBody @Valid AuthCodeDTO authCodeDTO){
        CodeOpenid codeOpenid = alipayAuthService.getAuthUser(authCodeDTO);
        return success(codeOpenid);
    }

    /**
     * 用户授权手机号记录接口
     */
    @GetMapping("/auth_record")
    public R<String> authRecord(){
        glUserService.authRecord();
        return R.ok("success");
    }

    /**
     * 查询配置
     */
    @Operation(summary = "查询阅读配置", description = "查询阅读配置", tags = { "用户接口1.0" })
    @GetMapping("/queryReadConfig")
    public R<SaveNewsConfigDto> queryReadConfig(){
        SaveNewsConfigDto saveNewsConfigDto = glUserService.queryReadConfig();
        return R.ok(saveNewsConfigDto);
    }

    /**
     * 用户阅读发放积分奖励
     */
    @Operation(summary = "用户阅读发放积分奖励", description = "用户阅读发放积分奖励", tags = { "用户接口1.0" })
    @PostMapping("/sendUserReadPoints")
    public R<Integer> sendUserReadPoints(@RequestBody SendUserReadPointsDto sendUserReadPointsDto){
        Integer integer = glUserService.sendUserReadPoints2(sendUserReadPointsDto);
        return R.ok(integer);
    }

    @Operation(summary = "用户阅读发放积分奖励", description = "用户阅读发放积分奖励", tags = { "用户接口1.0" })
    @PostMapping("/sendUserReadPoints2")
    public R<Integer> sendUserReadPoints2(@RequestBody SendUserReadPointsDto sendUserReadPointsDto){
        Integer integer = glUserService.sendUserReadPoints(sendUserReadPointsDto);
        return R.ok(integer);
    }

}
