package com.lvpuhui.gic.wxapp.carbon.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.carbon.dto.CarbonRankUserVo;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonRankDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 碳汇排行榜 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
public interface GlCarbonRankDao extends BaseMapper<GlCarbonRankDO> {

    /**
     * 查询碳汇排行前20的用户
     */
    List<CarbonRankUserVo> carbonRankList();

    /**
     * 获取当前登录人排行榜信息
     * @param mobileSha256 登录人
     */
    CarbonRankUserVo getCurrentUserRank(@Param("mobileSha256") String mobileSha256);

    GlCarbonRankDO selectByUserId(@Param("userId")Long userId);
}
