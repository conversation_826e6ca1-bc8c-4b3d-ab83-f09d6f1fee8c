package com.lvpuhui.gic.wxapp.infrastructure.interceptor;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.base.dao.GlAuthTokenDao;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppAuthException;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.base.entity.GlAuthTokenDO;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.infrastructure.dto.User;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AuthHandlerInterceptor implements HandlerInterceptor {

    private static AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Resource
    private GlAuthTokenDao glAuthTokenDao;

    @Resource
    private GlUserDao glUserDao;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws GicWxAppException {
        String requestPath = request.getRequestURI();
        if(requestPath == null) {
            try {
                log.error("非法请求");
                response.setContentType("text/plain; charset=utf-8");
                response.getWriter().write("非法请求");
                response.getWriter().flush();
                return false;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        // 不允许出现跨目录字符
        if(requestPath.contains("/.") || requestPath.contains("\\.")) {
            if(!requestPath.contains("download")){
                try {
                    log.error("非法请求:{}", requestPath);
                    response.setContentType("text/plain; charset=utf-8");
                    response.getWriter().write("非法请求");
                    response.getWriter().flush();
                    return false;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        // 不允许包含非法字符
        for (String item : invalidCharacter) {
            if (requestPath.contains(item)) {
                try {
                    log.error("非法请求:{}", requestPath);
                    response.setContentType("text/plain; charset=utf-8");
                    response.getWriter().write("非法请求");
                    response.getWriter().flush();
                    return false;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        if(skip(request)) {
            return true;
        }
        if(StrUtil.isBlank(MDC.get(Global.TRACE_ROUTE_HEADER))){
            MDC.put(Global.TRACE_ROUTE_HEADER, IdUtil.fastSimpleUUID());
        }
        String token = request.getHeader("token");
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        if (method.isAnnotationPresent(PassToken.class)) {// skip token check
            if(StrUtil.isNotBlank(token)){
                processSession(token,request);
            }
            PassToken passToken = method.getAnnotation(PassToken.class);
            if (passToken.required()) {
                return true;
            }
        }
        if(StrUtil.isBlank(token)){
            throw new GicWxAppAuthException("请您先授权");
        }
        processSession(token,request);
        return true;
    }

    private void processSession(String token,HttpServletRequest request){
        LambdaQueryWrapper<GlAuthTokenDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(GlAuthTokenDO::getToken,token);
        GlAuthTokenDO glAuthTokenDO = glAuthTokenDao.selectOne(lambdaQueryWrapper);
        if(Objects.isNull(glAuthTokenDO)){
            throw new GicWxAppAuthException("授权失效,请删除小程序重新进入");
        }
        if(Objects.nonNull(glAuthTokenDO.getExpired()) && LocalDateTime.now().isAfter(glAuthTokenDO.getExpired())){
            throw new GicWxAppAuthException("您授权已过期,请重新授权");
        }
        GlUser glUser = glUserDao.selectById(glAuthTokenDO.getUserId());
        if(Objects.isNull(glUser)){
            throw new GicWxAppAuthException("登录信息有误,请删除小程序重新进入");
        }
        User user = new User();
        user.setId(glUser.getId());
        user.setName(glUser.getNickName());
        user.setToken(token);
        user.setOpenId(glUser.getOpenId());
        user.setMobile(glUser.getMobile());
        user.setMobileSha256(glUser.getMobileSha256());
        request.setAttribute(Global.USER, JSON.toJSONString(user));
    }

    private boolean skip(HttpServletRequest request) {
        String path = request.getServletPath();
        for (String skipPath : SKIP_PATHS) {
            if (antPathMatcher.match(skipPath, path)) {
                return true;
            }
        }
        return false;
    }

    private final static String[] SKIP_PATHS = {
            "/v3/api-docs/*",
            "/doc.html",
            "/swagger-resources/**",
            "/swagger/**",
            "/**/v2/api-docs",
            "/**/*.js",
            "/**/*.css",
            "/**/*.png",
            "/**/*.ico",
            "/error",
            "/v2/**",
            "/swagger-ui.html/**",
            "/webjars/**"
    };

    private final String[] invalidCharacter = {
            "//",           // //
            "\\",			// \
            "%2e", "%2E",	// .
            "%2f", "%2F",	// /
            "%5c", "%5C",	// \
            ";", "%3b", "%3B",	// ;    // 参考资料：https://mp.weixin.qq.com/s/77CIDZbgBwRunJeluofPTA
            "%25"			// 空格
    };
}
