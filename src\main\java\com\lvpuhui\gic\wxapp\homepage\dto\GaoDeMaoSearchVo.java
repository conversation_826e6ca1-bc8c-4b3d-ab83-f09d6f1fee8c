package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022年06月13日 18:28:00
 */
@Data
public class GaoDeMaoSearchVo {

    /**
     * 如果成功返回1，如果失败返回0
     */
    private String status;

    /**
     * 访问状态值的说明，如果成功返回"ok"，失败返回错误原因
     */
    private String info;

    /**
     * 返回状态说明,10000代表正确,详情参阅info状态表
     */
    private String infocode;

    /**
     * 单次请求返回的实际poi点的个数
     */
    private String count;

    /**
     * 返回的poi完整集合
     */
    private List<PoisBean> pois;

    @Data
    public static class PoisBean {

        /**
         * poi名称
         */
        private String name;

        /**
         * poi唯一标识
         */
        private String id;

        /**
         * poi经纬度 格式: 经度,维度
         */
        private String location;

        /**
         * poi所属类型
         */
        private String type;

        /**
         * poi分类编码
         */
        private String typecode;

        /**
         * poi所属省份
         */
        private String pname;

        /**
         * poi所属城市
         */
        private String cityname;

        /**
         * poi所属区县
         */
        private String adname;

        /**
         * poi详细地址
         */
        private String address;

        /**
         * poi所属省份编码
         */
        private String pcode;

        /**
         * poi所属区域编码
         */
        private String citycode;

        /**
         * poi所属城市编码
         */
        private String adcode;
    }
}
