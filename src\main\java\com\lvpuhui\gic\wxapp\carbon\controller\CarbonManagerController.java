package com.lvpuhui.gic.wxapp.carbon.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbon.dto.*;
import com.lvpuhui.gic.wxapp.carbon.service.CarbonManagerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Tags(value = {@Tag(name = "碳汇1.0")})
@RestController
@RequestMapping("programCarbonManager")
public class CarbonManagerController {

    @Resource
    private CarbonManagerService carbonManagerService;

    @Operation(summary = "碳汇管理", description = "碳汇管理", tags = { "碳汇管理1.0" })
    @GetMapping("/carbonManager")
    public R<CarbonManagerVo> carbonManager() {
        CarbonManagerVo carbonManagerVo =  carbonManagerService.carbonManager();
        return R.ok(carbonManagerVo);
    }

    @Operation(summary = "预计收入明细", description = "预计收入明细", tags = { "碳汇管理1.0" })
    @GetMapping("/carbonProjectIncome")
    public R<CarbonProjectIncomeVo> carbonProjectIncome() {
        CarbonProjectIncomeVo carbonProjectIncomeVo = carbonManagerService.carbonProjectIncome();
        return R.ok(carbonProjectIncomeVo);
    }

    @Operation(summary = "碳汇明细", description = "碳汇明细", tags = { "碳汇管理1.0" })
    @GetMapping("/carbonDetails")
    public R<CarbonDetailsVo> carbonDetails(@RequestParam Long modelId) {
        CarbonDetailsVo carbonDetailsVo = carbonManagerService.carbonDetails(modelId);
        return R.ok(carbonDetailsVo);
    }


    @Operation(summary = "model日收益明细", description = "日收益明细", tags = { "碳汇管理1.0" })
    @GetMapping("/inComeDayDetails")
    public R<IncomeDayVo> inComeDayDetails(@RequestParam Long modelId) {
        IncomeDayVo incomeDayVo = carbonManagerService.inComeDayDetails(modelId);
        return R.ok(incomeDayVo);
    }

    @Operation(summary = "所有日收益明细", description = "日收益明细", tags = { "碳汇管理1.0" })
    @GetMapping("/allInComeDayDetails")
    public R<IncomeDayVo> allInComeDayDetails() {
        IncomeDayVo incomeDayVo = carbonManagerService.allInComeDayDetails();
        return R.ok(incomeDayVo);
    }


    @Operation(summary = "碳汇每日收益", description = "碳汇每日收益", tags = { "碳汇管理1.0" })
    @GetMapping("/getCarbonEveryReward")
    public R<BigDecimal> getCarbonEveryReward() {
        BigDecimal reward = carbonManagerService.getCarbonEveryReward();
        return R.ok(reward);
    }

    @Operation(summary = "model每日收益", description = "model每日收益", tags = { "碳汇管理1.0" })
    @GetMapping("/getCarbonEveryModelReward")
    public R<BigDecimal> getCarbonEveryModelReward(@RequestParam Long modelId) {
        BigDecimal reward = carbonManagerService.getCarbonEveryModelReward(modelId);
        return R.ok(reward);
    }

    @Operation(summary = "开发碳汇", description = "开发碳汇", tags = { "碳汇管理1.0" })
    @PostMapping("/saveCarbon")
    public R saveCarbon(@RequestBody CarbonSaveDto carbonSaveDto) {
        carbonManagerService.saveCarbon(carbonSaveDto);
        return R.ok("");
    }

    @Operation(summary = "选择碳汇类型", description = "选择碳汇类型", tags = { "碳汇管理1.0" })
    @GetMapping("/getProjectList")
    public R<List<CarbonTypeListVo>> getProjectList() {
        List<CarbonTypeListVo> list = carbonManagerService.getProjectList();
        return R.ok(list);
    }

    @Operation(summary = "查询当年所有档位", description = "查询当年所有档位", tags = { "碳汇管理1.0" })
    @GetMapping("/getAllSubsidyList")
    public R<List<CarbonSubsidyListVo>> getAllSubsidyList() {
        List<CarbonSubsidyListVo> list = carbonManagerService.getAllSubsidyList();
        return R.ok(list);
    }














}
