package com.lvpuhui.gic.wxapp.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 创建战队请求参数
 * <AUTHOR>
 * @since 2023年05月24日 15:14:00
 */
@Data
@Schema(description = "创建战队请求参数DTO")
public class TeamCreateDto {

    /**
     * 战队名称
     */
    @NotBlank(message = "请您输入战队名称")
    @Schema(description = "战队名称")
    private String teamName;

    /**
     * 战队Logo
     */
    @NotBlank(message = "请您上传战队LOGO")
    @Schema(description = "战队Logo")
    private String teamLogo;

    /**
     * 邀请码
     */
    @Schema(description = "邀请码")
    private String inviteCode;

    /**
     * 加入方式,无限制:0,邀请:1
     */
    @NotNull(message = "请您选择加入限制方式")
    @Schema(description = "加入方式,无限制:0,邀请:1")
    private Integer joinMode;

    /**
     * 类型 0:企业 1:学校 2:政府 3:其他
     */
    @Schema(description = "类型 0:企业 1:学校 2:政府 3:其他")
    private Integer type;
}
