package com.lvpuhui.gic.wxapp.homepage.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 公告详情PO
 * <AUTHOR>
 * @since 2022年06月02日 14:41:00
 */
@Data
public class NoticeDetail {

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String title;

    /**
     * 场景企业名称
     */
    private String content;

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date published;
}
