package com.lvpuhui.gic.wxapp.team.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 战队详情返回值
 * <AUTHOR>
 * @since 2023年05月24日 15:36:00
 */
@Data
@Schema(description = "战队详情返回值VO")
public class TeamListDetailVo {

    /**
     * 自己战队的ID
     */
    @Schema(description = "自己战队的ID")
    private Long myTeamId;

    /**
     * 战队ID
     */
    @Schema(description = "战队ID")
    private Long id;

    /**
     * 战队名称
     */
    @Schema(description = "战队名称")
    private String teamName;

    /**
     * 战队Logo
     */
    @Schema(description = "战队Logo")
    private String teamLogo;

    /**
     * 加入方式,无限制:0,邀请:1
     */
    @Schema(description = "加入方式,无限制:0,邀请:1")
    private Integer joinMode;

    /**
     * 密码是否为空  true：为空 false：不为空
     */
    @Schema(description = "密码是否为空  true：为空 false：不为空")
    private Boolean passwordBlank = true;

    /**
     * 成员人数
     */
    @Schema(description = "成员人数")
    private Integer memberCount;

    /**
     * 减排量
     */
    @Schema(description = "减排量")
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    @Schema(description = "减排量-文本")
    private String emissionText;

    /**
     * 创建人昵称
     */
    @Schema(description = "创建人昵称")
    private String nickName;

    /**
     * 创建人头像
     */
    @Schema(description = "创建人头像")
    private String avatarUrl;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd")
    @Schema(description = "创建时间")
    private LocalDateTime created;

    /**
     * 是否已认证 0：未认证  1：已认证
     */
    @Schema(description = "是否已认证 0：未认证  1：已认证")
    private Integer certification;

    /**
     * 类型 0:企业 1:学校 2:政府 3:其他
     */
    @Schema(description = "类型 0:企业 1:学校 2:政府 3:其他")
    private Integer type;

    /**
     * 当前登录人的减排量
     */
    @Schema(description = "当前登录人的减排量")
    private String myEmissionText;

    @Schema(description = "战队排名")
    private Long rank;

    @Schema(description = "当前登录人队内排名")
    private Long memberRank;

    /**
     * 人均贡献减排量
     */
    @Schema(description = "人均贡献减排量")
    private String avgEmission;

    /**
     * 成员信息集合
     */
    @Schema(description = "成员信息集合")
    private List<TeamListDetailMemberVo> memberVos;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return emissionText;
    }
}
