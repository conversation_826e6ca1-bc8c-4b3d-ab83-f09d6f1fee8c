package com.lvpuhui.gic.wxapp.carbonbook.entity;

import java.util.Date;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 绿色行为记录表 (来源大数据增量)(GlBehaviorTmp)表实体类
 *
 * <AUTHOR>
 * @since 2022-05-20 10:52:59
 */
@Data
@SuppressWarnings("serial")
public class GlBehaviorTmp extends Model<GlBehaviorTmp> {
  @TableId(type = IdType.AUTO)
    /**自增id*/
    private Long id;
    /**减排行为数据的id*/
    private String eventId;
    /**行政区划*/
    private String region;
    /**租户id*/
    private Integer tenantId;
    /**应用id*/
    private Integer appId;
    /**场景id*/
    private Integer scenarioId;
    /**行为id*/
    private Integer actId;
    /**手机号*/
    private String mobileSha256;
    /**行为发生日期*/
    private Date date;
    /**减排量*/
    private Double emission;
    /**创建时间*/
    private Date created;

}