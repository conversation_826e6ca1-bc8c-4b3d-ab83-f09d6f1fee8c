package com.lvpuhui.gic.wxapp.activety.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum ActivityEnums {

    TO_BE_REVIEWED(0,"待审核"),
    PASS(1,"审核通过"),
    REFUSE(2,"审核未通过"),
    SIGN_UP(3,"已报名"),
    HAND_CUTOFF(4,"手动截止报名"),
    END(5,"已结束"),
    SIGN_UP_ING(6,"报名中"),
    SIGN_UP_END(7,"报名时间已截止"),
    WAIT_JOIN(8,"待参加"),

    ;

    private Integer code;

    private String describe;

    ActivityEnums(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
