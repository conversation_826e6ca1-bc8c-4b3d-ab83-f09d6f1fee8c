package com.lvpuhui.gic.wxapp.base.storage;

import cn.hutool.extra.spring.SpringUtil;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023年04月07日 16:50:00
 */
@Component
public class StorageServiceFactory {

    @Resource
    private GlAppletConfigService glAppletConfigService;

    public StorageService getStorageService(){
        String paramValue = glAppletConfigService.getStorageType();
        return SpringUtil.getBean(paramValue + "StorageService", StorageService.class);
    }
}
