package com.lvpuhui.gic.wxapp.carbon.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 补贴档位主体枚举
 * <AUTHOR>
 * @since 2023年03月22日 17:26:00
 */
@Getter
public enum SubsidyGearSubject {

    PEOPLE(0,"按业主"),
    RECEIVE(1,"按地"),
    ;

    private Integer code;

    private String describe;

    SubsidyGearSubject(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static String getDescribe(Integer status){
        return Arrays.stream(SubsidyGearSubject.values())
                .filter(subsidyGearSubject -> subsidyGearSubject.getCode().equals(status))
                .findFirst()
                .get().getDescribe();
    }
}
