package com.lvpuhui.gic.wxapp.treeplanting.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Data Object for Tree Planting Information.
 */
@Data
@TableName("gl_tree_planting")
public class TreePlantingDO {
    /**
     * 植树ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 植树人Id
     */
    private Long planterId;

    /**
     * 单位名称
     */
    private String unitName;


    /**
     * 植树人昵称
     */
    private String planterNickname;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 植树前图片的URL
     */
    private String treeImageBeforeUrl;

    /**
     * 植树后图片的URL
     */
    private String treeImageAfterUrl;

    /**
     * 植树地理位置
     */
    private String location;

    /**
     * 植树位置纬度
     */
    private Double latitude;

    /**
     * 植树位置经度
     */
    private Double longitude;

    /**
     * 植树时间
     */
    private LocalDateTime plantingTime;

    /**
     * 植树棵树
     */
    private Integer treeCount;

    /**
     * 树种
     */
    private String treeSpecies;

    /**
     * 树名
     */
    private String treeName;

    private BigDecimal emission;

    /**
     * 处理状态：0 待处理，1，处理完成，2 驳回
     */
    private Integer status;

    /**
     * 处理人ID
     */
    private Long handlerId;

    /**
     * 处理人
     */
    private String handlerName;

    /**
     * 处理时间
     */
    private LocalDateTime handlingTime;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime updated;
}
