package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.homepage.dao.*;
import com.lvpuhui.gic.wxapp.homepage.entity.*;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.homepage.dto.CheckTickDto;
import com.lvpuhui.gic.wxapp.homepage.dto.TickoffDto;
import com.lvpuhui.gic.wxapp.homepage.dto.CheckTick;
import com.lvpuhui.gic.wxapp.homepage.dto.Tickoff;
import com.lvpuhui.gic.wxapp.homepage.dto.BaiduMapSearchVo;
import com.lvpuhui.gic.wxapp.homepage.dto.GaoDeMaoSearchVo;
import com.lvpuhui.gic.wxapp.homepage.dto.MapSearchVo;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.homepage.enums.TickoffAvailable;
import com.lvpuhui.gic.wxapp.homepage.enums.TickoffTypeSwitch;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.homepage.service.GlTickoffService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.homepage.utils.CoordinatesTransformUtil;
import com.lvpuhui.gic.wxapp.homepage.utils.DistanceUtil;
import com.lvpuhui.gic.wxapp.homepage.utils.TickOnlyCertsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <p>
 * 打卡数据 (用户扫码) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-05
 */
@Slf4j
@Service
public class GlTickoffServiceImpl extends ServiceImpl<GlTickoffDao, GlTickoff> implements GlTickoffService {

    @Autowired
    private GlTickoffTypeDao glTickoffTypeDao;

    @Autowired
    private GlUserDao glUserDao;

    @Autowired
    private GlPointsService glPointsService;

    @Resource
    private GlOutletsPositionDao glOutletsPositionDao;

    @Resource
    private GlTickoffJudgeDao glTickoffJudgeDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Resource
    private GlTickWhiteDao glTickWhiteDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Tickoff> punchClock(TickoffDto tickoffDto) {
        GlTickoffType glTickoffType = glTickoffTypeDao.selectById(tickoffDto.getId());
        if(Objects.isNull(glTickoffType)){
            throw new GicWxAppException("您所选的打卡错误,请确认");
        }
        if(Deleted.DELETED.getDeleted().equals(glTickoffType.getDeleted())){
            throw new GicWxAppException("您来晚了，此打卡已取消");
        }
        tickoffDto.setMobileSha256(UserUtils.getMobileSha256());
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,tickoffDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }

        String onlyCerts = TickOnlyCertsUtils.generateOnlyCerts(glUser.getId(),glTickoffType.getId(),glTickoffType.getType());
        LambdaQueryWrapper<GlTickoff> glTickoffLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTickoffLambdaQueryWrapper.eq(GlTickoff::getOnlyCerts,onlyCerts);
        glTickoffLambdaQueryWrapper.last("limit 1");
        GlTickoff glTickoffExist = baseMapper.selectOne(glTickoffLambdaQueryWrapper);
        if(Objects.nonNull(glTickoffExist)){
            throw new GicWxAppException("您已打过卡,请勿重复打卡");
        }
        LambdaQueryWrapper<GlTickoffJudge> glTickoffJudgeLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTickoffJudgeLambdaQueryWrapper.eq(GlTickoffJudge::getMobileSha256,tickoffDto.getMobileSha256());
        glTickoffJudgeLambdaQueryWrapper.orderByDesc(GlTickoffJudge::getId);
        glTickoffJudgeLambdaQueryWrapper.last("limit 1");
        GlTickoffJudge glTickoffJudge = glTickoffJudgeDao.selectOne(glTickoffJudgeLambdaQueryWrapper);

        GlTickoff glTickoff = new GlTickoff();
        if(Objects.nonNull(glTickoffType.getPositionSwitch())
                && TickoffTypeSwitch.ON.getState().equals(glTickoffType.getPositionSwitch())){
            // 先搜索高德如果搜索不到在搜索数据库
            String searchContent = glTickoffType.getPositionKeyword();
            Double wgs84Lat = BigDecimal.valueOf(tickoffDto.getLatitude()).setScale(6, RoundingMode.DOWN).doubleValue();
            Double wgs84Lon = BigDecimal.valueOf(tickoffDto.getLongitude()).setScale(6, RoundingMode.DOWN).doubleValue();
            Integer distance = glTickoffType.getDistance();
            List<MapSearchVo> mapSearchVoList = getGaoDeMapSearchResult(searchContent, wgs84Lat, wgs84Lon, distance);
            if(CollUtil.isNotEmpty(mapSearchVoList)){
                // 如果高德搜索到了结果
                for (MapSearchVo mapSearchVo : mapSearchVoList) {
                    // 地图搜索会进行分词,返回的结果不一定是符合的,这里在进行过滤一下
                    if(!mapSearchVo.getName().contains(searchContent)
                            && !mapSearchVo.getAddress().contains(searchContent)){
                        continue;
                    }
                    glTickoff.setOutletsName(mapSearchVo.getName());
                    glTickoff.setOutletsLatitude(mapSearchVo.getLat());
                    glTickoff.setOutletsLongitude(mapSearchVo.getLng());
                    glTickoff.setMapType(0);
                    break;
                }
            }
            if(StrUtil.isBlank(glTickoff.getOutletsName())){
                // 高德地图搜索未出现合适结果 进行数据库维护的门店比对
                MapSearchVo searchVo = getOutlet(wgs84Lat, wgs84Lon, glTickoffType.getId(), distance);
                if(Objects.isNull(searchVo)){
                    throw new GicWxAppException("抱歉,您打卡位置不在符合范围内,打卡失败");
                }
                glTickoff.setOutletsName(searchVo.getName());
                glTickoff.setOutletsLatitude(searchVo.getLat());
                glTickoff.setOutletsLongitude(searchVo.getLng());
                glTickoff.setMapType(2);
            }
            LambdaQueryWrapper<GlTickWhite> glTickWhiteLambdaQueryWrapper = Wrappers.lambdaQuery();
            glTickWhiteLambdaQueryWrapper.eq(GlTickWhite::getMobileSha256,tickoffDto.getMobileSha256());
            glTickWhiteLambdaQueryWrapper.last("limit 1");
            Integer tickWhiteCount = glTickWhiteDao.selectCount(glTickWhiteLambdaQueryWrapper);
            if(Objects.isNull(tickWhiteCount) || tickWhiteCount <= 0){
                // 不在白名单内 需要做判断
                // 获取锁定时间/单位秒
                Integer tickLockDuration = glAppletConfigService.getTickLockDuration();
                // 获取打卡每公里间隔时间/单位秒
                Integer tickLimit = glAppletConfigService.getTickLimit();
                // 最小间隔距离,大于此距离的做判断  否则不做距离判断
                Integer minDistance = glAppletConfigService.getTickMinDistance();

                Map<String, Double> transform = CoordinatesTransformUtil.transform(wgs84Lat, wgs84Lon);
                log.info("地球坐标系(WGS - 84)转火星坐标系(GCJ):{}", JSON.toJSONString(transform));
                Double lat = transform.get("lat");
                Double lon = transform.get("lon");

                if(Objects.nonNull(glTickoffJudge)
                        && Objects.nonNull(glTickoffJudge.getLockTime())
                        && DateUtil.compare(glTickoffJudge.getLockTime(),new Date()) < 0){
                    Long glTickoffJudgeId = glTickoffJudge.getId();
                    glTickoffJudge = null;
                    glTickoffJudgeDao.deleteById(glTickoffJudgeId);
                }

                if(Objects.nonNull(glTickoffJudge)){
                    if(Objects.nonNull(glTickoffJudge.getLockTime())
                            && DateUtil.compare(glTickoffJudge.getLockTime(),new Date()) > 0){
                        throw new GicWxAppException("打卡过于频繁,请明日再试");
                    }
                    Date lastTime = glTickoffJudge.getLastTime();
                    // 获取间隔距离
                    Double intervalDistance = DistanceUtil.getDistance(glTickoffJudge.getLastLatitude(),glTickoffJudge.getLastLongitude(),lat,lon);
                    log.info("间隔距离:{}",intervalDistance);
                    if(intervalDistance > minDistance){
                        double minute = NumberUtil.div(intervalDistance,Double.valueOf(1000D));
                        // 必须间隔多少秒
                        double second = minute * tickLimit;
                        long between = DateUtil.between(lastTime, new Date(), DateUnit.SECOND);
                        log.info("必须间隔多少秒:{},此用户本次打卡和上次打卡间隔秒数:{}",second,between);
                        if(between < second){
                            DateTime lockDateTime = DateUtil.offsetSecond(new Date(), tickLockDuration);
                            glTickoffJudge.setLockTime(lockDateTime);
                            glTickoffJudgeDao.updateById(glTickoffJudge);
                            return R.failed("打卡过于频繁,请明日再试");
                        }else {
                            glTickoffJudge.setLastLatitude(lat);
                            glTickoffJudge.setLastLongitude(lon);
                            glTickoffJudge.setLastTime(new Date());
                            glTickoffJudge.setUpdated(new Date());
                            glTickoffJudgeDao.updateById(glTickoffJudge);
                        }
                    }else {
                        log.info("本次打卡距离小于设置的距离用户:{},打卡通过",tickoffDto.getMobileSha256());
                        glTickoffJudge.setLastLatitude(lat);
                        glTickoffJudge.setLastLongitude(lon);
                        glTickoffJudge.setLastTime(new Date());
                        glTickoffJudge.setUpdated(new Date());
                        glTickoffJudgeDao.updateById(glTickoffJudge);
                    }
                }else {
                    glTickoffJudge = new GlTickoffJudge();
                    glTickoffJudge.setLastLatitude(lat);
                    glTickoffJudge.setLastLongitude(lon);
                    glTickoffJudge.setMobileSha256(tickoffDto.getMobileSha256());
                    glTickoffJudge.setLastTime(new Date());
                    glTickoffJudgeDao.insert(glTickoffJudge);
                }
            }else {
                log.info("此用户在白名单中,不做打卡校验:{}",tickoffDto.getMobileSha256());
            }
        }
        glTickoff.setTypeId(tickoffDto.getId());
        glTickoff.setUserId(glUser.getId());
        glTickoff.setMobileSha256(tickoffDto.getMobileSha256());
        glTickoff.setLatitude(tickoffDto.getLatitude());
        glTickoff.setLongitude(tickoffDto.getLongitude());
        glTickoff.setCreated(new Date());
        glTickoff.setOnlyCerts(onlyCerts);
        try {
            baseMapper.insert(glTickoff);
        }catch (DuplicateKeyException e){
            e.printStackTrace();
            throw new GicWxAppException("您已打过卡,请勿重复打卡");
        }

        LambdaUpdateWrapper<GlTickoffType> tickoffTypeLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        tickoffTypeLambdaUpdateWrapper.eq(GlTickoffType::getId,glTickoffType.getId());

        Integer consumed = Objects.isNull(glTickoffType.getConsumed())?1:glTickoffType.getConsumed()+1;
        GlTickoffType updateGlTickoffType = new GlTickoffType();
        updateGlTickoffType.setConsumed(consumed);
        glTickoffTypeDao.update(updateGlTickoffType,tickoffTypeLambdaUpdateWrapper);

        Long companyId = glTickoffType.getCompanyId();
        glPointsService.grantTickoff(glTickoff,companyId);

        Tickoff tickoff = new Tickoff();
        tickoff.setPoint(glTickoffType.getPoint());
        tickoff.setOutletsName(glTickoff.getOutletsName());
        return R.ok(tickoff);
    }

    @Override
    public CheckTick checkTick(CheckTickDto checkTickDto) {
        GlTickoffType glTickoffType = glTickoffTypeDao.selectById(checkTickDto.getId());
        if(Objects.isNull(glTickoffType)){
            throw new GicWxAppException("您所选的打卡错误,请确认");
        }
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,checkTickDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        CheckTick checkTick = new CheckTick();
        checkTick.setAvailable(TickoffAvailable.UN_TICK.getState());

        String onlyCerts = TickOnlyCertsUtils.generateOnlyCerts(glUser.getId(),glTickoffType.getId(),glTickoffType.getType());
        LambdaQueryWrapper<GlTickoff> glTickoffLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTickoffLambdaQueryWrapper.select(GlTickoff::getOnlyCerts);
        glTickoffLambdaQueryWrapper.eq(GlTickoff::getOnlyCerts,onlyCerts);
        glTickoffLambdaQueryWrapper.last("limit 1");
        GlTickoff glTickoffExist = baseMapper.selectOne(glTickoffLambdaQueryWrapper);
        if(Objects.nonNull(glTickoffExist)){
            checkTick.setAvailable(TickoffAvailable.TICK.getState());
        }
        return checkTick;
    }

    public List<MapSearchVo> getBaiduMapSearchResult(String searchContent,Double wgs84Lat,Double wgs84Lon,Integer distance){
        Map<String, Double> transform = CoordinatesTransformUtil.transform(wgs84Lat, wgs84Lon);
        log.info("地球坐标系(WGS - 84)转火星坐标系(GCJ):{}",JSON.toJSONString(transform));
        Map<String, Double> marsTobaiduMap = CoordinatesTransformUtil.marsTobaidu(transform.get("lat"), transform.get("lon"));
        log.info("火星坐标系(GCJ02)转百度坐标系(bd09II):{}",JSON.toJSONString(marsTobaiduMap));
        Double lat = marsTobaiduMap.get("lat");
        Double lon = marsTobaiduMap.get("lon");
        // 判断地理位置
        String location = lat + "," + lon;
        String mapSearchUrl = Global.MAP_SEARCH_URL;
        HashMap<String, Object> paramMap = Maps.newHashMapWithExpectedSize(8);
        paramMap.put("query",searchContent);
        paramMap.put("output","json");
        paramMap.put("ak",Global.BAIDU_AK);
        paramMap.put("location",location);
        paramMap.put("radius",distance);
        paramMap.put("scope",2);
        paramMap.put("page_size",3);
        paramMap.put("filter","sort_name:distance|sort_rule:1");

        String mapSearchResult = null;
        try {
            mapSearchResult = HttpUtil.get(mapSearchUrl,paramMap);
        }catch (Exception e){
            e.printStackTrace();
            log.info("百度地图获取附近店铺异常:{}",e.getMessage());
            return Lists.newArrayList();
        }
        log.info("百度地图获取附近店铺地址:{},参数:{},结果:{}",mapSearchUrl,JSON.toJSONString(paramMap),mapSearchResult);
        if(StrUtil.isBlank(mapSearchResult)){
            return Lists.newArrayList();
        }
        BaiduMapSearchVo baiduMapSearchVo = JSON.parseObject(mapSearchResult, BaiduMapSearchVo.class);
        if(baiduMapSearchVo.getStatus() != 0){
            return Lists.newArrayList();
        }
        List<BaiduMapSearchVo.ResultsBean> results = baiduMapSearchVo.getResults();
        if(CollUtil.isEmpty(results)){
            return Lists.newArrayList();
        }
        List<MapSearchVo> mapSearchVos = Lists.newArrayListWithCapacity(results.size());
        for (BaiduMapSearchVo.ResultsBean resultsBean : results) {
            MapSearchVo mapSearchVo = new MapSearchVo();
            mapSearchVo.setName(resultsBean.getName());
            mapSearchVo.setAddress(StrUtil.isBlank(resultsBean.getAddress())?"":resultsBean.getAddress());
            mapSearchVo.setLat(resultsBean.getLocation().getLat());
            mapSearchVo.setLng(resultsBean.getLocation().getLng());
            mapSearchVos.add(mapSearchVo);
        }
        return mapSearchVos;
    }

    public List<MapSearchVo> getGaoDeMapSearchResult(String searchContent,Double wgs84Lat,Double wgs84Lon,Integer distance) {
        Map<String, Double> transform = CoordinatesTransformUtil.transform(wgs84Lat, wgs84Lon);
        log.info("地球坐标系(WGS - 84)转火星坐标系(GCJ):{}", JSON.toJSONString(transform));
        Double lat = transform.get("lat");
        Double lon = transform.get("lon");
        // 判断地理位置
        String location = lat + "," + lon;

        String mapSearchUrl = Global.GAO_DE_MAP_SEARCH_URL;
        HashMap<String, Object> paramMap = Maps.newHashMapWithExpectedSize(8);
        paramMap.put("keywords",searchContent);
        paramMap.put("key",Global.GAO_DE_KEY);
        paramMap.put("location",location);
        paramMap.put("radius",distance);
        paramMap.put("page_size",3);
        String mapSearchResult = null;
        try {
            mapSearchResult = HttpUtil.get(mapSearchUrl,paramMap);
        }catch (Exception e){
            e.printStackTrace();
            log.info("高德地图获取附近店铺异常:{}",e.getMessage());
            return Lists.newArrayList();
        }
        log.info("高德地图获取附近店铺地址:{},参数:{},结果:{}",mapSearchUrl,JSON.toJSONString(paramMap),mapSearchResult);
        if(StrUtil.isBlank(mapSearchResult)){
            return Lists.newArrayList();
        }
        GaoDeMaoSearchVo gaoDeMaoSearchVo = JSON.parseObject(mapSearchResult, GaoDeMaoSearchVo.class);
        if(!gaoDeMaoSearchVo.getStatus().equals("1")){
            return Lists.newArrayList();
        }
        List<GaoDeMaoSearchVo.PoisBean> pois = gaoDeMaoSearchVo.getPois();
        if(CollUtil.isEmpty(pois)){
            return Lists.newArrayList();
        }
        List<MapSearchVo> mapSearchVos = Lists.newArrayListWithCapacity(pois.size());
        for (GaoDeMaoSearchVo.PoisBean poisBean : pois) {
            MapSearchVo mapSearchVo = new MapSearchVo();
            mapSearchVo.setName(poisBean.getName().replaceAll(" ",""));
            mapSearchVo.setAddress(poisBean.getAddress().replaceAll(" ",""));
            String[] locations = poisBean.getLocation().split(",");
            mapSearchVo.setLat(Double.parseDouble(locations[1]));
            mapSearchVo.setLng(Double.parseDouble(locations[0]));
            mapSearchVos.add(mapSearchVo);
        }
        return mapSearchVos;
    }

    /**
     * 根据数据库维护的门店判断距离
     * @param wgs84Lat 纬度
     * @param wgs84Lon 经度
     * @param typeId 打卡类型ID
     * @param distance 打卡有效距离
     * @return 计算得到的最近的结果
     */
    public MapSearchVo getOutlet(Double wgs84Lat,Double wgs84Lon,Long typeId,Integer distance){
        LambdaQueryWrapper<GlOutletsPosition> glOutletsPositionQuery = Wrappers.lambdaQuery();
        glOutletsPositionQuery.eq(GlOutletsPosition::getTypeId,typeId);
        glOutletsPositionQuery.eq(GlOutletsPosition::getDeleted,Deleted.UN_DELETE.getDeleted());
        List<GlOutletsPosition> glOutletsPositions = glOutletsPositionDao.selectList(glOutletsPositionQuery);
        if(CollUtil.isEmpty(glOutletsPositions)){
            return null;
        }
        Map<String, Double> transform = CoordinatesTransformUtil.transform(wgs84Lat, wgs84Lon);
        log.info("地球坐标系(WGS - 84)转火星坐标系(GCJ):{}", JSON.toJSONString(transform));
        Double lat = transform.get("lat");
        Double lon = transform.get("lon");

        TreeMap<Double,GlOutletsPosition> outletsPositionTreeMap = new TreeMap<>();
        for (GlOutletsPosition glOutletsPosition : glOutletsPositions) {
            double calcDistance = DistanceUtil.getDistance(lat, lon, glOutletsPosition.getLatitude(), glOutletsPosition.getLongitude());
            if(calcDistance <= distance){
                outletsPositionTreeMap.put(calcDistance,glOutletsPosition);
            }
        }
        if(MapUtil.isEmpty(outletsPositionTreeMap)){
            return null;
        }
        MapSearchVo mapSearchVo = new MapSearchVo();
        GlOutletsPosition glOutletsPosition = outletsPositionTreeMap.firstEntry().getValue();
        mapSearchVo.setName(glOutletsPosition.getOutletsName());
        mapSearchVo.setLat(glOutletsPosition.getLatitude());
        mapSearchVo.setLng(glOutletsPosition.getLongitude());
        return mapSearchVo;
    }
}
