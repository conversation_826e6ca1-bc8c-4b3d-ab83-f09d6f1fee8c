package com.lvpuhui.gic.wxapp.base.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lvpuhui.gic.wxapp.base.constant.AppletConfigConstant;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.base.dao.GlAppletConfigDao;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.homepage.dto.RankConfigDto;
import com.lvpuhui.gic.wxapp.homepage.dto.RankGoodsConfigDto;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 系统配置信息表(GlAppletConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-07 14:41:32
 */
@Service("glAppletConfigService")
@Slf4j
public class GlAppletConfigServiceImpl extends ServiceImpl<GlAppletConfigDao, GlAppletConfig> implements GlAppletConfigService {

    @Override
    public double getDoubleValue(String key) {
        GlAppletConfig config = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.CONFIG_POINT_WORTH_CONSUMPTION));
        if(null == config){
            throw new GicWxAppException("config empty");
        }
        double rate = Double.parseDouble(config.getParamValue());
        return rate;
    }

    @Override
    public double getBehaviorToPoints() {
        GlAppletConfig consumption = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.CONFIG_POINT_WORTH_CONSUMPTION));
        GlAppletConfig behavior = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.CONFIG_POINT_WORTH_BEHAVIOR));
        if(null == consumption || null == behavior){
            throw new GicWxAppException("config empty");
        }
        double consumptionRate = Double.parseDouble(consumption.getParamValue());
        double behaviorRate = Double.parseDouble(behavior.getParamValue());
        return NumberUtil.round(NumberUtil.div(behaviorRate,consumptionRate),6).doubleValue();
    }

    @Override
    public double getPoints(double money) {
        GlAppletConfig consumption = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.CONFIG_POINT_WORTH_CONSUMPTION));
        double consumptionRate = Double.parseDouble(consumption.getParamValue());
        double value =  NumberUtil.round(NumberUtil.div(money,consumptionRate),6).doubleValue();
        log.info(" money {} -> {}",money,value);
        return value;
    }

    @Override
    public RankConfigDto getRankConfig() {
        GlAppletConfig rankSettingsConfig = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.RANK_SETTINGS));
        return JSON.parseObject(rankSettingsConfig.getParamValue(), RankConfigDto.class);
    }

    @Override
    public RankGoodsConfigDto getRankGoodsConfig() {
        GlAppletConfig rankSettingsConfig = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.RANK_GOODS_LIMIT));
        return JSON.parseObject(rankSettingsConfig.getParamValue(), RankGoodsConfigDto.class);
    }

    @Override
    public Integer getTickLockDuration() {
        GlAppletConfig glAppletConfig = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.TICK_LOCK_DURATION));
        return Integer.valueOf(glAppletConfig.getParamValue());
    }

    @Override
    public Integer getTickLimit() {
        GlAppletConfig glAppletConfig = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.TICK_INTERVAL_DURATION));
        return Integer.valueOf(glAppletConfig.getParamValue());
    }

    @Override
    public Integer getTickMinDistance() {
        GlAppletConfig glAppletConfig = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.TICK_MIN_DISTANCE));
        return Integer.valueOf(glAppletConfig.getParamValue());
    }

    @Override
    public String getImagePrefix() {
        GlAppletConfig glAppletConfig = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.IMAGE_PREFIX));
        return glAppletConfig.getParamValue();
    }

    @Override
    public String getStorageType() {
        GlAppletConfig glAppletConfig = getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, AppletConfigConstant.STORAGE));
        return glAppletConfig.getParamValue();
    }
}