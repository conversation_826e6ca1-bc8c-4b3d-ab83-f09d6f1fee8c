package com.lvpuhui.gic.wxapp.homepage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lvpuhui.gic.wxapp.homepage.dao.GlPurchaseIntentionDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlPurchaseSubjectMapper;
import com.lvpuhui.gic.wxapp.homepage.dto.QueryPurchaseSubjectListVo;
import com.lvpuhui.gic.wxapp.homepage.entity.GlPurchaseIntention;
import com.lvpuhui.gic.wxapp.homepage.dto.PurchaseIntetionDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlPurchaseSubjectDO;
import com.lvpuhui.gic.wxapp.homepage.service.GlPurchaseIntentionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 购买意愿实现类
 * <AUTHOR>
 */
@Service
public class GlPurchaseIntentionImpl extends ServiceImpl<GlPurchaseIntentionDao, GlPurchaseIntention> implements GlPurchaseIntentionService {


    @Autowired
    private GlPurchaseIntentionDao glPurchaseIntentionDao;

    @Autowired
    private GlPurchaseSubjectMapper glPurchaseSubjectMapper;

    private final static Logger logger = LoggerFactory.getLogger(GlPurchaseIntentionImpl.class);



    @Override
    public void savePurchase(PurchaseIntetionDto purchaseIntetionDto) {
        if(null != purchaseIntetionDto){
            GlPurchaseIntention glPurchaseIntention = new GlPurchaseIntention();
            glPurchaseIntention.setAmount(purchaseIntetionDto.getAmount());
            glPurchaseIntention.setSubjectId(purchaseIntetionDto.getSubjectId());
            glPurchaseIntention.setIndustry(purchaseIntetionDto.getIndustry());
            glPurchaseIntention.setMobileSha256(purchaseIntetionDto.getMobileSha256());
            glPurchaseIntention.setOperation(purchaseIntetionDto.getOperation());
            glPurchaseIntention.setRealName(purchaseIntetionDto.getRealName());
            glPurchaseIntention.setZone(purchaseIntetionDto.getZone());
            glPurchaseIntention.setUserCompany(purchaseIntetionDto.getUserCompany());
            glPurchaseIntention.setPurpose(purchaseIntetionDto.getPurpose());
            glPurchaseIntention.setIsDelete(0);
            glPurchaseIntention.setCreated(new Date());
            logger.info("glPurchaseIntention的参数为{}",glPurchaseIntention);
            int sum = glPurchaseIntentionDao.insert(glPurchaseIntention);
            if(sum <=0){
                logger.info("保存失败");
            }
        }else {
            logger.info("purchaseIntetionDto的参数为空");
        }
    }

    @Override
    public List<QueryPurchaseSubjectListVo> queryPurchaseSubjectList() {
        List<QueryPurchaseSubjectListVo> vo = new ArrayList<>();
        LambdaQueryWrapper<GlPurchaseSubjectDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(GlPurchaseSubjectDO ::getDeleted,0);
        queryWrapper.orderByDesc(GlPurchaseSubjectDO :: getRank);
        List<GlPurchaseSubjectDO> list = glPurchaseSubjectMapper.selectList(queryWrapper);
        if(list.size() > 0){
            for (GlPurchaseSubjectDO item:list) {
                QueryPurchaseSubjectListVo queryPurchaseSubjectListVo = new QueryPurchaseSubjectListVo();
                queryPurchaseSubjectListVo.setName(item.getName());
                queryPurchaseSubjectListVo.setRank(item.getRank());
                queryPurchaseSubjectListVo.setDescription(item.getDescription());
                queryPurchaseSubjectListVo.setId(item.getId());
                vo.add(queryPurchaseSubjectListVo);
            }
        }
        return vo;
    }
}
