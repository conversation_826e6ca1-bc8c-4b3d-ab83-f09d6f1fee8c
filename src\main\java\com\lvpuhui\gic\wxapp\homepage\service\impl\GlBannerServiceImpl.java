package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlBannerDao;
import com.lvpuhui.gic.wxapp.homepage.entity.GlBanner;
import com.lvpuhui.gic.wxapp.homepage.dto.Banner;
import com.lvpuhui.gic.wxapp.homepage.enums.BannerState;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.homepage.service.GlBannerService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图表(GlBanner)表服务实现类
 * <AUTHOR>
 * @since 2022年05月05日 18:10:00
 */
@Service("glBannerService")
public class GlBannerServiceImpl extends ServiceImpl<GlBannerDao, GlBanner> implements GlBannerService {

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Override
    public List<Banner> banner() {
        String imagePrefix = glAppletConfigService.getImagePrefix();

        // 查询未删除、已发布的banner数据
        LambdaQueryWrapper<GlBanner> glBannerQuery = Wrappers.lambdaQuery();
        glBannerQuery.eq(GlBanner::getDeleted, Deleted.UN_DELETE.getDeleted());
        glBannerQuery.eq(GlBanner::getState, BannerState.PUBLISH.getState());
        glBannerQuery.orderByDesc(GlBanner::getSequence);
        List<GlBanner> glBanners = baseMapper.selectList(glBannerQuery);
        if(CollUtil.isEmpty(glBanners)){
            return Lists.newArrayList();
        }
        // 进行类转换并返回
        return glBanners.stream().map(glBanner ->
                new Banner(glBanner.getId(), imagePrefix + glBanner.getCoverImage(), glBanner.getTitle(), glBanner.getJumpUrl(),glBanner.getAppId()))
                .collect(Collectors.toList());
    }
}
