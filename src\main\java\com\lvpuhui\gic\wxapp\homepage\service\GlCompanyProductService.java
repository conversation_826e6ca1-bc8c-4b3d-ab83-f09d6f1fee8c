package com.lvpuhui.gic.wxapp.homepage.service;

import com.lvpuhui.gic.wxapp.homepage.entity.GlCompanyProduct;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.dto.BigCategories;
import com.lvpuhui.gic.wxapp.homepage.dto.SceneCategories;

import java.util.List;

/**
 * 企业产品表(GlCompanyProduct)表服务接口
 * <AUTHOR>
 * @since 2022年5月5日 19:23:02
 */
public interface GlCompanyProductService extends IService<GlCompanyProduct> {

    /**
     * 绿色消费领积分(首页)-这是一个企业类型+企业集合
     */
    List<BigCategories> getCompanys();

    /**
     * 首页公司小程序展示列表
     */
    List<SceneCategories> companyList();
}
