<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.team.dao.GlTeamRankDao">
    <update id="updateDeductEmission">
        UPDATE gl_team_rank SET emission = IF(emission - #{emission} &lt; 0,0,emission - #{emission}) WHERE team_id = #{teamId}
    </update>
    <update id="updateIncrementEmission">
        UPDATE gl_team_rank SET emission = IFNULL(emission,0) + #{emission} WHERE team_id = #{teamId}
    </update>

    <select id="selectTeamRanks" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamRankListVo">
        SELECT
            gtr.team_id,
            gtr.`rank`,
            gtr.emission,
            gt.team_name,
            gt.team_logo,
            gt.certification_status as certification
        FROM
            gl_team_rank gtr
                INNER JOIN gl_team gt ON gtr.team_id = gt.id
        WHERE gt.`status` = 0
        ORDER BY -gtr.`rank` DESC
            LIMIT 20
    </select>
    <select id="selectTeamRankByTeamId" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamRankListVo">
        SELECT
            gtr.team_id,
            IF(gt.`status` != 0,NULL,gtr.`rank`) AS `rank`,
            gtr.emission,
            gt.team_name,
            gt.team_logo,
            gt.certification
        FROM
            gl_team gt
                INNER JOIN gl_team_rank gtr ON gt.id = gtr.team_id
        WHERE gt.id = #{teamId}
    </select>
</mapper>

