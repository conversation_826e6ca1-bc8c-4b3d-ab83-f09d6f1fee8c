package com.lvpuhui.gic.wxapp.infrastructure.utils;


import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class CalcUtil {
    public static final String[] UNIT_NAMES = new String[]{"g", "kg", "t"};

    public static double getRatio(double v1,double v2){
        return NumberUtil.mul(NumberUtil.div(v1,v2,4) ,100);
    }

    /**
     * 重量单位转换
     * @param size 大小/单位g
     * @return 转换后的字符串
     */
    public static String weightFormat(double size) {
        if (size <= 0) {
            return "0g";
        }
        int digitGroups = Math.min(UNIT_NAMES.length-1, (int) (Math.log10(size) / Math.log10(1000)));
        BigDecimal value = BigDecimal.valueOf(size / Math.pow(1000, digitGroups)).setScale(2, RoundingMode.HALF_UP);
        if(size >= 1000){
            return value + UNIT_NAMES[digitGroups];
        }
        return new DecimalFormat("#,##0.###")
                .format(value.doubleValue()) + UNIT_NAMES[digitGroups];
    }

    public static Double calcRankRadio(Long currentRank,Long lastRank){
        if(Objects.isNull(currentRank) || Objects.isNull(lastRank)){
            return null;
        }
        if(currentRank == 0){
            return 100D;
        }
        if(currentRank <= -1){
            return 0D;
        }
        if(currentRank.equals(lastRank)){
            return 0D;
        }
        BigDecimal lastRankBigDecimal = BigDecimal.valueOf(lastRank);
        BigDecimal currentRankBigDecimal = BigDecimal.valueOf(currentRank);

        BigDecimal divide = lastRankBigDecimal.subtract(currentRankBigDecimal).divide(lastRankBigDecimal,2, RoundingMode.DOWN).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.DOWN);
        return divide.doubleValue();
    }
}
