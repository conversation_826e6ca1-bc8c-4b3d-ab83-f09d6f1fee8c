package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlTickoffDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlTickoffTypeDao;
import com.lvpuhui.gic.wxapp.homepage.dto.Ticks;
import com.lvpuhui.gic.wxapp.homepage.dto.TicksDetail;
import com.lvpuhui.gic.wxapp.homepage.dto.TicksDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlTickoff;
import com.lvpuhui.gic.wxapp.homepage.entity.GlTickoffType;
import com.lvpuhui.gic.wxapp.homepage.enums.TickoffAvailable;
import com.lvpuhui.gic.wxapp.homepage.enums.TickoffType;
import com.lvpuhui.gic.wxapp.homepage.enums.TickoffTypeShow;
import com.lvpuhui.gic.wxapp.homepage.enums.TickoffTypeState;
import com.lvpuhui.gic.wxapp.homepage.service.GlTickoffTypeService;
import com.lvpuhui.gic.wxapp.homepage.utils.TickOnlyCertsUtils;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 打卡类别表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-05
 */
@Service
public class GlTickoffTypeServiceImpl extends ServiceImpl<GlTickoffTypeDao, GlTickoffType> implements GlTickoffTypeService {

    @Resource
    private GlTickoffDao glTickoffDao;

    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Override
    public List<Ticks> ticks(TicksDto ticksDto) {
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,ticksDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        // 查询所有的打卡
        LambdaQueryWrapper<GlTickoffType> glTickoffTypeQuery = Wrappers.lambdaQuery();
        glTickoffTypeQuery.eq(GlTickoffType::getDeleted, Deleted.UN_DELETE.getDeleted());
        glTickoffTypeQuery.eq(GlTickoffType::getIsShow, TickoffTypeShow.SHOW.getShow());
        glTickoffTypeQuery.eq(GlTickoffType::getState, TickoffTypeState.PUBLISH.getState());
        glTickoffTypeQuery.orderByDesc(GlTickoffType::getSequence);
        List<GlTickoffType> glTickoffTypes = baseMapper.selectList(glTickoffTypeQuery);
        if(CollUtil.isEmpty(glTickoffTypes)){
            return Lists.newArrayList();
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        // 获取仅一次和每天不同类型的打卡类型ID
        List<Long> onceTickoffTypeIds = glTickoffTypes
                .stream()
                .filter(glTickoffType -> TickoffType.ONCE.getType().equals(glTickoffType.getType()))
                .map(GlTickoffType::getId)
                .collect(Collectors.toList());
        List<Long> everyDayTickoffTypeIds = glTickoffTypes
                .stream()
                .filter(glTickoffType -> TickoffType.EVERY_DAY.getType().equals(glTickoffType.getType()))
                .map(GlTickoffType::getId)
                .collect(Collectors.toList());

        // 查询当前用户是否已经打卡 需要区分仅一次和每天  不同类型的
        List<String> onceOnlyCertsList = TickOnlyCertsUtils.generateOnlyCerts(glUser.getId(), onceTickoffTypeIds, TickoffType.ONCE.getType());
        List<String> everyOnlyCertsList = TickOnlyCertsUtils.generateOnlyCerts(glUser.getId(), everyDayTickoffTypeIds, TickoffType.EVERY_DAY.getType());
        onceOnlyCertsList.addAll(everyOnlyCertsList);

        LambdaQueryWrapper<GlTickoff> glTickoffQuery = Wrappers.lambdaQuery();
        glTickoffQuery.in(GlTickoff::getOnlyCerts,onceOnlyCertsList);
        List<GlTickoff> glTickoffs = glTickoffDao.selectList(glTickoffQuery);

        Map<Long, GlTickoff> typeIdTickoffEntityMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glTickoffs)){
            typeIdTickoffEntityMap = glTickoffs.stream()
                    .collect(Collectors.toMap(GlTickoff::getTypeId, Function.identity(), (k1, k2) -> k1));
        }

        List<Ticks> ticksList = Lists.newArrayListWithCapacity(glTickoffTypes.size());
        for (GlTickoffType glTickoffType : glTickoffTypes) {
            Ticks ticks = new Ticks();
            BeanUtil.copyProperties(glTickoffType,ticks);
            ticks.setAvailable(TickoffAvailable.UN_TICK.getState());
            if(typeIdTickoffEntityMap.containsKey(glTickoffType.getId())){
                ticks.setAvailable(TickoffAvailable.TICK.getState());
            }
            ticks.setAfterImage(imagePrefix + glTickoffType.getAfterImage());
            ticks.setCoverImage(imagePrefix + glTickoffType.getCoverImage());
            ticks.setBeforeImage(imagePrefix + glTickoffType.getBeforeImage());
            ticksList.add(ticks);
        }
        return ticksList;
    }

    @Override
    public TicksDetail ticksDetail(Long tickId) {
        LambdaQueryWrapper<GlTickoffType> glTickoffTypeQuery = Wrappers.lambdaQuery();
        glTickoffTypeQuery.eq(GlTickoffType::getDeleted, Deleted.UN_DELETE.getDeleted());
        glTickoffTypeQuery.eq(GlTickoffType::getState, TickoffTypeState.PUBLISH.getState());
        glTickoffTypeQuery.eq(GlTickoffType::getId, tickId);
        GlTickoffType tickoffType = baseMapper.selectOne(glTickoffTypeQuery);
        if(Objects.isNull(tickoffType)){
            throw new GicWxAppException("打卡不存在或已过期");
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();

        TicksDetail ticksDetail = new TicksDetail();
        BeanUtil.copyProperties(tickoffType,ticksDetail);
        ticksDetail.setAfterImage(imagePrefix + tickoffType.getAfterImage());
        ticksDetail.setCoverImage(imagePrefix + tickoffType.getCoverImage());
        ticksDetail.setBeforeImage(imagePrefix + tickoffType.getBeforeImage());
        return ticksDetail;
    }
}
