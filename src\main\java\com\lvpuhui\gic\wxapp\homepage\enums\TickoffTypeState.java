package com.lvpuhui.gic.wxapp.homepage.enums;

import lombok.Getter;

/**
 * 打卡类型state状态枚举
 * <AUTHOR>
 * @since 2022年05月05日 18:20:00
 */
@Getter
public enum TickoffTypeState {
    SAVE(0,"保存"),
    PUBLISH(1,"发布"),
    ;

    private Integer state;

    private String describe;

    TickoffTypeState(Integer state, String describe) {
        this.state = state;
        this.describe = describe;
    }
}
