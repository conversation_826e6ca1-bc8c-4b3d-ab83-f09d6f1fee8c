package com.lvpuhui.gic.wxapp.pointexchange.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品兑换dto
 * <AUTHOR>
 * @since 2022年05月06日 15:25:00
 */
@Data
public class GoodsExchangeDto {

    /**
     * 商品ID
     */
    @NotNull(message = "请您选择兑换的商品")
    private Long id;

    /**
     * 用户的手机号(sha256加密)
     */
    @NotBlank(message = "请您先进行授权")
    private String mobileSha256;
}
