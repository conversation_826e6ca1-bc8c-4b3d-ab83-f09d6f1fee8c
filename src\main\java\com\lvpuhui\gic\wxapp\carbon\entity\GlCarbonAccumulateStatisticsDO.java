package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 碳汇累积统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_accumulate_statistics")
public class GlCarbonAccumulateStatisticsDO extends Model<GlCarbonAccumulateStatisticsDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 面积/减排量
     */
    private BigDecimal accumulateData;

    /**
     * 类型ID：累积年度碳汇(吨)ID=0 其他是projectId
     */
    private Long projectId;

    /**
     * 类型 0:累积 1：每日
     */
    private Integer type;

    /**
     * 批次号 类型为1才会有
     */
    private String batchNo;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 减排次数
     */
    private Long emissionNumber;

    /**
     * 减排人数
     */
    private Long emissionUserNumber;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
