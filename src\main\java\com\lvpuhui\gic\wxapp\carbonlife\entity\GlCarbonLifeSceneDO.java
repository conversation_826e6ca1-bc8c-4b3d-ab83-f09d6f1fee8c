package com.lvpuhui.gic.wxapp.carbonlife.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 
 * <AUTHOR>
 * @since 2023年10月30日 17:55:00
 */
/**
    * 低碳生活场景表
    */
@Schema(description="低碳生活场景表")
@Data
@TableName(value = "gl_carbon_life_scene")
public class GlCarbonLifeSceneDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
     * 场景名称
     */
    @TableField(value = "`name`")
    @Schema(description="场景名称")
    private String name;

    /**
     * 场景logo
     */
    @TableField(value = "logo_image")
    @Schema(description="场景LOGO")
    private String logoImage;

    /**
     * 场景排序权重,越大越靠前
     */
    @TableField(value = "`sequence`")
    @Schema(description="场景排序权重,越大越靠前")
    private Integer sequence;

    /**
     * 场景减排量注释
     */
    @TableField(value = "description")
    @Schema(description="场景减排量注释")
    private String description;

    /**
     * 场景类别ID
     */
    @TableField(value = "type_id")
    @Schema(description="场景类别ID")
    private Long typeId;

    /**
     * 关联租户ID,多个英文逗号分隔
     */
    @TableField(value = "tenant_id")
    @Schema(description="关联租户ID,多个英文逗号分隔")
    private String tenantId;

    /**
     * 关联产品ID,多个英文逗号分隔
     */
    @TableField(value = "product_id")
    @Schema(description="关联产品ID,多个英文逗号分隔")
    private String productId;

    /**
     * 是否删除   0否 1是
     */
    @TableField(value = "deleted")
    @Schema(description="是否删除   0否 1是")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private LocalDateTime created;

    /**
     * 修改时间
     */
    @TableField(value = "updated")
    @Schema(description="修改时间")
    private LocalDateTime updated;
}