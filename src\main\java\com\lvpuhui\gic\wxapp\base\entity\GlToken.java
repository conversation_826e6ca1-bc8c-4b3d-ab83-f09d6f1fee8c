package com.lvpuhui.gic.wxapp.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 微信access_token表实体类
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
public class GlToken extends Model<GlToken> {

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 微信Open ID
     */
    private String token;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 过期时间
     */
    private Date expired;
}
