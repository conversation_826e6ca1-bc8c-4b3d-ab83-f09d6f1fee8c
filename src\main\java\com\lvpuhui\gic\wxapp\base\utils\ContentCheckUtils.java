package com.lvpuhui.gic.wxapp.base.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.green.model.v20180509.ImageSyncScanRequest;
import com.aliyuncs.green.model.v20180509.TextScanRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.lvpuhui.gic.wxapp.base.utils.aliyun.ClientUploader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 内容检测
 * <AUTHOR>
 * @since 2023年05月25日 17:05:00
 */
@Slf4j
@Component
public class ContentCheckUtils {

    private static final String regionId = "cn-beijing";

    private static final String product = "Green";

    private static final String endPoint = "green.cn-beijing.aliyuncs.com";

    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String secret;

    @Value("${spring.profiles.active}")
    private String profiles;

    public boolean checkImage(String imageUrl){
        log.info("Checking image:{}", imageUrl);
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, secret);
        DefaultProfile.addEndpoint(regionId, product, endPoint);
        // 注意：此处实例化的client尽可能重复使用，提升检测性能。避免重复建立连接。
        IAcsClient client = new DefaultAcsClient(profile);
        ImageSyncScanRequest imageSyncScanRequest = new ImageSyncScanRequest();
        // 指定API返回格式。
        imageSyncScanRequest.setAcceptFormat(FormatType.JSON);
        // 指定请求方法。
        imageSyncScanRequest.setMethod(MethodType.POST);
        imageSyncScanRequest.setEncoding("utf-8");
        // 支持HTTP和HTTPS。
        imageSyncScanRequest.setProtocol(ProtocolType.HTTP);
        JSONObject httpBody = new JSONObject();
        httpBody.put("scenes", Arrays.asList("porn","terrorism"));
        if(!"prod".equals(profiles)){
            ClientUploader clientUploader = ClientUploader.getImageClientUploader(profile, false);
            try{
                // 写入临时文件
                String tmpPath = FileUtil.getTmpDirPath() + File.separator + IdUtil.fastSimpleUUID() + ".png";
                HttpUtil.downloadFile(imageUrl,tmpPath);
                imageUrl = clientUploader.uploadFile(tmpPath);
                FileUtil.del(tmpPath);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        JSONObject task = new JSONObject();
        task.put("dataId", IdUtil.fastSimpleUUID());
        task.put("url",imageUrl);
        task.put("time", new Date());
        httpBody.put("tasks", Arrays.asList(task));
        imageSyncScanRequest.setHttpContent(StringUtils.getBytesUtf8(httpBody.toJSONString()),"UTF-8", FormatType.JSON);
        imageSyncScanRequest.setConnectTimeout(3000);
        imageSyncScanRequest.setReadTimeout(10000);
        HttpResponse httpResponse = null;
        try {
            httpResponse = client.doAction(imageSyncScanRequest);
        } catch (Exception e) {
            log.error("check image syn request failed", e);
            return false;
        }
        // 服务端接收到请求，完成处理后返回的结果。
        if (httpResponse != null && httpResponse.isSuccess()) {
            JSONObject scrResponse = JSON.parseObject(StringUtils.newStringUtf8(httpResponse.getHttpContent()));
            log.info("content security check result: {}", scrResponse.toString());
            int requestCode = scrResponse.getIntValue("code");
            // 每一张图片的检测结果。
            JSONArray taskResults = scrResponse.getJSONArray("data");
            if (200 != requestCode) {
                log.error("aliyun image check failed:{}",JSON.toJSONString(scrResponse));
                return false;
            }
            Object taskResult = taskResults.get(0);
            // 单张图片的处理结果。
            int taskCode = ((JSONObject) taskResult).getIntValue("code");
            if (200 != taskCode) {
                log.error("aliyun image check task process fail:{}",JSON.toJSONString(scrResponse));
                return false;
            }
            // 图片对应检测场景的处理结果。如果是多个场景，则会有每个场景的结果。
            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
            for (Object sceneResult : sceneResults) {
                String suggestion = ((JSONObject) sceneResult).getString("suggestion");
                if("block".equals(suggestion)) {
                    log.info("block suggestion:{}",((JSONObject) sceneResult).toJSONString());
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    public boolean checkContent(String content){
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, secret);
        DefaultProfile.addEndpoint(regionId, product, endPoint);
        // 注意：此处实例化的client尽可能重复使用，提升检测性能。避免重复建立连接。
        IAcsClient client = new DefaultAcsClient(profile);
        TextScanRequest textScanRequest = new TextScanRequest();
        textScanRequest.setAcceptFormat(FormatType.JSON); // 指定API返回格式。
        textScanRequest.setHttpContentType(FormatType.JSON);
        textScanRequest.setMethod(MethodType.POST); // 指定请求方法。
        textScanRequest.setEncoding("UTF-8");
        textScanRequest.setRegionId(regionId);
        List<Map<String, Object>> tasks = new ArrayList<>();
        Map<String, Object> task = new LinkedHashMap<>();
        task.put("dataId", IdUtil.fastSimpleUUID());
        // 待检测的文本，长度不超过10000个字符。
        task.put("content", content);
        tasks.add(task);
        JSONObject data = new JSONObject();
        // 检测场景。文本垃圾检测请传递antispam。
        data.put("scenes", Arrays.asList("antispam"));
        data.put("tasks", tasks);
        textScanRequest.setHttpContent(StrUtil.utf8Bytes(data.toJSONString()), "UTF-8", FormatType.JSON);
        // 请务必设置超时时间。
        textScanRequest.setConnectTimeout(3000);
        textScanRequest.setReadTimeout(6000);
        try {
            HttpResponse httpResponse = client.doAction(textScanRequest);
            if (!httpResponse.isSuccess()) {
                log.error("response not success. status:{}",httpResponse.getStatus());
                // 业务处理。
                return false;
            }
            JSONObject scrResponse = JSON.parseObject(new String(httpResponse.getHttpContent(), StandardCharsets.UTF_8));
            if (200 != scrResponse.getInteger("code")) {
                log.error("detect not success. code:{}",scrResponse.getInteger("code"));
                // 业务处理。
                return false;
            }
            JSONArray taskResults = scrResponse.getJSONArray("data");
            Object taskResult = taskResults.get(0);
            if (200 != ((JSONObject) taskResult).getInteger("code")) {
                log.error("task process fail:{}",((JSONObject) taskResult).getInteger("code"));
                // 业务处理。
                return false;
            }
            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
            for (Object sceneResult : sceneResults) {
                String suggestion = ((JSONObject) sceneResult).getString("suggestion");
                if("block".equals(suggestion)) {
                    log.info("block suggestion:{}",((JSONObject) sceneResult).toJSONString());
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("check content error",e);
            e.printStackTrace();
            return false;
        }
    }
}
