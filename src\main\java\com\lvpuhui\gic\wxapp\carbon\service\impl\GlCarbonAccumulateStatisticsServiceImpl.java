package com.lvpuhui.gic.wxapp.carbon.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonAccumulateStatisticsDao;
import com.lvpuhui.gic.wxapp.carbon.service.GlCarbonAccumulateStatisticsService;
import com.lvpuhui.gic.wxapp.homepage.dto.CarbonAccumulateDto;
import com.lvpuhui.gic.wxapp.homepage.dto.CarbonAccumulateProjectDto;
import com.lvpuhui.gic.wxapp.infrastructure.utils.MailUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023年04月11日 11:37:00
 */
@Slf4j
@Service
public class GlCarbonAccumulateStatisticsServiceImpl implements GlCarbonAccumulateStatisticsService {

    @Resource
    private GlCarbonAccumulateStatisticsDao glCarbonAccumulateStatisticsDao;

    @Resource
    private MailUtils mailUtils;

    @Override
    public CarbonAccumulateDto carbonStatistics() {
        CarbonAccumulateDto carbonAccumulateDto = new CarbonAccumulateDto();

        CarbonAccumulateProjectDto accumulateAndYesterdayStatistics = glCarbonAccumulateStatisticsDao.getAccumulateAndYesterdayStatistics();
        if(Objects.nonNull(accumulateAndYesterdayStatistics)){
            carbonAccumulateDto.setName("累积年度碳汇");
            carbonAccumulateDto.setUnit("吨");
            if(Objects.nonNull(accumulateAndYesterdayStatistics.getAccumulateArea())){
                carbonAccumulateDto.setAccumulateEmission(accumulateAndYesterdayStatistics.getAccumulateArea().divide(BigDecimal.valueOf(1000000),2,RoundingMode.HALF_UP));
            }
            if(Objects.nonNull(accumulateAndYesterdayStatistics.getYesterdayArea())){
                carbonAccumulateDto.setYesterdayEmission(accumulateAndYesterdayStatistics.getYesterdayArea().divide(BigDecimal.valueOf(1000000),2,RoundingMode.HALF_UP));
            }
        }
        String batchNo = DateUtil.offsetDay(new DateTime(), -1).toString(DatePattern.PURE_DATE_PATTERN);
        List<CarbonAccumulateProjectDto> projectList = new ArrayList<>();
        List<CarbonAccumulateProjectDto> carbonAccumulateProjectDtoList = glCarbonAccumulateStatisticsDao.getAccumulateStatistics(batchNo);
        if(CollUtil.isEmpty(carbonAccumulateProjectDtoList)){
            batchNo = DateUtil.offsetDay(new DateTime(), -2).toString(DatePattern.PURE_DATE_PATTERN);
            carbonAccumulateProjectDtoList = glCarbonAccumulateStatisticsDao.getAccumulateStatistics(batchNo);
        }
        for (CarbonAccumulateProjectDto carbonAccumulateProjectDto : carbonAccumulateProjectDtoList) {
            carbonAccumulateProjectDto.setName(carbonAccumulateProjectDto.getName()+ "面积");
            carbonAccumulateProjectDto.setUnit("亩");
            if(Objects.nonNull(carbonAccumulateProjectDto.getAccumulateArea())){
                carbonAccumulateProjectDto.setAccumulateArea(carbonAccumulateProjectDto.getAccumulateArea().setScale(2,RoundingMode.HALF_UP));
            }
            if(Objects.nonNull(carbonAccumulateProjectDto.getYesterdayArea())){
                carbonAccumulateProjectDto.setYesterdayArea(carbonAccumulateProjectDto.getYesterdayArea().setScale(2,RoundingMode.HALF_UP));
            }
            projectList.add(carbonAccumulateProjectDto);
        }
        carbonAccumulateDto.setProjectList(projectList);
        return carbonAccumulateDto;
    }
}
