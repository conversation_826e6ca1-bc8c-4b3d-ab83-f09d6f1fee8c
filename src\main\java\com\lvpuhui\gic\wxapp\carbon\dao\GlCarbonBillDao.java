package com.lvpuhui.gic.wxapp.carbon.dao;

import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonBillDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * <p>
 * 提现账单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
public interface GlCarbonBillDao extends BaseMapper<GlCarbonBillDO> {

    /**
     * 更新提现金额
     * @param billId 提现账单ID
     * @param amount 提现金额
     * @return 更新条数
     */
    int updateBillAmount(@Param("billId") Long billId, @Param("amount") BigDecimal amount);
}
