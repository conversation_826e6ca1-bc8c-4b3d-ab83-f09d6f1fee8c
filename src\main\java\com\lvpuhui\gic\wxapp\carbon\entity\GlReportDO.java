package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 举报表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_report")
public class GlReportDO extends Model<GlReportDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 举报类型
     */
    private Long reportType;

    /**
     * 举报位置的经度
     */
    private BigDecimal reportLongitude;

    /**
     * 举报位置的纬度
     */
    private BigDecimal reportLatitude;

    /**
     * 举报状态
     */
    private Integer status;

    /**
     * 处理人
     */
    private Long handler;

    /**
     * 处理时间
     */
    private LocalDateTime handlingTime;

    /**
     * 举报时间
     */
    private LocalDateTime reportTime;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 举报者ID
     */
    private Long reportUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    private Long updator;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
