package com.lvpuhui.gic.wxapp.homepage.controller;


import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbon.service.GlCarbonAccumulateStatisticsService;
import com.lvpuhui.gic.wxapp.homepage.dto.*;
import com.lvpuhui.gic.wxapp.homepage.service.*;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import com.lvpuhui.gic.wxapp.pointexchange.service.GlGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 *
 * 提供面向租户的API接口服务
 * 文件上传
 * 获取FTP上传信息
 *
 * <AUTHOR>
 */
@Tags(value = {@Tag(name = "首页1.0")})
@RestController
@RequestMapping("")
public class IndexController {

    @Resource
    private GlBannerService glBannerService;

    @Resource
    private GlCompanyProductService glCompanyProductService;

    @Resource
    PointsQueryService pointsQueryService;

    @Resource
    EmissionCertificateService emissionCertificateService;

    @Resource
    private GlNoticeService glNoticeService;

    @Resource
    private GlVideoTopicService glVideoTopicService;

    @Resource
    private GlPurchaseIntentionService glPurchaseIntentionService;

    @Resource
    private GlTickoffTypeService glTickoffTypeService;

    @Resource
    private GlTickoffService glTickoffService;

    @Resource
    private GlRankYesterdayService glRankYesterdayService;

    @Resource
    private GlRankService glRankService;

    @Resource
    private GlGoodsService glGoodsService;

    @Resource
    private GlRankCompanyService glRankCompanyService;

    @Resource
    private GlCarbonAccumulateStatisticsService glCarbonAccumulateStatisticsService;

    @Resource
    private GlQuestionService glQuestionService;

    @Resource
    private GlSubjectRecordService glSubjectRecordService;

    @Resource
    private GlUserService glUserService;

    @Resource
    private GlVideoService glVideoService;

    /**
     * 首页减排量统计接口
     */
    @PassToken
    @GetMapping("/emission")
    public R getEmission(){
        return pointsQueryService.getEmission();
    }

    /**
     * 首页碳汇统计接口
     */
    @Operation(summary = "首页碳汇统计接口", description = "首页碳汇统计接口", tags = { "首页1.0" })
    @PassToken
    @GetMapping("/carbonStatistics")
    public R<CarbonAccumulateDto> carbonStatistics(){
        return R.ok(glCarbonAccumulateStatisticsService.carbonStatistics());
    }

    @GetMapping("/green_points2")
    public R<GreenPointsVo> greenPoints2(@RequestParam("mobileSha256") String mobileSha256,@RequestParam("page") Integer page){
        return R.ok(pointsQueryService.getGreenPoints2(mobileSha256,page));
    }

    /**
     * 减排量证书接口
     */
    @GetMapping("/emission_certificate")
    public R<EmissionCertificateVo> emissionCertificate(String mobileSha256){
        return R.ok(emissionCertificateService.buildCertificate(mobileSha256));
    }

    /**
     * 首页轮播图列表接口-lijianguang
     */
    @PassToken
    @GetMapping("/banner")
    public R<List<Banner>> banner(){
        List<Banner> banners = glBannerService.banner();
        return R.ok(banners);
    }

    /**
     * 首页公司小程序展示列表
     */
    @PassToken
    @GetMapping("/company_list")
    public R<List<SceneCategories>> companyList(){
        List<SceneCategories> sceneCategoriesList = glCompanyProductService.companyList();
        return R.ok(sceneCategoriesList);
    }

    /**
     * 公告列表接口-lijianguang
     */
    @PassToken
    @GetMapping("/notice_list")
    public R<List<NoticeList>> noticeList(){
        List<NoticeList> noticeLists = glNoticeService.noticeList();
        return R.ok(noticeLists);
    }

    /**
     * 公告详情接口-lijianguang
     */
    @PassToken
    @PostMapping("/notice_detail")
    public R<NoticeDetail> noticeDetail(@RequestBody @Valid NoticeDetailDto noticeDetailDto){
        NoticeDetail noticeDetail = glNoticeService.noticeDetail(noticeDetailDto);
        return R.ok(noticeDetail);
    }

    /**
     * 获取微信短链
     */
    @PassToken
    @GetMapping("/url_link")
    public R<UrlLinkVo> getUrlLink(){
        UrlLinkVo urlLinkVo = glVideoTopicService.getUrlLink();
        return R.ok(urlLinkVo);
    }

    /**
     * 保存客户购买意愿
     */
    @PostMapping("/save_purchase")
    public R<String> savePurchase(@RequestBody PurchaseIntetionDto purchaseIntetionDto){
        glPurchaseIntentionService.savePurchase(purchaseIntetionDto);
        return R.ok("保存成功");
    }

    /**
     * 查询主体列表
     */

    @PostMapping("/queryPurchaseSubjectList")
    public R<List<QueryPurchaseSubjectListVo>> queryPurchaseSubjectList(){
        List<QueryPurchaseSubjectListVo> list = glPurchaseIntentionService.queryPurchaseSubjectList();
        return R.ok(list);
    }


    /**
     * 打卡列表(每日打卡、新人签到等)接口-lijianguang
     */
    @PassToken
    @GetMapping("/ticks")
    public R<List<Ticks>> ticks(@RequestParam("mobileSha256") String mobileSha256){
        TicksDto ticksDto = new TicksDto();
        ticksDto.setMobileSha256(mobileSha256);
        List<Ticks> ticks = glTickoffTypeService.ticks(ticksDto);
        return R.ok(ticks);
    }

    /**
     * 打卡接口-lijianguang
     */
    @PostMapping("/punch_clock")
    public R<Tickoff> punchClock(@RequestBody @Valid TickoffDto tickoffDto){
        R<Tickoff> tickoffR = glTickoffService.punchClock(tickoffDto);
        return tickoffR;
    }

    /**
     * 判断是否已打卡接口-lijianguang
     */
    @PostMapping("/check_tick")
    public R<CheckTick> checkTick(@RequestBody @Valid CheckTickDto checkTickDto){
        CheckTick checkTick = glTickoffService.checkTick(checkTickDto);
        return R.ok(checkTick);
    }

    /**
     * 打卡详情信息接口-lijianguang
     */
    @GetMapping("/ticks_detail")
    public R<TicksDetail> ticksDetail(@RequestParam("tickId") Long tickId){
        TicksDetail ticksDetail = glTickoffTypeService.ticksDetail(tickId);
        return R.ok(ticksDetail);
    }

    /**
     * 昨日排行接口
     */
    @PostMapping("/yesterday_rank")
    public R<RankYesterdayList> yesterdayRank(@RequestBody @Valid RankYesterdayListDto rankYesterdayListDto){
        RankYesterdayList rankYesterdayList = glRankYesterdayService.yesterdayRank(rankYesterdayListDto);
        return R.ok(rankYesterdayList);
    }

    /**
     * 累积排行接口
     */
    @PostMapping("/accumulate_rank")
    public R<RankAccumulateList> accumulateRank(@RequestBody @Valid RankAccumulateListDto rankAccumulateListDto){
        RankAccumulateList rankAccumulateList = glRankService.accumulateRank(rankAccumulateListDto);
        return R.ok(rankAccumulateList);
    }

    /**
     * 邀请排行接口
     */
    @PostMapping("/invite_rank")
    public R<RankInviteList> rankInviteList(@RequestBody @Valid RankAccumulateListDto rankAccumulateListDto){
        RankInviteList rankInviteList = glRankService.getRankInviteList(rankAccumulateListDto);
        return R.ok(rankInviteList);
    }

    /**
     * 昨日排行领取商品接口
     */
    @PostMapping("/yesterday_obtain")
    public R<YesterdayObtainGoods> yesterdayObtain(@RequestBody @Valid YesterdayObtainGoodsDto rankAccumulateListDto){
        YesterdayObtainGoods yesterdayObtainGoods = glGoodsService.yesterdayObtain(rankAccumulateListDto);
        return R.ok(yesterdayObtainGoods);
    }

    /**
     * 累积排行领取商品接口
     */
    @PostMapping("/accumulate_obtain")
    public R<AccumulateObtainGoods> accumulateObtain(@RequestBody @Valid AccumulateObtainGoodsDto accumulateObtainGoodsDto){
        AccumulateObtainGoods accumulateObtainGoods = glGoodsService.accumulateObtain(accumulateObtainGoodsDto);
        return R.ok(accumulateObtainGoods);
    }

    /**
     * 企业排行接口-lijianguang
     */
    @PassToken
    @GetMapping("/company_rank")
    public R<List<CompanyRank>> companyRank(){
        List<CompanyRank> companyRanks = glRankCompanyService.companyRank();
        return R.ok(companyRanks);
    }

    /**
     * 问卷列表接口-lijianguang
     */
    @PostMapping("/question_list")
    public R<List<QuestionList>> questionList(@RequestBody @Valid QuestionListDto questionListDto){
        List<QuestionList> questionLists = glQuestionService.questionList(questionListDto);
        return R.ok(questionLists);
    }

    /**
     * 问卷完成接口-lijianguang
     */
    @PassToken
    @PostMapping("/question_finish")
    public R<QuestionFinish> questionFinish(@RequestBody @Valid QuestionFinishDto questionFinishDto){
        QuestionFinish questionFinish = glQuestionService.questionFinish(questionFinishDto);
        return R.ok(questionFinish);
    }

    /**
     * 当天是否还有做题次数接口-lijianguang
     */
    @PostMapping("/check_subject")
    public R<SubjectNumber> checkSubject(@RequestBody @Valid SubjectNumberDto subjectNumberDto){
        SubjectNumber subjectNumber = glSubjectRecordService.checkSubject(subjectNumberDto);
        return R.ok(subjectNumber);
    }

    /**
     * 问答配置接口-lijianguang
     */
    @GetMapping("/subject_config")
    public R<SubjectConfig> subjectConfig(){
        SubjectConfig subjectConfig = glSubjectRecordService.subjectConfig();
        return R.ok(subjectConfig);
    }

    /**
     * 问答完成接口-lijianguang
     */
    @PostMapping("/subject_finish")
    public R<SubjectFinish> subjectFinish(@RequestBody @Valid SubjectFinishDto subjectFinishDto){
        SubjectFinish subjectFinish = glSubjectRecordService.subjectFinish(subjectFinishDto);
        return R.ok(subjectFinish);
    }

    @Operation(summary = "我的碳账本数据接口", description = "我的碳账本数据接口", tags = { "首页1.0" })
    @GetMapping("/my_carbon_book")
    public R<MyCarbonBookDataVo> myCarbonBook(){
        MyCarbonBookDataVo myCarbonBookDataVo = glUserService.myCarbonBook();
        return R.ok(myCarbonBookDataVo);
    }

    /**
     * 视频列表接口-lijianguang
     */
    @GetMapping("/index_video_list")
    @PassToken
    @Operation(summary = "视频接口-首页", description = "视频接口-首页", tags = { "首页1.0" })
    public R<List<VideoList>> indexVideoList(){
        List<VideoList> videoLists = glVideoService.indexVideoList();
        return R.ok(videoLists);
    }

    /**
     * 视频列表接口-lijianguang
     */
    @PostMapping("/video_list")
    public R<List<VideoList>> videoList(@RequestBody @Valid VideoListDto videoListDto){
        List<VideoList> videoLists = glVideoService.videoList(videoListDto);
        return R.ok(videoLists);
    }

    /**
     * 视频完成接口-lijianguang
     */
    @PostMapping("/video_finish")
    public R<VideoFinish> videoFinish(@RequestBody @Valid VideoFinishDto videoFinishDto){
        VideoFinish videoFinish = glVideoService.videoFinish2(videoFinishDto);
        return R.ok(videoFinish);
    }

    @PostMapping("/video_finish2")
    public R<VideoFinish> videoFinish2(@RequestBody @Valid VideoFinishDto videoFinishDto){
        VideoFinish videoFinish = glVideoService.videoFinish(videoFinishDto);
        return R.ok(videoFinish);
    }
}
