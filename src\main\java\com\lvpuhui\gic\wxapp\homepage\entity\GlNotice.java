package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 公告表实体类(gl_notice)
 * <AUTHOR>
 * @since 2022-06-01
 */
@Data
public class GlNotice extends Model<GlNotice> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String title;

    /**
     * 场景企业名称
     */
    private String content;

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 发布状态(0：保存  1：发布)
     */
    private Integer state;

    /**
     * 发布时间
     */
    private Date published;

    /**
     * 阅读数
     */
    private Integer readCount;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    private Long updator;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;
}
