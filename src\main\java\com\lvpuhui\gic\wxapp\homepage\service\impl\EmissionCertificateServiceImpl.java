package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbonbook.dto.PointsEmission;
import com.lvpuhui.gic.wxapp.carbonbook.service.CarbonBookService;
import com.lvpuhui.gic.wxapp.homepage.dto.EmissionCertificateVo;
import com.lvpuhui.gic.wxapp.homepage.service.EmissionCertificateService;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmissionCertificateServiceImpl implements EmissionCertificateService {

    @Resource
    CarbonBookService carbonBookService;
    @Resource
    GlAppletConfigService glAppletConfigService;
    @Resource
    GlUserService glUserService;
    @Override
    public EmissionCertificateVo buildCertificate(String mobileSha256) {
        mobileSha256 = UserUtils.getMobileSha256();
        PointsEmission pointsEmission = carbonBookService.myPointsEmission(mobileSha256);
        EmissionCertificateVo vo = new EmissionCertificateVo();
        BeanUtil.copyProperties(pointsEmission,vo);
        vo.setCertificateUrl("http://oss.lvpuhui.com/wx/20220512/1e9fb4e9123f4d779d7f1251f3e33fb2.png");
        GlUser user = glUserService.getOne(new LambdaQueryWrapper<GlUser>().eq(GlUser::getMobileSha256,mobileSha256));
        if(user !=null){
            vo.setNickName(user.getNickName());
            vo.setAvatarUrl(user.getAvatarUrl());
            long between = DateUtil.between(user.getCreated(), new Date(), DateUnit.DAY, false) + 1;
            vo.setDays(between);
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        GlAppletConfig config = glAppletConfigService.getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.CONFIG_BACKGROUND_IMAGE));
        if(config != null){
            vo.setCertificateUrl(String.format("%s%s", imagePrefix,config.getParamValue()));
        }
        return vo;

    }
}
