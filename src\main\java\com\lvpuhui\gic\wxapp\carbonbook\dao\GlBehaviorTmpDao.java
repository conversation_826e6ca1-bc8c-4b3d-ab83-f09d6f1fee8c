package com.lvpuhui.gic.wxapp.carbonbook.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.carbonbook.dto.TmpDateBetweenDto;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehaviorTmp;
import org.apache.ibatis.annotations.Select;

/**
 * 绿色行为记录表 (来源大数据增量)(GlBehaviorTmp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-20 10:52:59
 */
public interface GlBehaviorTmpDao extends BaseMapper<GlBehaviorTmp> {

    @Select("SELECT MIN(date) AS minDate,MAX(date) AS maxDate FROM gl_behavior_tmp")
    TmpDateBetweenDto getTmpDateBetween();
}