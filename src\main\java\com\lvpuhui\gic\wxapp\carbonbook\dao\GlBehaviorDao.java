package com.lvpuhui.gic.wxapp.carbonbook.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lvpuhui.gic.wxapp.carbonbook.dto.carbonbook.AppletCarbonBookVo;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.carbonbook.dto.SceneDetailDto;
import com.lvpuhui.gic.wxapp.carbonbook.dto.SceneVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 绿色行为记录表 (来源大数据增量)(GlBehavior)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-06 13:45:10
 */
public interface GlBehaviorDao extends BaseMapper<GlBehavior> {

    @Select("SELECT " +
            "sum( emission ) emissionNum, " +
            "scenario_id AS id  " +
            "FROM " +
            "gl_behavior " +
            "WHERE " +
            "mobile_sha256 = #{mobileSha256} " +
            "GROUP BY " +
            "scenario_id")
    List<SceneVo> getBehaviorGroupByScenarios(@Param("mobileSha256") String mobileSha256);

    @Select("SELECT " +
            "sum( emission ) emissionNum, " +
            "app_id, " +
            "scenario_id, " +
            "`date`  " +
            "FROM " +
            "gl_behavior " +
            "WHERE " +
            "mobile_sha256 = #{mobileSha256} " +
            "AND scenario_id IN (${ids})  " +
            "GROUP BY " +
            "app_id,scenario_id,`date`  " +
            "ORDER BY `date` DESC  " +
            "LIMIT #{offset},#{size}")
    List<SceneDetailDto> getBehaviorDetailGroupByScenarios(@Param("mobileSha256")String mobileSha256, @Param("offset") int offset, @Param("size") Integer size, @Param("ids") String ids);

    @Select("SELECT COUNT(1) " +
            "FROM " +
            "gl_behavior " +
            "WHERE " +
            "mobile_sha256 = #{mobileSha256} " +
            "AND scenario_id IN (${ids})  " +
            " ")
    Integer getBehaviorDetailCount(@Param("mobileSha256") String mobileSha256,@Param("ids") String ids);

    @Select("SELECT SUM(emission) FROM gl_behavior WHERE mobile_sha256 = #{mobileSha256}")
    BigDecimal getSumEmissionByMobileSha256(@Param("mobileSha256") String mobileSha256);

    @Select("SELECT behavior_id AS actId,SUM(emission) AS actEmission FROM gl_behavior WHERE mobile_sha256 = #{mobileSha256} GROUP BY behavior_id")
    List<AppletCarbonBookVo> getSumEmissionGroupByAct(@Param("mobileSha256") String mobileSha256);

    @Select("SELECT app_id,scenario_id,DATE_FORMAT(date,'%Y-%m-%d') AS date,emission as emissionNum FROM gl_behavior WHERE mobile_sha256 = #{mobileSha256}  AND behavior_id = #{actId}  ORDER BY date DESC")
    Page<SceneDetailDto> getBehaviorByActId(Page<SceneDetailDto> voPage, @Param("mobileSha256") String mobileSha256, @Param("actId") Integer actId);
}