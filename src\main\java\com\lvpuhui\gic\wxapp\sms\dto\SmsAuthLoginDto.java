package com.lvpuhui.gic.wxapp.sms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@Schema(description = "短信验证码登录")
public class SmsAuthLoginDto {

    @Schema(description = "手机号")
    @NotBlank(message = "请输入手机号")
    private String mobile;

    @Schema(description = "验证码")
    @NotBlank(message = "请输入验证码")
    private String code;
}
