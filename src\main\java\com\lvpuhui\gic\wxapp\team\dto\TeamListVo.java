package com.lvpuhui.gic.wxapp.team.dto;

import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 战队列表返回值
 * <AUTHOR>
 * @since 2023年05月24日 15:22:00
 */
@Data
@Schema(description = "战队列表返回值")
public class TeamListVo {

    /**
     * 是否是自己的战队 true:是自己的战队  false:不是自己的战队
     */
    @Schema(description = "是否是自己的战队 true:是自己的战队  false:不是自己的战队")
    private Boolean isMyTeam = false;

    /**
     * 战队ID
     */
    @Schema(description = "战队ID")
    private Long id;

    /**
     * 战队名称
     */
    @Schema(description = "战队名称")
    private String teamName;

    /**
     * 战队Logo
     */
    @Schema(description = "战队Logo")
    private String teamLogo;

    /**
     * 成员人数
     */
    @Schema(description = "成员人数")
    private Integer memberCount;

    /**
     * 减排量-文本
     */
    @Schema(description = "减排量-文本")
    private String emissionText;

    /**
     * 减排量
     */
    @Schema(description = "减排量")
    private BigDecimal emission;

    /**
     * 创建人昵称
     */
    @Schema(description = "创建人昵称")
    private String nickName;

    /**
     * 创建人头像
     */
    @Schema(description = "创建人头像")
    private String avatarUrl;

    /**
     * 是否已认证 0：未认证  1：已认证
     */
    @Schema(description = "是否已认证 0：未认证  1：已认证")
    private Integer certification;

    /**
     * 类型 0:企业 1:学校 2:政府 3:其他
     */
    @Schema(description = "类型 0:企业 1:学校 2:政府 3:其他")
    private Integer type;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return "0g";
    }
}
