package com.lvpuhui.gic.wxapp.infrastructure.constant;

/**
 * 此类
 *
 * <AUTHOR>
 * @date 2020/7/23
 */
public class Global {

    public static final String USER = "user";
    /**
     * 日志key
     */
    public static final String TRACE_ROUTE_HEADER = "trace_uuid";
    /**
     * 租户token
     */
    public static final String ACCESS_TOKEN = "ACCEPTER:ACCESS_TOKEN:";
    public static final String FTP_SERVER_TENANT = "ACCEPTER:FTP_SERVER_TENANT:";

    /**
     * 配置表 行为key
     */
    public static final String CONFIG_BEHAVIOR = "BEHAVIOR";
    /** 消费积分价值设置*/
    public static final String CONFIG_POINT_WORTH_CONSUMPTION = "POINT_WORTH_CONSUMPTION";
    /** 减排量积分兑换设置 */
    public static final String CONFIG_POINT_WORTH_BEHAVIOR = "POINT_WORTH_BEHAVIOR";
    /** 小程序背景图 */
    public static final String CONFIG_BACKGROUND_IMAGE = "BACKGROUND_IMAGE";
    /** 导入绿色消费发放减排量*/
    public static final String CONSUMPTION_EMISSION_REDUCTION = "CONSUMPTION_EMISSION_REDUCTION";
    /** 排行榜设置*/
    public static final String RANK_SETTINGS = "RANK_SETTINGS";
    /** 排行榜商品领取限制*/
    public static final String RANK_GOODS_LIMIT = "RANK_GOODS_LIMIT";
    /** 打卡锁定时长*/
    public static final String TICK_LOCK_DURATION = "TICK_LOCK_DURATION";
    /** 打卡每公里间隔时间*/
    public static final String TICK_INTERVAL_DURATION = "TICK_INTERVAL_DURATION";
    /** 打卡最小距离 小于等于此距离的不做判断*/
    public static final String TICK_MIN_DISTANCE = "TICK_MIN_DISTANCE";

    /** 映射关系*/
    public static final String MAPPING = "MAPPING";

    /**
     * 碳汇项目ID
     */
    public static final String CARBON_PROJECT_IDS = "CARBON_PROJECT_IDS";

    /**
     * 图片前缀
     */
    public static final String IMAGE_PREFIX = "IMAGE_PREFIX";
    /**
     * AppID(小程序ID)
     */
    public static final String APP_ID = "wxd1f7a37f4c46056a";

    /**
     * AppSecret(小程序密钥)
     */
    public static final String SECRET = "1dab36c1648512f748d8234779cf9e6c";

    /**
     * 微信code2Session接口地址
     */
    public static final String WX_CODE2SESSION = "https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code";

    /**
     * 获取微信AccessToken接口地址
     */
    public static final String WX_ACCESS_TOKEN = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";

    /**
     * 获取微信手机号
     */
    public static final String WX_USER_PHONE = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=ACCESS_TOKEN";

    /**
     * 百度地图的ak
     */
    public static final String BAIDU_AK = "Zw2WBUB7yMGtNpQsZKznGKOemogcSbjY";

    /**
     * 百度地图搜索地址-GET
     */
    public static final String MAP_SEARCH_URL = "https://api.map.baidu.com/place/v2/search";
    /**
     * 小程序发送服务消息
     */
    public static final String WX_SERVICE_MESSAGE_SEND = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send";

    /**
     * 高德地图key
     */
    public static final String GAO_DE_KEY = "2e213ac2bd9d2d75a7659352f515d99b";

    /**
     * 高德地图周边搜索地址-GET
     */
    public static final String GAO_DE_MAP_SEARCH_URL = "https://restapi.amap.com/v5/place/around";

    /**
     * 生成微信短链地址
     */
    public static final String GENERATE_URL_LINK = "https://api.weixin.qq.com/wxa/generate_urllink?access_token={}";

    /**
     * 累积的编号-固定的
     */
    public static final String ACCUMULATE_NO = "ACC_NO";
}
