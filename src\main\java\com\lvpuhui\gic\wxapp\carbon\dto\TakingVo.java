package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "提现凭证返回值")
public class TakingVo {

    @Schema(description = "提现凭证ID")
    private Long id;

    /**
     * 提现验证码
     */
    @Schema(description = "提现验证码")
    private String withdrawVerifyCode;

    /**
     * 提现金额
     */
    @Schema(description = "提现金额")
    private BigDecimal amount;

    /**
     * 提现时间
     */
    @Schema(description = "提现时间")
    private LocalDateTime withdrawTime;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String userName;

    /**
     * 备注,包含提现开始时间和截止时间以及提现携带资料和提现地点
     */
    @Schema(description = "备注,包含提现开始时间和截止时间以及提现携带资料和提现地点")
    private String remarks;

    /**
     * 状态 0：未领取 1：已领取
     */
    @Schema(description = "状态 0：未领取 1：已领取")
    private Integer status;
}