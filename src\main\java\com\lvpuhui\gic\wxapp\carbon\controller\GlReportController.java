package com.lvpuhui.gic.wxapp.carbon.controller;


import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbon.dto.ReportInfoDto;
import com.lvpuhui.gic.wxapp.carbon.dto.ReportInfoValidVo;
import com.lvpuhui.gic.wxapp.carbon.dto.ReportSubmitDto;
import com.lvpuhui.gic.wxapp.carbon.dto.SuperviseListVo;
import com.lvpuhui.gic.wxapp.carbon.service.GlReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * 举报表 前端控制器
 * <AUTHOR>
 * @since 2023-09-12
 */
@Tags(value = {@Tag(name = "举报1.0")})
@RestController
@RequestMapping("")
public class GlReportController {

    @Resource
    private GlReportService glReportService;

    @Operation(summary = "举报人信息填写校验接口", description = "举报人信息填写校验接口", tags = { "举报1.0" })
    @GetMapping("/report_info_valid")
    public R<ReportInfoValidVo> reportInfoValid() {
        ReportInfoValidVo reportInfoValid = glReportService.reportInfoValid();
        return R.ok(reportInfoValid);
    }

    @Operation(summary = "举报人信息填写提交接口", description = "举报人信息填写提交接口", tags = { "举报1.0" })
    @PostMapping("/report_info")
    public R<String> reportInfo(@RequestBody @Valid ReportInfoDto reportInfoDto) {
        glReportService.reportInfo(reportInfoDto);
        return R.ok("");
    }

    @Operation(summary = "举报提交接口", description = "举报提交接口", tags = { "举报1.0" })
    @PostMapping("/report_submit")
    public R<String> reportSubmit(@RequestBody @Valid ReportSubmitDto reportSubmitDto) {
        glReportService.reportSubmit(reportSubmitDto);
        return R.ok("");
    }

    @Operation(summary = "监督管理列表", description = "监督管理列表", tags = { "举报1.0" })
    @PostMapping("/superviseList")
    public R<LinkedHashMap<String, List<SuperviseListVo>>> superviseList() {
        LinkedHashMap<String, List<SuperviseListVo>> map = glReportService.superviseList();
        return R.ok(map);
    }


}