package com.lvpuhui.gic.wxapp.base.storage.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.lvpuhui.gic.wxapp.base.storage.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2023年04月07日 16:20:00
 */
@Slf4j
@Service("localStorageService")
public class LocalStorageService implements StorageService {

    @Value("${file.path}")
    private String path;

    @Override
    public String uploadStream(InputStream inputStream, String fileName) {
        try {
            String localFileName = File.separator + path + fileName;
            FileUtil.writeFromStream(inputStream,new File(localFileName));
        }catch (Exception e){
            return null;
        }
        return fileName;
    }

    @Override
    public void downloadToResponse(String fileName, HttpServletResponse response) {
        String localFileName = File.separator + path + fileName;
        if(!FileUtil.exist(localFileName)){
            return;
        }
        try (InputStream inputStream = IoUtil.toStream(new File(localFileName))){
            IoUtil.copy(inputStream,response.getOutputStream());
        }catch (Exception e){
            log.error("下载文件异常:{}",e);
        }
    }

    @Override
    public void deleteFile(String hash) {
        String directory = File.separator + path;
        if(!directory.endsWith(File.separator)) {
            directory += File.separator;
        }
        String uri = hash.substring(0, 2) + File.separator + hash.substring(2, 4) + File.separator + hash;

        String cafePath = directory + uri +".cafe";
        String filePath = directory + uri;

        new File(filePath).delete();

        File cafePathFile = new File(cafePath);
        if(cafePathFile.exists()){
            cafePathFile.delete();
        }
    }
}
