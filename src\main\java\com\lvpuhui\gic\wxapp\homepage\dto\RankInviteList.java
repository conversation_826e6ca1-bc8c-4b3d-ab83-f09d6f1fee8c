package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RankInviteList {
    private Double points;
    private Double MaximumPoints;
    private InviteRankUser currentUser;
    private List<InviteRankUser> inviteRankUsers;

    @Data
    public static class InviteRankUser{

        /**
         * 排名
         */
        private Long rank;

        /**
         * 减排量
         */
        private Double emission;

        /**
         * 昵称
         */
        private String nickName;

        /**
         * 头像
         */
        private String avatarUrl;

        /**
         * 用户id(sha256)
         */
        private String mobileSha256;
    }
}
