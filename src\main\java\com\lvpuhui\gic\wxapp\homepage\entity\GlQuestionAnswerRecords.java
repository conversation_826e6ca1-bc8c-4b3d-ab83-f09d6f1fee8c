package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 小程序问卷答复记录表实体类(gl_question_answer_records)
 * <AUTHOR>
 * @since 2022-06-13
 */
@Data
public class GlQuestionAnswerRecords extends Model<GlQuestionAnswerRecords> {

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 小程序问卷id
     */
    private Long appletQuestionId;

    /**
     * 微信Open ID
     */
    private String openId;

    /**
     * 问卷积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 用户id(sha256)
     */
    private String mobileSha256;

    /**
     * 问卷id
     */
    private String questionId;

    /**
     * 问卷记录id
     */
    private Long questionRecordId;

    /**
     * 创建时间
     */
    private Date created;
}
