FROM openjdk-1.8-skywalking-agent:v0.1

ENV XMS "512m"
####  openjdk-1.8-skywalking-agent:v0.1  变量
ENV SERVER_NAME "ordos-wxapp-api"
ENV JAVA_CMD "/usr/local/openjdk-8/bin/java"
ENV SKYWALKING_SERVER "172.17.151.116:11800"
ENV AGENT_JAR "/usr/local/skywalking-agent/skywalking-agent.jar"
ENV SKYWALKING_AGENT "-javaagent:${AGENT_JAR} -Dskywalking.agent.service_name=${SERVER_NAME} -Dskywalking.collector.backend_service=${SKYWALKING_SERVER}" 
### 
ADD target/*.jar /${SERVER_NAME}.jar 

ENTRYPOINT  [ "/bin/bash", "-c", "${JAVA_CMD} -Xms${XMS} -Xmx${XMS} ${SKYWALKING_AGENT} -jar /${SERVER_NAME}.jar" ] 
