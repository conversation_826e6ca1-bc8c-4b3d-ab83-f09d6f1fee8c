package com.lvpuhui.gic.wxapp.carbon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.carbon.dto.CarbonEmissionSumGroupByProjectDto;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelEmissionDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023年04月12日 16:56:00
 */
public interface GlCarbonModelEmissionService extends IService<GlCarbonModelEmissionDO> {

    /**
     * 根据用户手机号和碳汇项目分组计算总减排量
     */
    List<CarbonEmissionSumGroupByProjectDto> getEmissionSumGroupByProject(String mobileSha256);

    /**
     * 根据实例ID集合获取ID和名称的映射
     * @param modelIds 实例ID集合
     */
    Map<Long, String> getModelIdNameMapByModelIds(List<Long> modelIds);
}
