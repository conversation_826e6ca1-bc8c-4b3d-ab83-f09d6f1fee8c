<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.activety.dao.GlActivityDetailDao">
    <update id="updateByActiceId">
        update gl_activity_detail set detail_img_url = #{glActivityDetail.detailImgUrl} where activity_id = #{glActivityDetail.activityId}
    </update>

    <select id="selectByActivityId" resultType="java.lang.String">
        select detail_img_url from gl_activity_detail where activity_id = #{activityId}
    </select>
</mapper>

