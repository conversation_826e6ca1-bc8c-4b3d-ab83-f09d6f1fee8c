package com.lvpuhui.gic.wxapp.carbon.dao;

import com.lvpuhui.gic.wxapp.carbon.dto.TakingCertificateVo;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonBillDetailDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 账单明细表(每个人的账单） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
public interface GlCarbonBillDetailDao extends BaseMapper<GlCarbonBillDetailDO> {

    /**
     * 根据用户ID查询提现凭证
     * @param userId 用户ID
     */
    List<TakingCertificateVo> selectBillDetailByUserId(@Param("userId") Long userId);
}
