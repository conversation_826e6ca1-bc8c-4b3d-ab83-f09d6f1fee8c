package com.lvpuhui.gic.wxapp.pointexchange.dto;

import lombok.Data;

/**
 * 商品详情PO
 * <AUTHOR>
 * @since 2022年05月06日 14:58:00
 */
@Data
public class GoodsDetail {

    /**
     * 商品id
     */
    private Long id;

    /**
     * 商品封面图
     */
    private String coverImage;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 兑换积分
     */
    private Integer point;

    /**
     * 剩余数量
     */
    private Integer inventoryRemain;

    /**
     * 商品信息
     */
    private String description;

    /**
     * 有效期
     */
    private String expired;

    /**
     * 注意事项
     */
    private String attention;

    /**
     * 小程序appid
     */
    private String appletAppid;

    /**
     * 小程序调转地址
     */
    private String appletUrl;

    /**
     * 商品价值
     */
    private String price;

    /**
     * 兑换类型 0:兑换码 1:直接跳转
     */
    private Integer exchangeType;

    /**
     * 兑换地址
     */
    private String url;
}
