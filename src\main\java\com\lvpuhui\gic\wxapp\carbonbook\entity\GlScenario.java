package com.lvpuhui.gic.wxapp.carbonbook.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 场景表
 * <AUTHOR>
 * @since 2023年02月23日 17:26:00
 */
@Data
@TableName("gl_scenario")
public class GlScenario extends Model<GlScenario> {

    @TableId(type = IdType.INPUT)
    private Long id;

    private String name;

    private Date created;

    private Date updated;
}
