package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 碳汇项目实例（用户申请）减排量明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_model_emission")
public class GlCarbonModelEmissionDO extends Model<GlCarbonModelEmissionDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * modelID
     */
    private Long modelId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 加密手机号
     */
    private String mobileSha256;

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 收益减排量
     */
    private BigDecimal emission;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 收入备注
     */
    private String remark;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    private Long updator;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
