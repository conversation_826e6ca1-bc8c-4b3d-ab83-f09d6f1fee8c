package com.lvpuhui.gic.wxapp.treeplanting.service.impl;

import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.treeplanting.dao.TreePlantingDAO;
import com.lvpuhui.gic.wxapp.treeplanting.dto.TreePlantingDTO;
import com.lvpuhui.gic.wxapp.treeplanting.entity.TreePlantingDO;
import com.lvpuhui.gic.wxapp.treeplanting.service.TreePlantingService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

@Service
public class TreePlantingServiceImpl implements TreePlantingService {

    @Resource
    private TreePlantingDAO treePlantingDAO;

    @Override
    public void save(TreePlantingDTO treePlantingDTO) {
        if(Objects.nonNull(treePlantingDTO.getLatitude())
                && (treePlantingDTO.getLatitude() > Double.MAX_VALUE || treePlantingDTO.getLatitude() < Double.MIN_VALUE)){
            throw new GicWxAppException("地理位置非法");
        }
        if(Objects.nonNull(treePlantingDTO.getLongitude())
                && (treePlantingDTO.getLongitude() > Double.MAX_VALUE || treePlantingDTO.getLongitude() < Double.MIN_VALUE)){
            throw new GicWxAppException("地理位置非法");
        }
        TreePlantingDO treePlantingDO = new TreePlantingDO();
        BeanUtils.copyProperties(treePlantingDTO, treePlantingDO);
        treePlantingDO.setPlanterId(UserUtils.getUserId());
        treePlantingDO.setPlantingTime(LocalDateTime.now());
        treePlantingDO.setContactPhone(UserUtils.getUser().getMobile());
        treePlantingDO.setPlanterNickname(UserUtils.getUser().getName());
        treePlantingDO.setStatus(0);
        treePlantingDO.setCreated(LocalDateTime.now());
        treePlantingDAO.insert(treePlantingDO);
    }
}
