package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@Schema(description = "举报人信息填写DTO")
public class ReportInfoDto {

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @NotBlank(message = "请填写您的真实姓名")
    @Size(max = 100, message = "姓名长度过长")
    private String fullName;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    @NotBlank(message = "请填写您的身份证")
    @Size(max = 30, message = "身份证号过长")
    private String IDCard;
}
