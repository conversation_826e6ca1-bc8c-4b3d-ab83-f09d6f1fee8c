package com.lvpuhui.gic.wxapp.homepage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.homepage.entity.GlQuestion;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 小程序问卷 Mapper 接口
 * <AUTHOR>
 * @since 2022-06-13
 */
public interface GlQuestionDao extends BaseMapper<GlQuestion> {

    @Update("update gl_question set write_count=write_count+1 where id = #{id}")
    void updateWriteCount(@Param("id") Long id);
}
