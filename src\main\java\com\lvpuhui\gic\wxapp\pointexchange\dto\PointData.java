package com.lvpuhui.gic.wxapp.pointexchange.dto;

import lombok.Data;

import java.util.Objects;

/**
 * 我的绿色积分
 * <AUTHOR>
 * @since 2022年05月06日 16:57:00
 */
@Data
public class PointData {

    /**用户ID*/
    private Long id;
    /**总积分点数*/
    private Long pointTotal;
    /**剩余积分点数*/
    private Long pointRemain;
    /**消耗积分点数*/
    private Long pointConsumed;

    public Long getPointConsumed() {
        if(Objects.nonNull(pointConsumed)){
            return Math.abs(pointConsumed);
        }
        return pointConsumed;
    }
}
