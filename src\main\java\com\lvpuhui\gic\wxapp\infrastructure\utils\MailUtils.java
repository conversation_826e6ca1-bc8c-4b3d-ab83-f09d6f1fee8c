package com.lvpuhui.gic.wxapp.infrastructure.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 发送邮件工具类
 * <AUTHOR>
 * @since 2023年03月30日 11:45:00
 */
@Component
public class MailUtils {

    @Value("${spring.profiles.active:}")
    private String active;

    private final ThreadPoolExecutor executorService = new ThreadPoolExecutor(1, 1,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNameFormat("mail-t-%d").build(),new ThreadPoolExecutor.DiscardPolicy());

    public void sendMail(String title,String content){
        if("prod".equals(active)){
//            executorService.execute(() -> MailUtil.sendText("<EMAIL>",title,content));
        }
    }
}
