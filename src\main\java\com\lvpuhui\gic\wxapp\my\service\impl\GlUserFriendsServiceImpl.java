package com.lvpuhui.gic.wxapp.my.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.dto.RankConfigDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlPoints;
import com.lvpuhui.gic.wxapp.homepage.enums.PointsType;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.dao.GlUserFriendsDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.my.entity.GlUserFriends;
import com.lvpuhui.gic.wxapp.my.service.GlUserFriendsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 用户好友服务实现类
 * <AUTHOR>
 * @since 2022-07-26
 */
@Service
public class GlUserFriendsServiceImpl extends ServiceImpl<GlUserFriendsDao, GlUserFriends> implements GlUserFriendsService {
    @Resource
    GlPointsService glPointsService;
    @Resource
    GlAppletConfigService glAppletConfigService;

    @Resource
    private GlUserDao glUserDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void grantPoints(String mobileSha256, String sharedSha256,String nickname) {
        int k = count(new LambdaQueryWrapper<GlUserFriends>().eq(GlUserFriends::getMobileSha256,mobileSha256)
                .eq(GlUserFriends::getFriendsSha256,sharedSha256).eq(GlUserFriends::getDeleted,0));
        if(k!=0){
            String warning = StrUtil.format("已存在好友:{},{}",mobileSha256,sharedSha256);
            log.warn(warning);
            return;
        }
        LambdaQueryWrapper<GlUser> glUserLambdaQueryWrapper = Wrappers.lambdaQuery();
        glUserLambdaQueryWrapper.eq(GlUser::getMobileSha256,sharedSha256);
        GlUser glUser = glUserDao.selectOne(glUserLambdaQueryWrapper);
        if(Objects.isNull(glUser)){
            String warning = StrUtil.format("手机号:{},不存在,建立好友关系失败{}",sharedSha256,mobileSha256);
            log.warn(warning);
            return;
        }
        RankConfigDto rankConfigDto = glAppletConfigService.getRankConfig();
        RankConfigDto.InvitationBean invitation = rankConfigDto.getInvitation();
        double points = invitation.getPoints();
        double maximumPoints = invitation.getMaximumPoints();
        Map<String,Object> map = glPointsService.getMap(new QueryWrapper<GlPoints>()
                .select("IFNULL(sum(`point`),0) `points`")
                .eq("mobile_sha256",sharedSha256)
                .eq("type", PointsType.INVITE.getCode()));
        double totalPoints = Double.parseDouble(map.get("points").toString());
        GlUserFriends glUserFriends = new GlUserFriends()
                .setId(IdWorker.getId())
                .setMobileSha256(mobileSha256)
                .setFriendsSha256(sharedSha256)
                .setCreated(new Date())
                .setDeleted(0);
        save(glUserFriends);
        GlUserFriends glFriends = new GlUserFriends()
                .setId(IdWorker.getId())
                .setMobileSha256(sharedSha256)
                .setFriendsSha256(mobileSha256)
                .setCreated(new Date())
                .setDeleted(0);
        save(glFriends);
        if(totalPoints >= maximumPoints){
            glPointsService.grantInvite(glUserFriends,0d,nickname);
        }else if((totalPoints+points) >= maximumPoints){
            glPointsService.grantInvite(glUserFriends,maximumPoints-totalPoints,nickname);
        }else {
            glPointsService.grantInvite(glUserFriends,points,nickname);
        }
    }
}
