package com.lvpuhui.gic.wxapp.infrastructure.jackson;

import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 图片前缀静态属性
 * <AUTHOR>
 * @since 2023年11月01日 16:45:00
 */
@Slf4j
@Component
@RequestMapping("")
public class ImagePrefixStaticProperties implements CommandLineRunner {

    public static String imagePrefix;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Override
    public void run(String... args) throws Exception {
        log.info("select imagePrefix");
        imagePrefix = glAppletConfigService.getImagePrefix();
    }

    /**
     * 用于重置图片前缀的GET方法
     */
    @PassToken
    @ResponseBody
    @GetMapping("resetImagePrefix")
    public void resetImagePrefix(@RequestParam("secret")String secret) {
        if("c0277b1451144a32a90ea72c6d43bde2".equals(secret)){
            imagePrefix = glAppletConfigService.getImagePrefix();
        }
    }
}
