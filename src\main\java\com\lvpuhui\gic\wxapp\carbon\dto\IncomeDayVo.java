package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "日收益明细返回值")
public class IncomeDayVo {

    /**
     * 今年预计收益
     */
    @Schema(description = "今年预计收益")
    private BigDecimal amount;

    /**
     * 累计收益
     */
    @Schema(description = "累计收益")
    private BigDecimal accumulateAmount;


    /**
     * 日收益明细
     */
    @Schema(description = "日收益明细")
    List<InComeDetails> dayList;
}
