package com.lvpuhui.gic.wxapp.alipay.serivice.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipayEncrypt;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.internal.util.StringUtils;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserInfoShareRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lvpuhui.gic.wxapp.alipay.dto.AlipayGetPhoneNumberResponse;
import com.lvpuhui.gic.wxapp.alipay.dto.AlipayUserAuthData;
import com.lvpuhui.gic.wxapp.alipay.dto.AuthCodeDTO;
import com.lvpuhui.gic.wxapp.alipay.serivice.AuthService;
import com.lvpuhui.gic.wxapp.base.dao.GlAuthTokenDao;
import com.lvpuhui.gic.wxapp.base.entity.GlAuthTokenDO;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.my.dao.GlChangeBindDao;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.dto.CodeOpenid;
import com.lvpuhui.gic.wxapp.my.entity.GlChangeBind;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.other.utils.AES256Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 用户积分表(GlUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 15:16:32
 */
@Slf4j
@Service("alipayAuthService")
public class AlipayAuthServiceImpl extends ServiceImpl<GlUserDao, GlUser> implements AuthService {
    private static final String ALIPAY_APPID = "2021004170685433";
    private static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkw5fNUwfQ4W1WE4HbjZj0hGK8w3wOj7AK3/alyIf8K0/1KXKqQlfwNY5mqB7TAIZxV8n5M7J/+w8zc6PuQMDR/QdUz1DdGZcyTgE7+eg57X2oYIzKpwvchvKoHkT75G0RbOJRxL4hu700GDQF07jizqWbAhV3JNmIDtu0pdUoPzwm3ie/KcYQYJEukSWgXXlHgvQy1MVLw0WH0KzPIDKNCUrko8FKMTZEvPv+xDiVZGNbwQWa2HMDJB5++0g4I3QGBg7cVPYQP3ACsVqL9j9Z7HlZnS4gqMGtfA/6kjYMRs7PxMZAHOhx4yxvhbnQiAqbAPlTl2kPycxXFaX+DXN5wIDAQAB";
    private static final String ALIPAY_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCiEuJnUPPOw6j2F4FDf6X1tHpqN9H7W0UZLTihoQZXXuY4VXoZFSoUVyTE7HDfnibQj9+wz6LJ+6aZaZTb3Nt5RXkuFgfq6P9yJWACR273TxXNqVtggHuh+YQRAaBXH7aUeenJNXk3llt/8kI/6g9hj/Nm6VyN6cGbSNAyz1w7iO93bd7inae4FrNWaVzbEWJ1vAmKlWfop9EMlV2FOtMqpAbNM/Cy5us2NVMXUvYq12m/PUWrUnad9qZmUd/zDmk+p33/W6vxR7o6fdBBEKydAMgnsVBcqBC9hw0P10RmiFPUAd4ca/0y3QtPojrTx9OSsh5vFbQnDK1I7Hye68ktAgMBAAECggEBAJq4El/0vLqeuBYtfB3WprS3VME/hnpAt+b/73cEhEw18OrIVqXJOYFva09+M4aVsJIbNrzQiqPtUy5Sx2NYJEBR3QvsxiQvx2PNYCjsznbSjYrmaK/oF54Z10Rw3AMv6Bk3hMGrBYvWQoUS3MGtDH/jbsXIcq2bAFLgILjQkUjWu/TBpQF4EF0CL4/ZyFC+vi0iOlAUFwED1bCMmsYoxFY6kp22mWBPZG6z8sB8GL2jJL2R0vYdxyLt6ALTexkieo9s0NK2NiFLCqfJsjqOWFsbOI/yftw3p90IzEd3npCWmwJaLH+dSd/8LT/8KqEd4HfD4lBwRcd8vl/qlOMDF/UCgYEA1kBs+7y/EyTpfesc5gSI9l28fI1UcMZDZLYGIvy2+dgZtRIzjlhhYCHM/Hr+D/+d7qienzdsAkUk8UVvj5ZNyMXCHL4Q6PaCEECCPxeolJSzZhTu7CDnb0VXfncI5sc7ty4YrgtDOahj2QcRbVJm5gJnqnWENTiOOi+iEJST84sCgYEAwaeniLNdwKCzvCPP7WPHtR84ABvdCptuPJnJrPjBzloktNOLLzBs5iLFyFnLVVq4KvZhsgl996gLvzdNKUlWlk7089tKJPhBQl//+EamHm8yxE1ZxXDwzdbFVr3hAQrwf0wX7guk7gaWhZ0km2ICG9DJGKOIxWP+fTHhBCD+7ScCgYEAxNcOCb5gQkNwbap7s2xJNSfrivcBtFbDBh0hyDUsGqELBUvCqC3U0NBdFz4cezBatjCfAyKVDgTeqvgjReVOc34VF/kbah1IY4wQ3cxrqo5vwALNe43Dc3xepSbRu80htOsZ65UXXzqw+Wy/+xNwzDM7cOVk4MykBxtKJzeKMn8CgYA1z8OTDdx4YW9wUTc/DOH6p3ElPpjekLBCMK3E3d7k21Gl2b6sXzVzgagZ8zG4Rxwz/ADLuy2AuvaArPpJBbME023nsUDaIw3nRsvXDrKsdFPUDnZqkeaoHAz7/Ilgg8yuYtOndx7ecYA0IqWAc+yIFC09Jnwj9ZRK76hKQGz2MwKBgH7DAYNL8Wi8AWUm0gtRcHLWypnF7B1h468dzeiqOTDcCqfXuzx+PyGU2C4icgsZVyAjJxsvKa9Q907a7GP+AaYr3PI9lHHjKjsbed0AWXUNY+g4+laFTQfc1BT4X2c8s231HjcwC7BhkU6CryciZggNRFkzKIInxgXTsup3rXrb";
    private static final String ALIPAY_SERVER_URL = "https://openapi.alipay.com/gateway.do";

    @Autowired
    private GlPointsService glPointsService;
    @Resource
    private GlChangeBindDao glChangeBindDao;

    @Resource
    private GlAuthTokenDao glAuthTokenDao;

    @Resource
    private AES256Util aes256Util;


    @Override
    public CodeOpenid getAuthUser(AuthCodeDTO codeOpenidDto) {
        /**
         * 1.拿到userid和access_token
         * 2.拿到手机号
         */
        AlipayClient alipayClient = getAlipayClient();
        AlipayUserAuthData alipayUserAuthData = getAccessTokenAndUserId(alipayClient, codeOpenidDto.getJsCode());
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getAlipayOpenId, alipayUserAuthData.getAlipayUserId());
        glUserQuery.last("limit 1");
        GlUser glUser = baseMapper.selectOne(glUserQuery);
        // 获取手机号,content 不为空则解密手机号内容
        String mobile = StringUtils.isEmpty(codeOpenidDto.getContent()) ? getPhoneNumber(alipayClient, codeOpenidDto.getJsCode()) : decryptPhoneContent(codeOpenidDto.getContent());
        String cryptoMobile = aes256Util.encrypt(mobile);
        String mobileSha256 = SecureUtil.sha256(mobile);
        if (Objects.isNull(glUser)) {
            // 查询用户
            LambdaQueryWrapper<GlUser> glUserByMobileQuery = Wrappers.lambdaQuery();
            glUserByMobileQuery.eq(GlUser::getMobileSha256, mobileSha256);
            glUserByMobileQuery.last("limit 1");
            GlUser mobileGlUser = baseMapper.selectOne(glUserByMobileQuery);
            if (Objects.isNull(mobileGlUser)) {
                glUser = new GlUser();
                glUser.setMobile(cryptoMobile);
                glUser.setMobileSha256(mobileSha256);
                glUser.setAlipayOpenId(alipayUserAuthData.getAlipayUserId());
                glUser.setCreated(new Date());
                glUser.setUpdated(new Date());

                baseMapper.insert(glUser);
            } else {
                glUser = mobileGlUser;
                glUser.setMobile(cryptoMobile);
                glUser.setMobileSha256(mobileSha256);
                glUser.setAlipayOpenId(alipayUserAuthData.getAlipayUserId());
                glUser.setUpdated(new Date());
                baseMapper.updateById(glUser);
            }
        } else {
            // 查询用户
            LambdaQueryWrapper<GlUser> glUserByMobileQuery = Wrappers.lambdaQuery();
            glUserByMobileQuery.eq(GlUser::getMobileSha256, mobileSha256);
            glUserByMobileQuery.last("limit 1");
            GlUser mobileGlUser = baseMapper.selectOne(glUserByMobileQuery);
            if (Objects.nonNull(mobileGlUser)) {
                mobileGlUser.setMobile(cryptoMobile);
                mobileGlUser.setMobileSha256(mobileSha256);
                mobileGlUser.setAlipayOpenId(alipayUserAuthData.getAlipayUserId());
                mobileGlUser.setUpdated(new Date());
                mobileGlUser.setAvatarUrl(glUser.getAvatarUrl());
                mobileGlUser.setNickName(glUser.getNickName());
                baseMapper.updateById(mobileGlUser);
            } else {
                // 记录一下换绑手机号
                try {
                    GlChangeBind glChangeBind = new GlChangeBind();
                    glChangeBind.setOriginalMobile(glUser.getMobile());
                    glChangeBind.setOriginalMobileSha256(glUser.getMobileSha256());
                    glChangeBind.setMobile(cryptoMobile);
                    glChangeBind.setMobileSha256(mobileSha256);
                    glChangeBind.setUserId(glUser.getId());
                    glChangeBindDao.insert(glChangeBind);
                } catch (Exception e) {
                    log.error("新增换绑记录异常:{}", e);
                }
                glUser.setMobile(cryptoMobile);
                glUser.setMobileSha256(mobileSha256);
                glUser.setAlipayOpenId(alipayUserAuthData.getAlipayUserId());
                glUser.setUpdated(new Date());
                baseMapper.updateById(glUser);
            }
        }
        // 用户授权的时候 查询下积分表 算出来 总积分/剩余积分/使用积分
        glPointsService.calcUserPoints(glUser.getMobileSha256());

        LambdaQueryWrapper<GlAuthTokenDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(GlAuthTokenDO::getUserId, glUser.getId());
        GlAuthTokenDO glAuthTokenDO = glAuthTokenDao.selectOne(lambdaQueryWrapper);
        if (Objects.nonNull(glAuthTokenDO)) {
            glAuthTokenDO.setToken(IdUtil.fastSimpleUUID());
            glAuthTokenDao.updateById(glAuthTokenDO);
        } else {
            glAuthTokenDO = new GlAuthTokenDO();
            glAuthTokenDO.setToken(IdUtil.fastSimpleUUID());
            glAuthTokenDO.setUserId(glUser.getId());
            glAuthTokenDao.insert(glAuthTokenDO);
        }

        CodeOpenid codeOpenid = new CodeOpenid();
        codeOpenid.setAvatarUrl(glUser.getAvatarUrl());
        codeOpenid.setNickName(glUser.getNickName());
        codeOpenid.setOpenid(alipayUserAuthData.getAlipayUserId());
        codeOpenid.setMobileSha256(mobileSha256);
        codeOpenid.setToken(glAuthTokenDO.getToken());
        return codeOpenid;
    }

    /**
     * 通过接口获取手机号
     */
    public String getPhoneNumber(AlipayClient alipayClient, String accessToken) {
        AlipayUserInfoShareRequest request = new AlipayUserInfoShareRequest();
        AlipayUserInfoShareResponse response = null;
        try {
            response = alipayClient.execute(request, accessToken);
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
        if (response.isSuccess()) {
            return response.getPhone();
        } else {
            log.error("getPhoneError:", response.getBody());
            throw new GicWxAppException("授权失败,请重试或联系管理员");
        }
    }

    public String decryptPhoneContent(String response) {
        //1. 获取验签和解密所需要的参数
        Map<String, String> openapiResult = JSON.parseObject(response, new TypeReference<Map<String, String>>() {
        }, Feature.OrderedField);
        String signType = "RSA2";
        String charset = "UTF-8";
        String encryptType = "AES";
        String sign = openapiResult.get("sign");
        String content = openapiResult.get("response");
        //判断是否为加密内容
        boolean isDataEncrypted = !content.startsWith("{");
        boolean signCheckPass = false;
        //2. 验签
        String signContent = content;
        String signVeriKey = ALIPAY_PUBLIC_KEY;
        String decryptKey = "Aos+qOygt864VtvVHnfOww==";//如果是加密的报文则需要在密文的前后添加双引号
        if (isDataEncrypted) {
            signContent = "\"" + signContent + "\"";
        }
        try {
            signCheckPass = AlipaySignature.rsaCheck(signContent, sign, signVeriKey, charset, signType);
        } catch (AlipayApiException e) {
            // 验签异常, 日志
            log.error("验签异常", e);
        }
        if (!signCheckPass) {
            //验签不通过（异常或者报文被篡改），终止流程（不需要做解密）
            throw new GicWxAppException("授权失败,请您重试");
        }
        //3. 解密
        String plainData = null;
        if (isDataEncrypted) {
            try {
                plainData = AlipayEncrypt.decryptContent(content, encryptType, decryptKey, charset);
            } catch (AlipayApiException e) {
                log.error("解密异常", e);

                //解密异常, 记录日志
                throw new GicWxAppException("授权失败,请您重试");
            }
        } else {
            plainData = content;
        }
        AlipayGetPhoneNumberResponse alipayGetPhoneNumberResponse = JSON.parseObject(plainData, AlipayGetPhoneNumberResponse.class);
        if (alipayGetPhoneNumberResponse.getCode().equals("10000") && !StringUtils.isEmpty(alipayGetPhoneNumberResponse.getMobile())) {
            return alipayGetPhoneNumberResponse.getMobile();
        }
        log.error("decryptPhoneContent error:" + plainData);
        throw new GicWxAppException("授权失败,请您重试");
    }

    /**
     * 获取access_token
     */
    public AlipayUserAuthData getAccessTokenAndUserId(AlipayClient alipayClient, String authCode) {
        AlipayUserAuthData alipayUserAuthData = new AlipayUserAuthData();

        AlipaySystemOauthTokenResponse response = null;
        try {
            AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
            request.setCode(authCode);
            request.setGrantType("authorization_code");
            response = alipayClient.execute(request);
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }

        if (response.isSuccess()) {
            alipayUserAuthData.setAlipayUserId(response.getOpenId());
            alipayUserAuthData.setAccessToken(response.getAccessToken());

            return alipayUserAuthData;
        }
        log.error("getAccessTokenAndUserIdError:", response.getBody());

        throw new GicWxAppException("授权失败,请您重试");
    }

    private AlipayClient getAlipayClient() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(ALIPAY_SERVER_URL);
        alipayConfig.setAppId(ALIPAY_APPID);
        alipayConfig.setPrivateKey(ALIPAY_PRIVATE_KEY);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
        alipayConfig.setCharset("UTF8");
        alipayConfig.setSignType("RSA2");
        try {
            return new DefaultAlipayClient(alipayConfig);
        } catch (AlipayApiException e) {
            log.error("初始化支付宝链接支失败", e);
            throw new RuntimeException("初始化支付宝链接支失败");
        }
    }
}