package com.lvpuhui.gic.wxapp.treeplanting.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.treeplanting.dto.TreePlantingDTO;
import com.lvpuhui.gic.wxapp.treeplanting.service.TreePlantingService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/treePlanting")
public class TreePlantingController {

    @Resource
    private TreePlantingService treePlantingService;

    @Operation(summary = "保存植树信息")
    @PostMapping("save")
    public R save(@RequestBody @Validated TreePlantingDTO treePlantingDTO) {
        treePlantingService.save(treePlantingDTO);
        return R.ok("save");
    }
}
