package com.lvpuhui.gic.wxapp.carbon.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class InComeDetails {

    /**
     * 收益金额
     */
    private BigDecimal amount;


    /**
     * 收益名称
     */

    private String name;

    /**
     * 日期
     */

    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDateTime incomeDate;

    /**
     * 地址
     */
    private String address;


    /**
     * 剩余金额
     */

    private BigDecimal remainAmount;

    /**
     * 状态
     */
    @Schema(description = "0-不可用，1-可用")
    private Integer status;
}
