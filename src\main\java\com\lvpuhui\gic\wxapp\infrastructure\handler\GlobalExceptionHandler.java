package com.lvpuhui.gic.wxapp.infrastructure.handler;

import com.baomidou.mybatisplus.extension.api.IErrorCode;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppAuthException;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.MailUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;

import javax.annotation.Resource;

/**
 * 此类
 *
 * <AUTHOR>
 * @date 2020/12/1
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @Resource
    private MailUtils mailUtils;

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public R exception(Exception ex) {
        log.error(ex.getMessage(),ex);
        MDC.clear();
        mailUtils.sendMail("小程序系统出现异常", ex.getMessage());
        return R.failed("哎呦，服务出错啦！");
    }

    @ExceptionHandler(GicWxAppException.class)
    @ResponseStatus(HttpStatus.OK)
    public R handlerGicAccepterException(Exception ex) {
        log.error(ex.getMessage(),ex);
        MDC.clear();
        return R.failed(ex.getMessage());
    }

    @ExceptionHandler(GicWxAppAuthException.class)
    @ResponseStatus(HttpStatus.OK)
    public R handlerGicWxAppAuthException(GicWxAppAuthException ex) {
        log.error(ex.getMessage(),ex);
        MDC.clear();
        return R.failed(new IErrorCode() {
            @Override
            public long getCode() {
                return ex.getCode();
            }

            @Override
            public String getMsg() {
                return ex.getMessage();
            }
        });
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.OK)
    public R handlerHttpMessageNotReadableException(Exception ex) {
        log.error(ex.getMessage(),ex);
        MDC.clear();
        return R.failed("请检查您的参数格式");
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex){
        log.error(ex.getMessage(),ex);
        MDC.clear();
        return R.failed("请求类型不支持!");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<String> handlerValidException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return R.failed(message);
    }

    @ExceptionHandler(value = MultipartException.class)
    public R<String> handleFileSizeException(MaxUploadSizeExceededException ex) {
        log.error(ex.getMessage());
        return R.failed("上传文件过大[单文件大小不得超过20M]");
    }
}