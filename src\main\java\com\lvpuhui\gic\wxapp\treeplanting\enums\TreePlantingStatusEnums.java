package com.lvpuhui.gic.wxapp.treeplanting.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum TreePlantingStatusEnums {

    TO_BE_REVIEWED(0,"未出理"),
    PASS(1,"已核实"),
    REFUSE(2,"未采纳"),
    ;

    private Integer code;

    private String describe;

    TreePlantingStatusEnums(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static String getDescribeByCode(Integer code) {
        for (TreePlantingStatusEnums status : TreePlantingStatusEnums.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescribe();
            }
        }
        return null;
    }
}
