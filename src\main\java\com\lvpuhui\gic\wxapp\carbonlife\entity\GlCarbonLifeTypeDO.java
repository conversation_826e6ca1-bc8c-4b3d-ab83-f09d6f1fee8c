package com.lvpuhui.gic.wxapp.carbonlife.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @since 2023年10月30日 17:55:00
 */
/**
    * 低碳生活类别表
    */
@Schema(description="低碳生活类别表")
@Data
@TableName(value = "gl_carbon_life_type")
public class GlCarbonLifeTypeDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
     * 类别名称
     */
    @TableField(value = "`name`")
    @Schema(description="类别名称")
    private String name;

    /**
     * 场景类型排序权重,越大越靠前
     */
    @TableField(value = "`sequence`")
    @Schema(description="场景类型排序权重,越大越靠前")
    private Integer sequence;

    /**
     * 场景类型描述
     */
    @TableField(value = "description")
    @Schema(description="场景类型描述")
    private String description;

    /**
     * 是否删除   0否 1是
     */
    @TableField(value = "deleted")
    @Schema(description="是否删除   0否 1是")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private LocalDateTime created;

    /**
     * 修改时间
     */
    @TableField(value = "updated")
    @Schema(description="修改时间")
    private LocalDateTime updated;
}