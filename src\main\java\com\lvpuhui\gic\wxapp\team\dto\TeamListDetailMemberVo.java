package com.lvpuhui.gic.wxapp.team.dto;

import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 战队详情返回值
 * <AUTHOR>
 * @since 2023年05月24日 15:36:00
 */
@Data
@Schema(description = "战队详情成员返回值VO")
public class TeamListDetailMemberVo {

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickName;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatarUrl;

    /**
     * 减排量
     */
    @Schema(description = "减排量")
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    @Schema(description = "减排量-文本")
    private String emissionText;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return emissionText;
    }
}
