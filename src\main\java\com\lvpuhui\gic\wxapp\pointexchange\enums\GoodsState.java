package com.lvpuhui.gic.wxapp.pointexchange.enums;

import lombok.Getter;

/**
 * 商品state状态枚举
 * <AUTHOR>
 * @since 2022年05月05日 18:20:00
 */
@Getter
public enum GoodsState {
    UN_SHELF(0,"未上架"),
    SHELF(1,"上架"),
    OFF_SHELF(2,"下架"),
    ;

    private Integer state;

    private String describe;

    GoodsState(Integer state, String describe) {
        this.state = state;
        this.describe = describe;
    }
}
