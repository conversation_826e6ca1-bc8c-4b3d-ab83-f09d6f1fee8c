package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 视频观看记录表实体类(gl_video_record)
 * <AUTHOR>
 * @since 2022-06-01
 */
@Data
public class GlVideoRecord extends Model<GlVideoRecord> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 视频ID(gl_video表主键)
     */
    private Long videoId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 领到的积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 观看时间
     */
    private String watchTime;
}
