package com.lvpuhui.gic.wxapp.pointexchange.dao;

import com.lvpuhui.gic.wxapp.pointexchange.entity.GlGoods;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 商品表(GlGoods)表数据库访问层
 * <AUTHOR>
 * @since 2022年5月5日 19:21:08
 */
public interface GlGoodsDao extends BaseMapper<GlGoods> {

    @Update("update gl_goods set inventory_consumed=inventory_consumed+1,inventory_remain=inventory_remain-1 where id = #{id} and inventory_consumed <= inventory and inventory_remain > 0")
    int updateGoodsConsumedAndRemain(@Param("id") Long id);
}
