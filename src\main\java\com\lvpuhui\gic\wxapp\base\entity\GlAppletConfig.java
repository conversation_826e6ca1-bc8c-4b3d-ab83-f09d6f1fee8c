package com.lvpuhui.gic.wxapp.base.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 系统配置信息表(GlAppletConfig)表实体类
 *
 * <AUTHOR>
 * @since 2022-05-07 14:41:32
 */
@Data
@SuppressWarnings("serial")
public class GlAppletConfig extends Model<GlAppletConfig> {
  @TableId(type = IdType.AUTO)
    /**自增id*/
    private Long id;
    /**key*/
    private String paramKey;
    /**value*/
    private String paramValue;
    /**状态 0隐藏 1显示*/
    private Integer status;
    /**备注*/
    private String remark;

}