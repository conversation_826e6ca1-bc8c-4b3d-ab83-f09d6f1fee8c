package com.lvpuhui.gic.wxapp.pointexchange.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.homepage.dto.*;
import com.lvpuhui.gic.wxapp.pointexchange.constant.RollbackConstant;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankDaysDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankSnapshotDao;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRankDays;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRankSnapshot;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.pointexchange.dao.GlCertsDao;
import com.lvpuhui.gic.wxapp.pointexchange.dao.GlExchangeDao;
import com.lvpuhui.gic.wxapp.pointexchange.dao.GlGoodsDao;
import com.lvpuhui.gic.wxapp.pointexchange.dto.*;
import com.lvpuhui.gic.wxapp.pointexchange.entity.GlCerts;
import com.lvpuhui.gic.wxapp.pointexchange.entity.GlExchange;
import com.lvpuhui.gic.wxapp.pointexchange.entity.GlGoods;
import com.lvpuhui.gic.wxapp.pointexchange.enums.CertsExchanged;
import com.lvpuhui.gic.wxapp.pointexchange.enums.ExchangeType;
import com.lvpuhui.gic.wxapp.pointexchange.enums.GoodsExchangeType;
import com.lvpuhui.gic.wxapp.pointexchange.enums.GoodsState;
import com.lvpuhui.gic.wxapp.pointexchange.service.GlGoodsService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-05
 */
@Service
public class GlGoodsServiceImpl extends ServiceImpl<GlGoodsDao, GlGoods> implements GlGoodsService {

    @Autowired
    private GlUserDao glUserDao;

    @Autowired
    private GlCertsDao glCertsDao;

    @Autowired
    private GlExchangeDao glExchangeDao;

    @Autowired
    private GlPointsService glPointsService;

    @Resource
    private GlRankDaysDao glRankDaysDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Resource
    private GlRankSnapshotDao glRankSnapshotDao;

    @Override
    public List<Goods> goods() {
        // 查询未删除、上架的商品
        LambdaQueryWrapper<GlGoods> glGoodsQuery = Wrappers.lambdaQuery();
        glGoodsQuery.eq(GlGoods::getDeleted, Deleted.UN_DELETE.getDeleted());
        glGoodsQuery.eq(GlGoods::getState, GoodsState.SHELF.getState());
        glGoodsQuery.orderByDesc(GlGoods::getSequence);
        List<GlGoods> glGoods = baseMapper.selectList(glGoodsQuery);
        if(CollUtil.isEmpty(glGoods)){
            return Lists.newArrayList();
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        return glGoods.stream().map(goods -> {
            Goods gd = new Goods();
            gd.setId(goods.getId());
            gd.setName(goods.getName());
            gd.setCoverImage(imagePrefix + goods.getCoverImage());
            gd.setPoint(goods.getPoint());
            gd.setPrice(goods.getPrice());
            return gd;
        }).collect(Collectors.toList());
    }

    @Override
    public GoodsDetail goodsDetail(GoodsDetailDto goodsDetailDto) {
        String imagePrefix = glAppletConfigService.getImagePrefix();
        GlGoods glGoods = baseMapper.selectById(goodsDetailDto.getId());
        if(Objects.isNull(glGoods) || !GoodsState.SHELF.getState().equals(glGoods.getState())){
            throw new GicWxAppException("查询的商品不存在或者已下架");
        }
        GoodsDetail goodsDetail = new GoodsDetail();
        BeanUtil.copyProperties(glGoods,goodsDetail);
        goodsDetail.setAppletUrl("");
        goodsDetail.setCoverImage(imagePrefix + goodsDetail.getCoverImage());
        return goodsDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GoodsExchange goodsExchange(GoodsExchangeDto goodsExchangeDto) {
        // 查询商品
        GlGoods glGoods = baseMapper.selectById(goodsExchangeDto.getId());
        if(Objects.isNull(glGoods) || !GoodsState.SHELF.getState().equals(glGoods.getState())){
            throw new GicWxAppException("查询的商品不存在或者已下架");
        }
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,goodsExchangeDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        if(Objects.isNull(glGoods.getInventoryRemain()) || glGoods.getInventoryRemain() <= 0){
            throw new GicWxAppException(RollbackConstant.INSUFFICIENT_INVENTORY);
        }
        if(Objects.isNull(glUser.getPointRemain()) || glUser.getPointRemain() < glGoods.getPoint()){
            throw new GicWxAppException(RollbackConstant.INSUFFICIENT_POINT);
        }
        // 查询该商品已兑换过的次数是否超过了限制
        if(Objects.nonNull(glGoods.getExchangeLimit())){
            LambdaQueryWrapper<GlExchange> glExchangeQuery = Wrappers.lambdaQuery();
            glExchangeQuery.eq(GlExchange::getCreator,glUser.getId());
            glExchangeQuery.eq(GlExchange::getGoodsId,glGoods.getId());
            glExchangeQuery.eq(GlExchange::getExchangeType, ExchangeType.NORMAL.getState());
            Integer exchangeCount = glExchangeDao.selectCount(glExchangeQuery);
            if(Objects.nonNull(exchangeCount) && exchangeCount >= glGoods.getExchangeLimit()){
                throw new GicWxAppException("该商品您兑换次数已上限,谢谢您的支持");
            }
        }

        // 修改商品库存数据
        int updateCount = baseMapper.updateGoodsConsumedAndRemain(glGoods.getId());
        if(updateCount <= 0){
            throw new GicWxAppException("当前兑换人过多,操作过快,请重试");
        }
        String code = IdUtil.fastSimpleUUID();
        if(GoodsExchangeType.CERTS.getCode().equals(glGoods.getExchangeType())){
            // 查询兑换码未删除、未兑换的兑换码数据
            LambdaQueryWrapper<GlCerts> glCertsQuery = Wrappers.lambdaQuery();
            glCertsQuery.eq(GlCerts::getDeleted,Deleted.UN_DELETE.getDeleted());
            glCertsQuery.eq(GlCerts::getGoodsId,glGoods.getId());
            glCertsQuery.eq(GlCerts::getIsExchanged, CertsExchanged.UN_CHANGE.getState());
            glCertsQuery.orderByDesc(GlCerts::getId);
            glCertsQuery.last("limit 1 for update");
            GlCerts glCerts = glCertsDao.selectOne(glCertsQuery);
            if(Objects.isNull(glCerts)){
                throw new GicWxAppException(RollbackConstant.INSUFFICIENT_INVENTORY);
            }
            // 修改兑换码为已兑换状态
            glCerts.setIsExchanged(CertsExchanged.CHANGE.getState());
            glCertsDao.updateById(glCerts);

            code = glCerts.getCode();
        }

        String imagePrefix = glAppletConfigService.getImagePrefix();

        // 新增兑换明细数据
        GlExchange glExchange = new GlExchange();
        glExchange.setGoodsId(glGoods.getId());
        glExchange.setPoint(glGoods.getPoint());
        glExchange.setCode(code);
        glExchange.setCreated(new Date());
        glExchange.setCreator(glUser.getId());
        glExchange.setExchangeType(ExchangeType.NORMAL.getState());
        glExchangeDao.insert(glExchange);

        glPointsService.consumedPoints(glGoods,goodsExchangeDto.getMobileSha256());

        // 构建返回值对象
        GoodsExchange goodsExchange = new GoodsExchange();
        goodsExchange.setId(glExchange.getId());
        goodsExchange.setCoverImage(imagePrefix + glGoods.getCoverImage());
        goodsExchange.setName(glGoods.getName());
        goodsExchange.setPrice(glGoods.getPrice());
        goodsExchange.setPoint(glGoods.getPoint());
        goodsExchange.setCompanyName(glGoods.getCompanyName());
        goodsExchange.setCreated(new Date());
        goodsExchange.setDescription(glGoods.getDescription());
        goodsExchange.setContactNumber(glGoods.getContactNumber());
        goodsExchange.setCode(code);
        goodsExchange.setUrl(glGoods.getUrl());
        goodsExchange.setAppletAppid(glGoods.getAppletAppid());
        goodsExchange.setAppletUrl(glGoods.getAppletUrl());
        return goodsExchange;
    }

    @Override
    public List<GoodsUsed> goodsUsed(GoodsUsedDto goodsUsedDto) {
        // 根据mobileSha256查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,goodsUsedDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        // 查询用户已兑换的信息
        LambdaQueryWrapper<GlExchange> glExchangeQuery = Wrappers.lambdaQuery();
        glExchangeQuery.eq(GlExchange::getCreator,glUser.getId());
        glExchangeQuery.orderByDesc(GlExchange::getCreated);
        glExchangeQuery.last("limit 200");
        List<GlExchange> glExchanges = glExchangeDao.selectList(glExchangeQuery);
        if(CollUtil.isEmpty(glExchanges)){
            return Lists.newArrayList();
        }
        // 获取商品ID集合
        List<Long> goodsIds = glExchanges.stream().map(GlExchange::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
        // 查询商品
        List<GlGoods> glGoodsList = baseMapper.selectBatchIds(goodsIds);
        Map<Long, GlGoods> goodsIdToEntityMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glGoodsList)){
            // 转换为id和对象的map,方便后面取值
            goodsIdToEntityMap = glGoodsList.stream()
                    .collect(Collectors.toMap(GlGoods::getId, Function.identity(), (k1, k2) -> k1));
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        // 创建返回值容器对象
        List<GoodsUsed> goodsUseds = Lists.newArrayListWithCapacity(glExchanges.size());
        for (GlExchange glExchange : glExchanges) {
            GoodsUsed goodsUsed = new GoodsUsed();
            goodsUsed.setId(glExchange.getId());
            goodsUsed.setCreated(glExchange.getCreated());
            if(goodsIdToEntityMap.containsKey(glExchange.getGoodsId())){
                GlGoods glGoods = goodsIdToEntityMap.get(glExchange.getGoodsId());
                goodsUsed.setCoverImage(imagePrefix + glGoods.getCoverImage());
                goodsUsed.setName(glGoods.getName());
                goodsUsed.setPrice(glGoods.getPrice());
                goodsUsed.setPoint(glExchange.getPoint());
                goodsUsed.setCompanyName(glGoods.getCompanyName());
            }
            goodsUseds.add(goodsUsed);
        }
        return goodsUseds;
    }

    @Override
    public GoodsExchangedDetail goodsDetailUsed(GoodsExchangedDetailDto goodsExchangedDetailDto) {
        GlExchange glExchange = glExchangeDao.selectById(goodsExchangedDetailDto.getId());
        if(Objects.isNull(glExchange)){
            throw new GicWxAppException("您查看的兑换记录异常");
        }
        if(Objects.isNull(glExchange.getGoodsId())){
            throw new GicWxAppException("您查看的兑换记录异常");
        }
        GlGoods glGoods = baseMapper.selectById(glExchange.getGoodsId());
        if(Objects.isNull(glGoods)){
            throw new GicWxAppException("您兑换的商品信息已被删除,暂无法查看");
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        GoodsExchangedDetail goodsExchangedDetail = new GoodsExchangedDetail();
        goodsExchangedDetail.setId(glExchange.getId());
        goodsExchangedDetail.setCoverImage(imagePrefix + glGoods.getCoverImage());
        goodsExchangedDetail.setName(glGoods.getName());
        goodsExchangedDetail.setPrice(glGoods.getPrice());
        goodsExchangedDetail.setPoint(glExchange.getPoint());
        goodsExchangedDetail.setCompanyName(glGoods.getCompanyName());
        goodsExchangedDetail.setCreated(glExchange.getCreated());
        goodsExchangedDetail.setDescription(glGoods.getDescription());
        goodsExchangedDetail.setContactNumber(glGoods.getContactNumber());
        goodsExchangedDetail.setCode(glExchange.getCode());
        goodsExchangedDetail.setUrl(glGoods.getUrl());
        goodsExchangedDetail.setAppletAppid(glGoods.getAppletAppid());
        goodsExchangedDetail.setAppletUrl(glGoods.getAppletUrl());
        goodsExchangedDetail.setExchangeType(glGoods.getExchangeType());
        return goodsExchangedDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YesterdayObtainGoods yesterdayObtain(YesterdayObtainGoodsDto rankAccumulateListDto) {
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,rankAccumulateListDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        RankConfigDto rankConfig = glAppletConfigService.getRankConfig();
        if(Objects.isNull(rankConfig)
                || Objects.isNull(rankConfig.getYesterday())
                || StrUtil.isBlank(rankConfig.getYesterday().getCommodityId())
                || Objects.isNull(rankConfig.getYesterday().getDays())){
            throw new GicWxAppException("未配置可领取的商品,请联系管理员!");
        }
        RankGoodsConfigDto rankGoodsConfig = glAppletConfigService.getRankGoodsConfig();
        if(Objects.isNull(rankGoodsConfig)
                || Objects.isNull(rankGoodsConfig.getYesterdayLimit())){
            throw new GicWxAppException("领取的商品配置有误,请联系管理员");
        }
        Integer days = rankConfig.getYesterday().getDays();
        String endTime = rankConfig.getYesterday().getEndTime();
        String batchNo = endTime.replaceAll("-","").replaceAll(" ","").replaceAll(":","");
        Long goodsId = Long.parseLong(rankConfig.getYesterday().getCommodityId());

        // 查询当前用户的上榜天数
        LambdaQueryWrapper<GlRankDays> glRankDaysQuery = Wrappers.lambdaQuery();
        glRankDaysQuery.eq(GlRankDays::getMobileSha256,rankAccumulateListDto.getMobileSha256());
        glRankDaysQuery.eq(GlRankDays::getDeleted, Deleted.UN_DELETE.getDeleted());
        glRankDaysQuery.last("limit 1");
        GlRankDays glRankDays = glRankDaysDao.selectOne(glRankDaysQuery);
        if(Objects.isNull(glRankDays)){
            throw new GicWxAppException("您还未有上榜天数,请继续努力");
        }
        if(glRankDays.getDays() < days){
            throw new GicWxAppException("您上榜天数还未达到,请继续努力");
        }
        // 查询商品
        GlGoods glGoods = baseMapper.selectById(goodsId);
        if(Objects.isNull(glGoods)){
            throw new GicWxAppException("配置的商品暂不可领取,请您联系管理员");
        }
        // 查询该商品已兑换过的次数是否超过了限制
        LambdaQueryWrapper<GlExchange> glExchangeQuery = Wrappers.lambdaQuery();
        glExchangeQuery.eq(GlExchange::getCreator,glUser.getId());
        glExchangeQuery.eq(GlExchange::getGoodsId,glGoods.getId());
        glExchangeQuery.eq(GlExchange::getExchangeType, ExchangeType.RANK.getState());
        glExchangeQuery.eq(GlExchange::getBatchNo, batchNo);
        Integer exchangeCount = glExchangeDao.selectCount(glExchangeQuery);
        if(Objects.nonNull(exchangeCount) && exchangeCount >= rankGoodsConfig.getYesterdayLimit()){
            throw new GicWxAppException("您已领取上限,谢谢您的支持");
        }
        // 查询兑换码未删除、未兑换的兑换码数据
        LambdaQueryWrapper<GlCerts> glCertsQuery = Wrappers.lambdaQuery();
        glCertsQuery.eq(GlCerts::getDeleted,Deleted.UN_DELETE.getDeleted());
        glCertsQuery.eq(GlCerts::getGoodsId,glGoods.getId());
        glCertsQuery.eq(GlCerts::getIsExchanged, CertsExchanged.UN_CHANGE.getState());
        glCertsQuery.orderByDesc(GlCerts::getId);
        glCertsQuery.last("limit 1 for update");
        GlCerts glCerts = glCertsDao.selectOne(glCertsQuery);
        if(Objects.isNull(glCerts)){
            throw new GicWxAppException("您来晚了,商品已被领取完毕,感谢您的参与");
        }
        // 修改商品库存数据
        int updateCount = baseMapper.updateGoodsConsumedAndRemain(glGoods.getId());
        if(updateCount <= 0){
            throw new GicWxAppException("当前兑换人过多,操作过快,请重试");
        }

        String imagePrefix = glAppletConfigService.getImagePrefix();

        // 修改兑换码为已兑换状态
        glCerts.setIsExchanged(CertsExchanged.CHANGE.getState());
        glCertsDao.updateById(glCerts);

        // 新增兑换明细数据
        GlExchange glExchange = new GlExchange();
        glExchange.setGoodsId(glGoods.getId());
        glExchange.setPoint(0);
        glExchange.setCode(glCerts.getCode());
        glExchange.setCreated(new Date());
        glExchange.setCreator(glUser.getId());
        glExchange.setExchangeType(ExchangeType.RANK.getState());
        glExchange.setBatchNo(batchNo);
        glExchangeDao.insert(glExchange);

        glPointsService.rankGoodsPoints(glGoods,glUser.getMobileSha256());

        // 构建返回值对象
        YesterdayObtainGoods yesterdayObtainGoods = new YesterdayObtainGoods();
        yesterdayObtainGoods.setId(glExchange.getId());
        yesterdayObtainGoods.setCoverImage(imagePrefix + glGoods.getCoverImage());
        yesterdayObtainGoods.setName(glGoods.getName());
        yesterdayObtainGoods.setPrice(glGoods.getPrice());
        yesterdayObtainGoods.setPoint(glGoods.getPoint());
        yesterdayObtainGoods.setCompanyName(glGoods.getCompanyName());
        yesterdayObtainGoods.setCreated(new Date());
        yesterdayObtainGoods.setDescription(glGoods.getDescription());
        yesterdayObtainGoods.setContactNumber(glGoods.getContactNumber());
        yesterdayObtainGoods.setCode(glCerts.getCode());
        yesterdayObtainGoods.setUrl(glGoods.getUrl());
        yesterdayObtainGoods.setAppletAppid(glGoods.getAppletAppid());
        yesterdayObtainGoods.setAppletUrl(glGoods.getAppletUrl());
        return yesterdayObtainGoods;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccumulateObtainGoods accumulateObtain(AccumulateObtainGoodsDto accumulateObtainGoodsDto) {
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,accumulateObtainGoodsDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        RankConfigDto rankConfig = glAppletConfigService.getRankConfig();
        if(Objects.isNull(rankConfig)
                || Objects.isNull(rankConfig.getCumulative())
                || StrUtil.isBlank(rankConfig.getCumulative().getCommodityId())
                || Objects.isNull(rankConfig.getCumulative().getTop())){
            throw new GicWxAppException("未配置可领取的商品,请联系管理员!");
        }
        RankGoodsConfigDto rankGoodsConfig = glAppletConfigService.getRankGoodsConfig();
        if(Objects.isNull(rankGoodsConfig)
                || Objects.isNull(rankGoodsConfig.getCumulativeLimit())){
            throw new GicWxAppException("领取的商品配置有误,请联系管理员");
        }
        String endTime = rankConfig.getCumulative().getEndTime();
        String batchNo = endTime.replaceAll("-","").replaceAll(" ","").replaceAll(":","");
        DateTime endDateTime = DateUtil.parseDateTime(endTime);
        if(DateUtil.date().isBefore(endDateTime)){
            throw new GicWxAppException("截止时间还未到,暂不能领取,截止时间为："+endTime);
        }
        Integer top = rankConfig.getCumulative().getTop();
        Long goodsId = Long.parseLong(rankConfig.getCumulative().getCommodityId());

        LambdaQueryWrapper<GlRankSnapshot> glRankSnapshotQuery = Wrappers.lambdaQuery();
        glRankSnapshotQuery.eq(GlRankSnapshot::getMobileSha256,glUser.getMobileSha256());
        glRankSnapshotQuery.orderByAsc(GlRankSnapshot::getRank);
        glRankSnapshotQuery.last("limit 1");
        GlRankSnapshot glRankSnapshot = glRankSnapshotDao.selectOne(glRankSnapshotQuery);
        if(Objects.isNull(glRankSnapshot)){
            throw new GicWxAppException("您未有排名数据,不能领取商品,请稍后再试");
        }
        if(Objects.isNull(glRankSnapshot.getRank())){
            throw new GicWxAppException("您的排名不符合,不能领取商品");
        }
        if(glRankSnapshot.getRank() > top){
            throw new GicWxAppException("您的排名大于" + top + "名,不能领取,感谢您的参与");
        }

        // 查询商品
        GlGoods glGoods = baseMapper.selectById(goodsId);
        if(Objects.isNull(glGoods)){
            throw new GicWxAppException("配置的商品暂不可领取,请您联系管理员");
        }
        // 查询该商品已兑换过的次数是否超过了限制
        LambdaQueryWrapper<GlExchange> glExchangeQuery = Wrappers.lambdaQuery();
        glExchangeQuery.eq(GlExchange::getCreator,glUser.getId());
        glExchangeQuery.eq(GlExchange::getGoodsId,glGoods.getId());
        glExchangeQuery.eq(GlExchange::getExchangeType, ExchangeType.ACCUMULATE_RANK.getState());
        glExchangeQuery.eq(GlExchange::getBatchNo, batchNo);
        Integer exchangeCount = glExchangeDao.selectCount(glExchangeQuery);
        if(Objects.nonNull(exchangeCount) && exchangeCount >= rankGoodsConfig.getCumulativeLimit()){
            throw new GicWxAppException("您已领取上限,谢谢您的支持");
        }
        // 查询兑换码未删除、未兑换的兑换码数据
        LambdaQueryWrapper<GlCerts> glCertsQuery = Wrappers.lambdaQuery();
        glCertsQuery.eq(GlCerts::getDeleted,Deleted.UN_DELETE.getDeleted());
        glCertsQuery.eq(GlCerts::getGoodsId,glGoods.getId());
        glCertsQuery.eq(GlCerts::getIsExchanged, CertsExchanged.UN_CHANGE.getState());
        glCertsQuery.orderByDesc(GlCerts::getId);
        glCertsQuery.last("limit 1 for update");
        GlCerts glCerts = glCertsDao.selectOne(glCertsQuery);
        if(Objects.isNull(glCerts)){
            throw new GicWxAppException("您来晚了,商品已被领取完毕,感谢您的参与");
        }
        // 修改商品库存数据
        int updateCount = baseMapper.updateGoodsConsumedAndRemain(glGoods.getId());
        if(updateCount <= 0){
            throw new GicWxAppException("当前兑换人过多,操作过快,请重试");
        }

        String imagePrefix = glAppletConfigService.getImagePrefix();

        // 修改兑换码为已兑换状态
        glCerts.setIsExchanged(CertsExchanged.CHANGE.getState());
        glCertsDao.updateById(glCerts);

        // 新增兑换明细数据
        GlExchange glExchange = new GlExchange();
        glExchange.setGoodsId(glGoods.getId());
        glExchange.setPoint(0);
        glExchange.setCode(glCerts.getCode());
        glExchange.setCreated(new Date());
        glExchange.setCreator(glUser.getId());
        glExchange.setExchangeType(ExchangeType.ACCUMULATE_RANK.getState());
        glExchange.setBatchNo(batchNo);
        glExchangeDao.insert(glExchange);

        glPointsService.rankGoodsPoints(glGoods,glUser.getMobileSha256());

        // 构建返回值对象
        AccumulateObtainGoods accumulateObtainGoods = new AccumulateObtainGoods();
        accumulateObtainGoods.setId(glExchange.getId());
        accumulateObtainGoods.setCoverImage(imagePrefix + glGoods.getCoverImage());
        accumulateObtainGoods.setName(glGoods.getName());
        accumulateObtainGoods.setPrice(glGoods.getPrice());
        accumulateObtainGoods.setPoint(glGoods.getPoint());
        accumulateObtainGoods.setCompanyName(glGoods.getCompanyName());
        accumulateObtainGoods.setCreated(new Date());
        accumulateObtainGoods.setDescription(glGoods.getDescription());
        accumulateObtainGoods.setContactNumber(glGoods.getContactNumber());
        accumulateObtainGoods.setCode(glCerts.getCode());
        accumulateObtainGoods.setUrl(glGoods.getUrl());
        accumulateObtainGoods.setAppletAppid(glGoods.getAppletAppid());
        accumulateObtainGoods.setAppletUrl(glGoods.getAppletUrl());
        return accumulateObtainGoods;
    }
}
