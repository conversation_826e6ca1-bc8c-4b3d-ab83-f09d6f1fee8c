package com.lvpuhui.gic.wxapp.carbonlife.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 
 * <AUTHOR>
 * @since 2023年10月30日 17:55:00
 */
/**
    * 企业产品表
    */
@Schema(description="企业产品表")
@Data
@TableName(value = "gl_company_product")
public class LifeCompanyProductDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

    /**
     * 产品名称
     */
    @TableField(value = "`name`")
    @Schema(description="产品名称")
    private String name;

    /**
     * 企业名称
     */
    @TableField(value = "company_name")
    @Schema(description="企业名称")
    private String companyName;

    /**
     * 企业ID
     */
    @TableField(value = "company_id")
    @Schema(description="企业ID")
    private Integer companyId;

    /**
     * 场景类别ID
     */
    @TableField(value = "category_id")
    @Schema(description="场景类别ID")
    private Long categoryId;

    /**
     * logo图
     */
    @TableField(value = "logo_image")
    @Schema(description="logo图")
    private String logoImage;

    /**
     * 位置权重
     */
    @TableField(value = "`sequence`")
    @Schema(description="位置权重")
    private Integer sequence;

    /**
     * 跳转地址
     */
    @TableField(value = "jump_url")
    @Schema(description="跳转地址")
    private String jumpUrl;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private LocalDateTime created;

    /**
     * 创建者ID
     */
    @TableField(value = "creator")
    @Schema(description="创建者ID")
    private Long creator;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    @Schema(description="更新时间")
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    @TableField(value = "updator")
    @Schema(description="修改者ID")
    private Long updator;

    /**
     * 删除 0未删除 1已删除
     */
    @TableField(value = "deleted")
    @Schema(description="删除 0未删除 1已删除")
    private Object deleted;

    /**
     * 小程序appid
     */
    @TableField(value = "applet_appid")
    @Schema(description="小程序appid")
    private String appletAppid;

    /**
     * 小程序跳转地址
     */
    @TableField(value = "applet_url")
    @Schema(description="小程序跳转地址")
    private String appletUrl;

    /**
     * 活动说明
     */
    @TableField(value = "product_describe")
    @Schema(description="活动说明")
    private String productDescribe;
}