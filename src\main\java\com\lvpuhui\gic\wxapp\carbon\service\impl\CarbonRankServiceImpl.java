package com.lvpuhui.gic.wxapp.carbon.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonRankDao;
import com.lvpuhui.gic.wxapp.carbon.dto.CarbonRankUserVo;
import com.lvpuhui.gic.wxapp.carbon.dto.CarbonRankVo;
import com.lvpuhui.gic.wxapp.carbon.service.CarbonRankService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 碳汇排行service实现
 * <AUTHOR>
 * @since 2023年03月27日 17:18:00
 */
@Slf4j
@Service
public class CarbonRankServiceImpl implements CarbonRankService {

    @Resource
    private GlCarbonRankDao glCarbonRankDao;

    @Resource
    private GlUserDao glUserDao;

    @Override
    public CarbonRankVo carbonRank() {
        CarbonRankVo carbonRankVo = new CarbonRankVo();
        List<CarbonRankUserVo> carbonRankUserVos = glCarbonRankDao.carbonRankList();
        carbonRankVo.setCarbonRankUserVos(carbonRankUserVos);

        if(UserUtils.userIsAuth()){
            String currentMobileSha256 = UserUtils.getMobileSha256();
            if(CollUtil.isNotEmpty(carbonRankUserVos)){
                carbonRankUserVos.forEach(carbonRankUserVo -> {
                    if(currentMobileSha256.equals(carbonRankUserVo.getMobileSha256())){
                        carbonRankVo.setCurrentUser(carbonRankUserVo);
                    }
                });
            }
            if(Objects.isNull(carbonRankVo.getCurrentUser())){
                CarbonRankUserVo currentUserRank = glCarbonRankDao.getCurrentUserRank(currentMobileSha256);
                carbonRankVo.setCurrentUser(currentUserRank);
            }
            if(Objects.isNull(carbonRankVo.getCurrentUser())){
                GlUser glUser = glUserDao.selectById(UserUtils.getUserId());
                CarbonRankUserVo currentUserRank = new CarbonRankUserVo();
                currentUserRank.setRank(null);
                currentUserRank.setMobileSha256(glUser.getMobileSha256());
                currentUserRank.setAvatarUrl(glUser.getAvatarUrl());
                currentUserRank.setNickName(glUser.getNickName());
                currentUserRank.setEstimateAmountSum(BigDecimal.ZERO);
                carbonRankVo.setCurrentUser(currentUserRank);
            }
        }
        return carbonRankVo;
    }

    @Override
    public CarbonRankVo indexCarbonRank() {
        CarbonRankVo carbonRankVo = new CarbonRankVo();
        List<CarbonRankUserVo> carbonRankUserVos = glCarbonRankDao.carbonRankList();
        carbonRankVo.setCarbonRankUserVos(carbonRankUserVos);
        return carbonRankVo;
    }
}
