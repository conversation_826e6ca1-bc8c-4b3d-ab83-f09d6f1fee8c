package com.lvpuhui.gic.wxapp.homepage.dao;

import com.lvpuhui.gic.wxapp.homepage.entity.GlRankCompany;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.homepage.dto.CompanyRank;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 企业排行表 Mapper 接口
 * <AUTHOR>
 * @since 2022-08-25
 */
public interface GlRankCompanyDao extends BaseMapper<GlRankCompany> {

    @Select("SELECT " +
            "rankCompany.logo,rankCompany.companyId, " +
            "rankCompany.companyName, " +
            "rankCompany.tenant_id AS tenantId, " +
            "(IFNULL( rankCompany.rankEmission, 0 ) + IFNULL( rankCompany.correctEmission, 0 )) AS emission  " +
            "FROM " +
            "( " +
            "SELECT " +
            "gc.logo,gc.id AS companyId, " +
            "gc.`name` AS companyName, " +
            "gc.tenant_id, " +
            "grc.emission AS rankEmission, " +
            "gcc.emission AS correctEmission  " +
            "FROM " +
            "gl_rank_company grc " +
            "INNER JOIN gl_company gc ON grc.tenant_id = gc.tenant_id " +
            "LEFT JOIN gl_correct_company gcc ON gcc.tenant_id = grc.tenant_id  " +
            ") rankCompany")
    List<CompanyRank> getCompanyRank();
}
