package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CarbonModelMessgae {


    private Long modelId;

    /**
     * 模块名称
     */
    private String modelName;

    /**
     * 今年预计收入
     */
    @Schema(description = "今年预计收益")
    private BigDecimal modelAmount;

    /**
     * 面积
     */
    @Schema(description = "面积")
    private BigDecimal area;

    /**
     * 累计奖励
     */
    @Schema(description = "累计奖励")
    private BigDecimal rewardAmount;

    /**
     * 累计惩罚
     */
    @Schema(description = "累计惩罚")
    private BigDecimal punAmount;

    /**
     * 状态
     */
    @Schema(description = "状态 0-待审核，1-正常，2-审核不通过，3-年审中，4，停用")
    private Integer status;
}
