package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 答题记录表实体类
 * <AUTHOR>
 * @since 2022-06-21
 */
@Data
public class GlSubjectRecord extends Model<GlSubjectRecord> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 领到的积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 答题日期
     */
    private String subjectTime;

    /**
     * 创建时间
     */
    private Date created;
}
