package com.lvpuhui.gic.wxapp.activety.service;

import com.lvpuhui.gic.wxapp.activety.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ActivityService {
    /**
     * 发布活动
     * @param publishActivityDto
     */
    void publishActivity(PublishActivityDto publishActivityDto);

    /**
     * 活动列表
     * @return
     */
    List<ActivityListVo> queryAllActivityList();

    /**
     * 活动详情
     * @param activityId
     * @return
     */
    ActivityDetailsVo getActivityDetails(Long activityId);

    /**
     * 我得活动列表
     * @return
     */
    List<ActivityListVo> getMyActivityList();

    /**
     * 报名
     * @param signUpDto
     */
    void signUp(SignUpDto signUpDto);

    /**
     * 新闻列表
     * @return
     */
    List<NewsListVo> newsList();

    /**
     * 修改活动
     * @param updateDto
     */
    void updateActity(UpdateDto updateDto);
}
