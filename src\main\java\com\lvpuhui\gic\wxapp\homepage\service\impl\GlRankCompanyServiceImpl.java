package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRankCompany;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankCompanyDao;
import com.lvpuhui.gic.wxapp.homepage.dto.CompanyRank;
import com.lvpuhui.gic.wxapp.homepage.service.GlRankCompanyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

/**
 * 企业排行表 服务实现类
 * <AUTHOR>
 * @since 2022-08-25
 */
@Service
public class GlRankCompanyServiceImpl extends ServiceImpl<GlRankCompanyDao, GlRankCompany> implements GlRankCompanyService {

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Override
    public List<CompanyRank> companyRank() {
        List<CompanyRank> companyRanks = baseMapper.getCompanyRank();
        if(CollUtil.isEmpty(companyRanks)){
            return Lists.newArrayList();
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        companyRanks.sort(Comparator.comparing(CompanyRank::getEmission).reversed());
        int rankIndex = 0;
        for (CompanyRank companyRank : companyRanks) {
            companyRank.setLogo(imagePrefix + companyRank.getLogo());
            companyRank.setRank(++rankIndex);
        }
        return companyRanks;
    }
}
