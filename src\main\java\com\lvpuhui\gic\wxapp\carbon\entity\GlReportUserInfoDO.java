package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 举报人信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_report_user_info")
public class GlReportUserInfoDO extends Model<GlReportUserInfoDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    private String fullName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 创建时间
     */
    private LocalDateTime created;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
