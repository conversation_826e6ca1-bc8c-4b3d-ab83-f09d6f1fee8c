package com.lvpuhui.gic.wxapp.base.storage.impl;

import cn.hutool.core.io.IoUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.lvpuhui.gic.wxapp.base.storage.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2023年04月07日 16:15:00
 */
@Slf4j
@Service("ossStorageService")
public class OssStorageService implements StorageService {

    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.bucketName}")
    private String bucketName;

    @Value("${file.path}")
    private String path;

    @Override
    public String uploadStream(InputStream inputStream, String fileName) {
        String ossFileName = path + fileName;
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, ossFileName, inputStream);
            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");
            // 创建PutObject请求。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            if(result.getResponse().isSuccessful()){
                return fileName;
            }
            return null;
        } catch (Exception e) {
            log.error("upload oss error:{}",e);
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @Override
    public void downloadToResponse(String fileName, HttpServletResponse response) {
        String ossFileName = path + fileName;
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            boolean found = ossClient.doesObjectExist(bucketName, ossFileName);
            if(!found){
                return;
            }
            // ossObject包含文件所在的存储空间名称、文件名称、文件元信息以及一个输入流。
            OSSObject ossObject = ossClient.getObject(bucketName, ossFileName);
            try (InputStream inputStream = ossObject.getObjectContent()){
                IoUtil.copy(inputStream,response.getOutputStream());
            }
        } catch (Exception e) {
            log.error("下载文件异常:{}",e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @Override
    public void deleteFile(String fileName) {
        String ossFileName = path + fileName;
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            ossClient.deleteObject(bucketName, ossFileName);
        } catch (Exception e) {
            log.error("删除文件异常",e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}
