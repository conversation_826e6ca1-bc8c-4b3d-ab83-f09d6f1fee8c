package com.lvpuhui.gic.wxapp.homepage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.entity.GlQuestion;
import com.lvpuhui.gic.wxapp.homepage.dto.QuestionFinishDto;
import com.lvpuhui.gic.wxapp.homepage.dto.QuestionListDto;
import com.lvpuhui.gic.wxapp.homepage.dto.QuestionFinish;
import com.lvpuhui.gic.wxapp.homepage.dto.QuestionList;

import java.util.List;

/**
 * 小程序问卷 服务类
 * <AUTHOR>
 * @since 2022-06-13
 */
public interface GlQuestionService extends IService<GlQuestion> {

    /**
     * 问卷列表接口
     */
    List<QuestionList> questionList(QuestionListDto questionListDto);

    /**
     * 问卷完成接口
     */
    QuestionFinish questionFinish(QuestionFinishDto questionFinishDto);
}
