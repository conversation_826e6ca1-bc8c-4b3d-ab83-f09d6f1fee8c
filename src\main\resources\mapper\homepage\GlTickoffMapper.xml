<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.homepage.dao.GlTickoffDao">

    <select id="selectTickTypeName" resultType="com.lvpuhui.gic.wxapp.homepage.dto.TickTypeNameDto">
        SELECT
            gt.id AS tickOffId,
            gtt.`name` AS tickTypeName
        FROM
            gl_tickoff gt
                LEFT JOIN gl_tickoff_type gtt ON gt.type_id = gtt.id
        WHERE
            gt.id IN
        <foreach collection="tickOffIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
