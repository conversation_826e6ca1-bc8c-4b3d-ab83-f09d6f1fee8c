package com.lvpuhui.gic.wxapp.carbonlife.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "低碳生活返回值VO")
public class CarbonLifeVo {

    @Schema(description="类别ID")
    private Long id;

    @Schema(description="类别名称")
    private String name;

    @Schema(description="场景集合")
    private List<CarbonLifeSceneVo> carbonLifeSceneVos;

    @Data
    @Schema(description = "低碳生活场景返回值VO")
    public static class CarbonLifeSceneVo {

        @Schema(description="场景名称")
        private String name;

        @Schema(description="场景LOGO")
        private String logoImage;

        @Schema(description="场景减排量注释")
        private String description;

        @Schema(description="场景类别ID")
        private Long typeId;

        @Schema(description="是否已行动 true:已行动 false:未行动")
        private Boolean action = false;

        @Schema(description="产品集合")
        private List<NameIdVo> productList;
    }

    @Data
    @Schema(description = "ID和名称VO")
    public static class NameIdVo{

        @Schema(description="ID")
        private Long id;

        @Schema(description="名称")
        private String companyName;

        @Schema(description="LOGO图片")
        private String logoImage;

        @Schema(description="小程序appid")
        private String appletAppid;

        @Schema(description="小程序跳转地址")
        private String appletUrl;
    }
}
