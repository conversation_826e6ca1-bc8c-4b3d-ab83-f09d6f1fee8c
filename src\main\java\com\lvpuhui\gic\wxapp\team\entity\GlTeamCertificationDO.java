package com.lvpuhui.gic.wxapp.team.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 战队加V表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_team_certification")
public class GlTeamCertificationDO extends Model<GlTeamCertificationDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 战队ID
     */
    private Long teamId;

    /**
     * 申请人userId
     */
    private Long userId;

    /**
     * 审核状态 0：审核中  -1：审核不通过  1：已认证
     */
    private Integer status;

    /**
     * 组织名称
     */
    private String organizationName;

    /**
     * 营业执照照片URL
     */
    private String organizationLicense;

    /**
     * 申请人姓名
     */
    private String applicant;

    /**
     * 申请人身份证号
     */
    private String applicantNumber;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime updated;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
