package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 补贴档位
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_subsidy_gear")
public class GlCarbonSubsidyGearDO extends Model<GlCarbonSubsidyGearDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 补贴名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 补贴年度
     */
    @TableField("`year`")
    private Integer year;

    /**
     * 补贴主体
     */
    @TableField("`subject`")
    private Integer subject;

    /**
     * 补贴判断条件
     */
    @TableField("`condition`")
    private Integer condition;

    /**
     * 起始减排量
     */
    private BigDecimal beginEmission;

    /**
     * 终止减排量
     */
    private BigDecimal endEmission;

    /**
     * 补贴限额
     */
    private BigDecimal subsidyQuota;

    /**
     * 补贴单价（每吨）
     */
    private BigDecimal subsidyPrice;

    /**
     * 状态 0未删除 1已删除
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    private Long updator;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
