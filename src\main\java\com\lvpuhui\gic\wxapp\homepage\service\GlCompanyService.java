package com.lvpuhui.gic.wxapp.homepage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.entity.GlCompany;

/**
 * 企业表(GlCompany)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 14:41:15
 */
public interface GlCompanyService extends IService<GlCompany> {
    /**
     * 获取企业信息 根据租户id 带有缓存
     * @param tenantId
     * @return
     */
    GlCompany getCacheCompanyByTenantId(Long tenantId);
    GlCompany getCacheCompanyById(Long id);
}