package com.lvpuhui.gic.wxapp.my.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_business_points_log")
public class GlBusinessPointsLogDO extends Model<GlBusinessPointsLogDO> {

    /**
     * 活动ID
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;


    private Long userId;

    private String businessKey;

    private Integer businessType;

    private Integer points;

    private LocalDate created;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
