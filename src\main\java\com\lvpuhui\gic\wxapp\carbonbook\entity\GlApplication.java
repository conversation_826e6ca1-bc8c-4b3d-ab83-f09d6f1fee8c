package com.lvpuhui.gic.wxapp.carbonbook.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 应用表
 * <AUTHOR>
 * @since 2023年02月23日 17:26:00
 */
@Data
@TableName("gl_application")
public class GlApplication extends Model<GlApplication> {

    @TableId(type = IdType.INPUT)
    private Long id;

    private String name;

    private Date created;

    private Date updated;
}
