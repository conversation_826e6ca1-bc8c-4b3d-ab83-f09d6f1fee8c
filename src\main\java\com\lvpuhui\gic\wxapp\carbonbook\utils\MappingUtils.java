package com.lvpuhui.gic.wxapp.carbonbook.utils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.base.dao.GlAppletConfigDao;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.carbonbook.dto.SceneVo;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 映射关系服务
 * <AUTHOR>
 * @since 2022年10月20日 10:36:00
 */
@Component
public class MappingUtils {

    @Resource
    private GlAppletConfigDao glAppletConfigDao;

    public List<SceneConfig> sceneConfigs(){
        GlAppletConfig rankSettingsConfig = glAppletConfigDao.selectOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.MAPPING));
        return JSON.parseArray(rankSettingsConfig.getParamValue(), SceneConfig.class);
    }

    public String getCarbonProjectIds(){
        GlAppletConfig rankSettingsConfig = glAppletConfigDao.selectOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.CARBON_PROJECT_IDS));
        return rankSettingsConfig.getParamValue();
    }

    public List<Long> getMapping(Long sId){
        List<SceneConfig> sceneConfigs = sceneConfigs();
        Map<Long, SceneConfig> sceneConfigMap = sceneConfigs.stream().collect(Collectors.toMap(SceneConfig::getId, Function.identity(), (k1, k2) -> k1));
        return sceneConfigMap.get(sId).getIds();
    }

    public Map<Long, SceneConfig> getMappings(){
        List<SceneConfig> sceneConfigs = sceneConfigs();
        return sceneConfigs.stream().collect(Collectors.toMap(SceneConfig::getId, Function.identity(), (k1, k2) -> k1));
    }

    public List<SceneVo> buildScenarios(List<SceneVo> scenarioList){
        List<SceneConfig> sceneConfigs = sceneConfigs();
        return sceneConfigs.stream().map(sceneConfig -> {
            List<SceneVo> scenarios = getScenario(scenarioList, sceneConfig.getIds());
            SceneVo sceneVo = new SceneVo();
            sceneVo.setId(sceneConfig.getId().intValue());
            sceneVo.setName(sceneConfig.getName());
            double emission =CollectionUtils.isEmpty(scenarios)?0: scenarios.stream().mapToDouble(SceneVo::getEmissionNum).sum();
            sceneVo.setEmissionNum(emission);
            sceneVo.setEmission(CalcUtil.weightFormat(emission));
            return sceneVo;
        }).collect(Collectors.toList());
    }

    public List<SceneVo> getScenario(List<SceneVo> scenarioList, List<Long> ids){
        List<SceneVo> list = Lists.newArrayList();
        if(CollectionUtils.isEmpty(scenarioList)){
            return list;
        }
        List<Long> longs = ids.stream().distinct().collect(Collectors.toList());
        for(SceneVo scenario:scenarioList){
            longs.forEach(l ->{
                if(scenario.getId() == l.intValue()){
                    list.add(scenario);
                }
            });
        }
        return list;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SceneConfig{
        private Long id;

        private String name;

        private List<Long> ids;
    }
}
