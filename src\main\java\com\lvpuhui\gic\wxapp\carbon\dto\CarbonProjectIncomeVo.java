package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "预计收入返回值")
public class CarbonProjectIncomeVo {


    /**
     * 今年预计收益金额
     */
    @Schema(description = "今年预计收益")
    private BigDecimal amount;

    /**
     * 预计碳汇减排量
     */
    @Schema(description = "预计减排量")
    private BigDecimal emission;

    /**
     * 单位名称
     */
    @Schema(description = "单位名称")
    private String singleName;

    /**
     * 补贴价格
     */
    @Schema(description = "补贴价格")
    private BigDecimal subsidyPrice;

    /**
     * 奖惩累计
     */
    @Schema(description = "奖惩累计")
    private BigDecimal rewordAmount;

    @Schema(description = "总钱数")
    private BigDecimal totalAmount;

    @Schema(description = "总吨数")
    private BigDecimal totalEmission;


    /**
     * 项目信息
     */
    @Schema(description = "项目信息")
    private List<CarbonModelIncome> projectList;

}
