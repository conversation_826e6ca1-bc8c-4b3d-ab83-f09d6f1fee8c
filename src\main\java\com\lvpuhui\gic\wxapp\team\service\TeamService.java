package com.lvpuhui.gic.wxapp.team.service;

import com.lvpuhui.gic.wxapp.team.dto.*;

import java.util.List;

/**
 * 战队service
 * <AUTHOR>
 * @since 2023年05月24日 15:11:00
 */
public interface TeamService {

    /**
     * 我的战队信息
     */
    TeamMeVo meTeam();

    /**
     * 创建战队接口
     */
    void createTeam(TeamCreateDto teamCreateDto);

    /**
     * 战队列表接口
     */
    List<TeamListVo> teamList(TeamListDto teamListDto);

    /**
     * 战队列表-战队详情接口
     */
    TeamListDetailVo teamListDetail(Long id);

    /**
     * 我的战队详情接口
     */
    TeamDetailVo teamDetail();

    /**
     * 战队排行榜接口
     */
    TeamRankVo teamRank();

    /**
     * 对内排行榜接口
     */
    TeamMemberRankVo teamMemberRank();

    /**
     * 加入战队接口
     */
    void teamJoin(TeamJoinDto teamJoinDto);

    /**
     * 退出战队接口
     */
    void teamExit(Long teamId);

    /**
     * 修改战队接口
     */
    void updateTeam(TeamUpdateDto teamUpdateDto);

    /**
     * 战队变更搜索接口
     */
    TeamChangeSearchVo teamChangeSearch(String mobile);

    /**
     * 战队变更接口
     */
    void teamChangeCreator(Long changeUserId);

    /**
     * 战队加V申请接口
     */
    void teamCertificationApply(TeamCertificationApplyDto teamCertificationApplyDto);

    /**
     * 战队认证详情接口
     */
    TeamCertificationDetailVo teamCertificationDetail();
}
