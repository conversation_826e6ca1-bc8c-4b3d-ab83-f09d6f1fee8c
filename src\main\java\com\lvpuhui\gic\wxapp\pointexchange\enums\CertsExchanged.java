package com.lvpuhui.gic.wxapp.pointexchange.enums;

import lombok.Getter;

/**
 * 兑换凭证状态枚举
 * <AUTHOR>
 * @since 2022年05月05日 18:20:00
 */
@Getter
public enum CertsExchanged {
    UN_CHANGE(0,"未兑换"),
    CHANGE(1,"已兑换"),
    ;

    private Integer state;

    private String describe;

    CertsExchanged(Integer state, String describe) {
        this.state = state;
        this.describe = describe;
    }
}
