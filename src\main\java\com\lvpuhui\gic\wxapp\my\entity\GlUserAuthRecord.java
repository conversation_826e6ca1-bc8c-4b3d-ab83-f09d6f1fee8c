package com.lvpuhui.gic.wxapp.my.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 用户授权手机号记录表实体类
 * <AUTHOR>
 * @since 2023年3月2日17:16:12
 */
@Data
public class GlUserAuthRecord extends Model<GlUserAuthRecord> {

    /**
     * 换绑记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 创建时间
     */
    private Date created;
}