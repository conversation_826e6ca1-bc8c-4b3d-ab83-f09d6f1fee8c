package com.lvpuhui.gic.wxapp.carbonbook.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SceneDetailVo implements Serializable {
    private String title;
    private String campus;
    private String emission;
    private Double emissionNum;
    private String date;
    private Double point;


}
