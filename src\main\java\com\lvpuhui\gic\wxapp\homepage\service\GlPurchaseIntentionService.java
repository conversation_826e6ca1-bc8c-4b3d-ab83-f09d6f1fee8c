package com.lvpuhui.gic.wxapp.homepage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.dto.QueryPurchaseSubjectListVo;
import com.lvpuhui.gic.wxapp.homepage.entity.GlPurchaseIntention;
import com.lvpuhui.gic.wxapp.homepage.dto.PurchaseIntetionDto;

import java.util.List;

public interface GlPurchaseIntentionService extends IService<GlPurchaseIntention> {

    /**
     * 保存客户购买意愿
     */

    void savePurchase(PurchaseIntetionDto purchaseIntetionDto);

    /**
     * 查询主体类型
     * @return
     */
    List<QueryPurchaseSubjectListVo> queryPurchaseSubjectList();
}
