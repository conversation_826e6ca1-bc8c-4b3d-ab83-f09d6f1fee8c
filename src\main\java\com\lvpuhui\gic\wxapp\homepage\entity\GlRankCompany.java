package com.lvpuhui.gic.wxapp.homepage.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.util.Date;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 企业排行表(gl_rank_company)实体类
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
public class GlRankCompany extends Model<GlRankCompany> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date updated;
}
