package com.lvpuhui.gic.wxapp.sms.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import com.lvpuhui.gic.wxapp.sms.dto.SmsAuthLoginDto;
import com.lvpuhui.gic.wxapp.sms.dto.SmsAuthSendDto;
import com.lvpuhui.gic.wxapp.sms.dto.SmsAuthVo;
import com.lvpuhui.gic.wxapp.sms.service.SmsAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Tags(value = {@Tag(name = "短信授权接口1.0")})
@RestController
@RequestMapping("/sms")
public class SmsAuthController {

    @Resource
    private SmsAuthService smsAuthService;

    @Operation(summary = "发送短信接口")
    @PassToken
    @PostMapping("/send")
    public R<String> send(@RequestBody @Valid SmsAuthSendDto smsAuthSendDto){
        smsAuthService.send(smsAuthSendDto);
        return R.ok("发送成功");
    }

    @Operation(summary = "短信授权接口")
    @PassToken
    @PostMapping("/login")
    public R<SmsAuthVo> login(@RequestBody @Valid SmsAuthLoginDto smsAuthLoginDto){
        SmsAuthVo smsAuthVo  = smsAuthService.login(smsAuthLoginDto);
        return R.ok(smsAuthVo);
    }
}