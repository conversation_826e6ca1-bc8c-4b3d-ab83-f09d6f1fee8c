package com.lvpuhui.gic.wxapp.carbon.dao;

import com.lvpuhui.gic.wxapp.carbon.dto.CarbonEmissionSumGroupByProjectDto;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelEmissionDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 碳汇项目实例（用户申请）减排量明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
public interface GlCarbonModelEmissionDao extends BaseMapper<GlCarbonModelEmissionDO> {

    /**
     * 根据用户手机号和碳汇项目分组计算总减排量
     * @param mobileSha256 用户手机号
     * @return 分组计算总减排量
     */
    List<CarbonEmissionSumGroupByProjectDto> getEmissionSumGroupByProject(@Param("mobileSha256") String mobileSha256);
}
