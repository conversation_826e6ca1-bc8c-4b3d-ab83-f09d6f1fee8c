package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlCompanyProductDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlSceneCategoryDao;
import com.lvpuhui.gic.wxapp.homepage.dto.BigCategories;
import com.lvpuhui.gic.wxapp.homepage.dto.CompanyProducts;
import com.lvpuhui.gic.wxapp.homepage.dto.SceneCategories;
import com.lvpuhui.gic.wxapp.homepage.entity.GlCompanyProduct;
import com.lvpuhui.gic.wxapp.homepage.entity.GlSceneCategory;
import com.lvpuhui.gic.wxapp.homepage.service.GlCompanyProductService;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 企业产品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-05
 */
@Service
public class GlCompanyProductServiceImpl extends ServiceImpl<GlCompanyProductDao, GlCompanyProduct> implements GlCompanyProductService {

    @Resource
    private GlSceneCategoryDao glSceneCategoryDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Override
    public List<BigCategories> getCompanys() {
        // 查询场景类别数据
        LambdaQueryWrapper<GlSceneCategory> glSceneCategoryQuery = Wrappers.lambdaQuery();
        glSceneCategoryQuery.eq(GlSceneCategory::getDeleted, Deleted.UN_DELETE.getDeleted());
        glSceneCategoryQuery.orderByDesc(GlSceneCategory::getSequence);
        List<GlSceneCategory> glSceneCategories = glSceneCategoryDao.selectList(glSceneCategoryQuery);
        if(CollUtil.isEmpty(glSceneCategories)){
            return Lists.newArrayList();
        }
        // 获取场景类别ID集合
        List<Long> categoryIds = glSceneCategories.stream().map(GlSceneCategory::getId).collect(Collectors.toList());
        // 查询企业产品
        LambdaQueryWrapper<GlCompanyProduct> glCompanyProductQuery = Wrappers.lambdaQuery();
        glCompanyProductQuery.eq(GlCompanyProduct::getDeleted,Deleted.UN_DELETE.getDeleted());
        glCompanyProductQuery.in(GlCompanyProduct::getCategoryId,categoryIds);
        glCompanyProductQuery.orderByDesc(GlCompanyProduct::getSequence);
        List<GlCompanyProduct> glCompanyProducts = baseMapper.selectList(glCompanyProductQuery);
        // 对企业产品根据场景类别进行分组
        Map<Long, List<GlCompanyProduct>> groupByCategoryIdMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glCompanyProducts)){
            groupByCategoryIdMap = glCompanyProducts.stream()
                    .collect(Collectors.groupingBy(GlCompanyProduct::getCategoryId));
        }
        // 把场景类别根据大类分组
        Map<String, List<GlSceneCategory>> groupByBigCategoryIdMap = glSceneCategories.stream().collect(Collectors.groupingBy(glSceneCategory -> (glSceneCategory.getBigCategoryId() + "##" + glSceneCategory.getBigCategoryName())));
        // 创建返回值容器
        List<BigCategories> bigCategoriesList = Lists.newArrayListWithCapacity(2);
        // 循环遍历大类
        for(Map.Entry<String, List<GlSceneCategory>> bigCategoryIdMap : groupByBigCategoryIdMap.entrySet()){
            List<GlSceneCategory> glSceneCategoryList = bigCategoryIdMap.getValue();
            String[] bigCategoryIdAndNameArray = bigCategoryIdMap.getKey().split("##");
            BigCategories bigCategories = new BigCategories();
            bigCategories.setId(Long.parseLong(bigCategoryIdAndNameArray[0]));
            bigCategories.setName(bigCategoryIdAndNameArray[1]);
            bigCategoriesList.add(bigCategories);

            List<SceneCategories> sceneCategoriesList = Lists.newArrayListWithCapacity(glSceneCategoryList.size());
            // 循环遍历场景类别
            for (GlSceneCategory glSceneCategory : glSceneCategoryList) {
                SceneCategories sceneCategories = new SceneCategories();
                sceneCategories.setId(glSceneCategory.getId());
                sceneCategories.setName(glSceneCategory.getName());
                sceneCategoriesList.add(sceneCategories);
                if(groupByCategoryIdMap.containsKey(glSceneCategory.getId())){
                    // 获取到场景类别对应的企业产品
                    List<GlCompanyProduct> glCompanyProductList = groupByCategoryIdMap.get(glSceneCategory.getId());
                    // 进行类型转换
                    List<CompanyProducts> companyProductsList = glCompanyProductList.stream()
                            .map(glCompanyProduct -> {
                                CompanyProducts companyProducts = new CompanyProducts();
                                companyProducts.setId(glCompanyProduct.getId());
                                companyProducts.setName(glCompanyProduct.getName());
                                companyProducts.setLogoImage(glCompanyProduct.getLogoImage());
                                companyProducts.setJumpUrl(glCompanyProduct.getJumpUrl());
                                companyProducts.setAppletAppid(glCompanyProduct.getAppletAppid());
                                companyProducts.setAppletUrl(glCompanyProduct.getAppletUrl());
                                return companyProducts;
                            }).collect(Collectors.toList());
                    sceneCategories.setProducts(companyProductsList);
                }
            }
            bigCategories.setCategoryList(sceneCategoriesList);
        }
        return bigCategoriesList;
    }

    @Override
    public List<SceneCategories> companyList() {
        // 查询场景类别数据
        LambdaQueryWrapper<GlSceneCategory> glSceneCategoryQuery = Wrappers.lambdaQuery();
        glSceneCategoryQuery.eq(GlSceneCategory::getDeleted, Deleted.UN_DELETE.getDeleted());
        glSceneCategoryQuery.orderByDesc(GlSceneCategory::getSequence);
        List<GlSceneCategory> glSceneCategories = glSceneCategoryDao.selectList(glSceneCategoryQuery);
        if(CollUtil.isEmpty(glSceneCategories)){
            return Lists.newArrayList();
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        // 获取场景类别ID集合
        List<Long> categoryIds = glSceneCategories.stream().map(GlSceneCategory::getId).collect(Collectors.toList());
        // 查询企业产品
        LambdaQueryWrapper<GlCompanyProduct> glCompanyProductQuery = Wrappers.lambdaQuery();
        glCompanyProductQuery.eq(GlCompanyProduct::getDeleted,Deleted.UN_DELETE.getDeleted());
        glCompanyProductQuery.in(GlCompanyProduct::getCategoryId,categoryIds);
        glCompanyProductQuery.orderByDesc(GlCompanyProduct::getSequence);
        List<GlCompanyProduct> glCompanyProducts = baseMapper.selectList(glCompanyProductQuery);
        // 对企业产品根据场景类别进行分组
        Map<Long, List<GlCompanyProduct>> groupByCategoryIdMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glCompanyProducts)){
            groupByCategoryIdMap = glCompanyProducts.stream()
                    .collect(Collectors.groupingBy(GlCompanyProduct::getCategoryId));
        }
        List<SceneCategories> sceneCategoriesList = Lists.newArrayListWithCapacity(glSceneCategories.size());
        // 循环遍历场景类别
        for (GlSceneCategory glSceneCategory : glSceneCategories) {
            SceneCategories sceneCategories = new SceneCategories();
            sceneCategories.setId(glSceneCategory.getId());
            sceneCategories.setName(glSceneCategory.getName());
            sceneCategories.setLogoImage(imagePrefix + glSceneCategory.getLogoImage());
            sceneCategoriesList.add(sceneCategories);
            if(groupByCategoryIdMap.containsKey(glSceneCategory.getId())){
                // 获取到场景类别对应的企业产品
                List<GlCompanyProduct> glCompanyProductList = groupByCategoryIdMap.get(glSceneCategory.getId());
                // 进行类型转换
                List<CompanyProducts> companyProductsList = glCompanyProductList.stream()
                        .map(glCompanyProduct -> {
                            CompanyProducts companyProducts = new CompanyProducts();
                            companyProducts.setId(glCompanyProduct.getId());
                            companyProducts.setName(glCompanyProduct.getName());
                            companyProducts.setLogoImage(imagePrefix + glCompanyProduct.getLogoImage());
                            companyProducts.setJumpUrl(glCompanyProduct.getJumpUrl());
                            companyProducts.setAppletAppid(glCompanyProduct.getAppletAppid());
                            companyProducts.setAppletUrl(glCompanyProduct.getAppletUrl());
                            companyProducts.setProductDescribe(glCompanyProduct.getProductDescribe());
                            return companyProducts;
                        }).collect(Collectors.toList());
                sceneCategories.setProducts(companyProductsList);
            }
        }
        return sceneCategoriesList;
    }
}
