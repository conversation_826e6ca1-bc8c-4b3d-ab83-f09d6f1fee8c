package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 累积排行榜po
 * <AUTHOR>
 * @since 2022年07月25日 18:10:00
 */
@Data
public class RankAccumulateList {

    /**
     * 是否已过截止日期 true:已过  false:未过
     */
    private Boolean isEnd;

    /**
     * 领取商品名称
     */
    private String goodsName;

    /**
     * 排行前多少名可以领取
     */
    private Integer top;

    /**
     * 截止日期
     */
    private String endTime;

    /**
     * 当前用户信息
     */
    private AccumulateRankUser currentUser;

    /**
     * 累积上榜用户信息集合
     */
    private List<AccumulateRankUser> accumulateRankUsers;

    @Data
    public static class AccumulateRankUser{

        /**
         * 排名
         */
        private Long rank;

        /**
         * 在整个城市中的排行榜
         */
        private Long cityRank;

        /**
         * 打败了多少人的占比
         */
        private Double radio;

        /**
         * 减排量
         */
        private Double emission;

        /**
         * 昵称
         */
        private String nickName;

        /**
         * 头像
         */
        private String avatarUrl;

        /**
         * 用户id(sha256)
         */
        private String mobileSha256;

        public Long getRank() {
            if(Objects.nonNull(cityRank)){
                return cityRank;
            }
            return rank;
        }
    }
}
