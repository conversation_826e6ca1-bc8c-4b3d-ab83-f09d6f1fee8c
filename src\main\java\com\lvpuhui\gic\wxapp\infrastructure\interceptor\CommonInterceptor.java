package com.lvpuhui.gic.wxapp.infrastructure.interceptor;


import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.api.R;
import com.google.common.collect.Sets;

import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * push cookies to model as cookieMap
 *
 * <AUTHOR> 2020-12-12 18:09:04
 */
@Aspect
@Component
@Slf4j
public class CommonInterceptor {

    private ThreadLocal<Long> startTime = new ThreadLocal<>();
    private String logPrefix = "==> Request";
    private String resultPrefix = "==> Response";
    private String TRACE_ROUTE_HEADER = Global.TRACE_ROUTE_HEADER;

    @Pointcut("execution(public * com.lvpuhui.gic.wxapp.*.controller..*Controller.*(..))")
    public void requestLog() { }


    /**
     * rest请求前后切面 环绕通知
     *
     * @param pjp
     * @return
     * @throws Throwable
     */
    @Around("requestLog()")
    public Object restInterInterceptor(ProceedingJoinPoint pjp) throws Throwable {
        if(StrUtil.isBlank(MDC.get(TRACE_ROUTE_HEADER))){
            MDC.put(TRACE_ROUTE_HEADER, IdUtil.fastSimpleUUID());
        }
        Object result;
        String methodName = pjp.getSignature().getName();
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder
                .getRequestAttributes())).getRequest();
        String frontToken = request.getHeader("token");
        try {
            Set<Object> allParams = getAllParams(pjp);
            log.info("{} {} | method:{} | token:{} | params:{}", logPrefix, request.getRequestURI(), methodName,
                    frontToken, JSONUtil.toJsonStr(allParams));
            startTime.set(System.currentTimeMillis());
            result = proceed(pjp);
            /*返回通知方法*/
            log.info("{} | 耗时:{} | result:{}", resultPrefix, (System.currentTimeMillis() - startTime.get()), JSON.toJSONString(result));
        } catch (Throwable e) {
            /*异常通知方法*/
            log.error("{}| 异常 :{}", request.getRequestURI(), e.getMessage());
            throw e;
        } finally {
            try {
                MDC.clear();
            } catch (Exception e) {
                log.error("清空当前线程的MDC数据失败{}", e.getMessage());
            }
            startTime.remove();
        }
        return result;
    }

    /**
     * 自动校验
     * @param pjp
     * @return
     */
    private Object proceed(ProceedingJoinPoint pjp) throws Throwable {
        Object[] args = pjp.getArgs();
        BeanPropertyBindingResult bindingResult = null;
        for (Object arg : args) {
            if (arg instanceof BeanPropertyBindingResult) {
                bindingResult = (BeanPropertyBindingResult) arg;
            }
        }
        if (Objects.nonNull(bindingResult) && bindingResult.hasErrors()) {
            return  R.failed(bindingResult.getFieldErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .distinct()
                    .collect((Collectors.joining(","))));
        }
        return pjp.proceed();
    }

    /**
     * 获取请求参数
     * @param pjp
     * @return
     */
    private Set<Object> getAllParams(ProceedingJoinPoint pjp){
        Object[] args = pjp.getArgs();
        Set<Object> allParams = Sets.newLinkedHashSet();
        for (Object arg : args) {
            if (arg instanceof HttpServletRequest) {
                HttpServletRequest request = (HttpServletRequest) arg;
                Map<String, String[]> paramMap = request.getParameterMap();
                if (MapUtil.isNotEmpty(paramMap)) {
                    allParams.add(paramMap);
                }
            }else if (arg instanceof BeanPropertyBindingResult) {
               continue;
            }else {
                allParams.add(arg);
            }
        }
        return allParams;
    }
}

