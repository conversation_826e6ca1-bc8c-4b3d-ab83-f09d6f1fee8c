package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.base.constant.AppletConfigConstant;
import com.lvpuhui.gic.wxapp.base.dao.GlAppletConfigDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlCompanyDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlSubjectRecordDao;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.homepage.entity.GlCompany;
import com.lvpuhui.gic.wxapp.homepage.entity.GlSubjectRecord;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectFinishDto;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectNumberDto;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectConfig;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectFinish;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectNumber;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.homepage.service.GlSubjectRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 答题记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-21
 */
@Service
public class GlSubjectRecordServiceImpl extends ServiceImpl<GlSubjectRecordDao, GlSubjectRecord> implements GlSubjectRecordService {

    @Resource
    private GlAppletConfigDao glAppletConfigDao;

    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlPointsService glPointsService;

    @Resource
    private GlCompanyDao glCompanyDao;

    @Override
    public SubjectNumber checkSubject(SubjectNumberDto subjectNumberDto) {
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,subjectNumberDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        LambdaQueryWrapper<GlAppletConfig> glAppletConfigQuery = Wrappers.lambdaQuery();
        glAppletConfigQuery.eq(GlAppletConfig::getParamKey, AppletConfigConstant.SUBJECT_ONE_DAY_NUMBER);
        GlAppletConfig glAppletConfig = glAppletConfigDao.selectOne(glAppletConfigQuery);
        if(Objects.isNull(glAppletConfig)){
            throw new GicWxAppException("答题未配置获取的积分,请联系管理人员,感谢您的反馈");
        }
        String currentDate = DatePattern.PURE_DATE_FORMAT.format(DateUtil.date());
        LambdaQueryWrapper<GlSubjectRecord> glSubjectRecordQuery = Wrappers.lambdaQuery();
        glSubjectRecordQuery.eq(GlSubjectRecord::getUserId,glUser.getId());
        glSubjectRecordQuery.eq(GlSubjectRecord::getSubjectTime, currentDate);
        Integer count = baseMapper.selectCount(glSubjectRecordQuery);

        Integer remainNum = Integer.parseInt(glAppletConfig.getParamValue()) - count;
        SubjectNumber subjectNumber = new SubjectNumber();
        subjectNumber.setRemainNum(remainNum);
        return subjectNumber;
    }

    @Override
    public SubjectConfig subjectConfig() {
        List<String> paramKeys = Lists.newArrayListWithCapacity(3);
        paramKeys.add(AppletConfigConstant.SUBJECT_ONE_DAY_NUMBER);
        paramKeys.add(AppletConfigConstant.SUBJECT_NUMBER);
        paramKeys.add(AppletConfigConstant.SUBJECT_PASS_POINT);

        LambdaQueryWrapper<GlAppletConfig> glAppletConfigQuery = Wrappers.lambdaQuery();
        glAppletConfigQuery.in(GlAppletConfig::getParamKey, paramKeys);
        List<GlAppletConfig> glAppletConfigs = glAppletConfigDao.selectList(glAppletConfigQuery);
        if(CollUtil.isEmpty(glAppletConfigs)){
            throw new GicWxAppException("答题未配置获取的积分,请联系管理人员,感谢您的反馈");
        }
        Map<String, String> appletConfigMap = glAppletConfigs.stream().collect(Collectors.toMap(GlAppletConfig::getParamKey, GlAppletConfig::getParamValue, (k1, k2) -> k1));

        SubjectConfig subjectConfig = new SubjectConfig();
        subjectConfig.setSubjectNumber(Integer.parseInt(appletConfigMap.get(AppletConfigConstant.SUBJECT_NUMBER)));
        subjectConfig.setSubjectPassPoint(Integer.parseInt(appletConfigMap.get(AppletConfigConstant.SUBJECT_PASS_POINT)));
        subjectConfig.setSubjectOneDayNumber(Integer.parseInt(appletConfigMap.get(AppletConfigConstant.SUBJECT_ONE_DAY_NUMBER)));
        return subjectConfig;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubjectFinish subjectFinish(SubjectFinishDto subjectFinishDto) {
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,subjectFinishDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }

        List<String> paramKeys = Lists.newArrayListWithCapacity(3);
        paramKeys.add(AppletConfigConstant.SUBJECT_ONE_DAY_NUMBER);
        paramKeys.add(AppletConfigConstant.SUBJECT_NUMBER);
        paramKeys.add(AppletConfigConstant.SUBJECT_PASS_POINT);

        LambdaQueryWrapper<GlAppletConfig> glAppletConfigQuery = Wrappers.lambdaQuery();
        glAppletConfigQuery.in(GlAppletConfig::getParamKey, paramKeys);
        List<GlAppletConfig> glAppletConfigs = glAppletConfigDao.selectList(glAppletConfigQuery);
        if(CollUtil.isEmpty(glAppletConfigs)){
            throw new GicWxAppException("答题未配置获取的积分,请联系管理人员,感谢您的反馈");
        }
        Map<String, String> appletConfigMap = glAppletConfigs.stream().collect(Collectors.toMap(GlAppletConfig::getParamKey, GlAppletConfig::getParamValue, (k1, k2) -> k1));
        String subjectOneDayNumber = appletConfigMap.get(AppletConfigConstant.SUBJECT_ONE_DAY_NUMBER);

        Integer subjectNumber = Integer.parseInt(appletConfigMap.get(AppletConfigConstant.SUBJECT_NUMBER));
        if(subjectFinishDto.getPassNum() > subjectNumber){
            throw new GicWxAppException("非法提交");
        }

        String currentDate = DatePattern.PURE_DATE_FORMAT.format(DateUtil.date());
        LambdaQueryWrapper<GlSubjectRecord> glSubjectRecordQuery = Wrappers.lambdaQuery();
        glSubjectRecordQuery.eq(GlSubjectRecord::getUserId,glUser.getId());
        glSubjectRecordQuery.eq(GlSubjectRecord::getSubjectTime, currentDate);
        Integer count = baseMapper.selectCount(glSubjectRecordQuery);

        Integer remainNum = Integer.parseInt(subjectOneDayNumber) - count;
        if(remainNum <= 0){
            throw new GicWxAppException("您今日的答题次数已使用完毕,感谢您的参与");
        }
        Integer subjectPassPoint = Integer.parseInt(appletConfigMap.get(AppletConfigConstant.SUBJECT_PASS_POINT));
        Integer pointSum = subjectPassPoint * subjectFinishDto.getPassNum();

        GlSubjectRecord glSubjectRecord = new GlSubjectRecord();
        glSubjectRecord.setUserId(glUser.getId());
        glSubjectRecord.setPoint(pointSum);
        glSubjectRecord.setSubjectTime(currentDate);
        glSubjectRecord.setCreated(new Date());
        baseMapper.insert(glSubjectRecord);

        GlCompany glCompany = glCompanyDao.selectById(121);
        glPointsService.grantSubject(glSubjectRecord,glCompany.getId(),glCompany.getName(),glUser.getMobileSha256());

        remainNum = remainNum - 1;
        SubjectFinish subjectFinish = new SubjectFinish();
        subjectFinish.setPoint(pointSum);
        subjectFinish.setRemainNum(remainNum);
        return subjectFinish;
    }
}
