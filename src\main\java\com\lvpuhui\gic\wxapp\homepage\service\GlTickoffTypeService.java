package com.lvpuhui.gic.wxapp.homepage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.entity.GlTickoffType;
import com.lvpuhui.gic.wxapp.homepage.dto.TicksDto;
import com.lvpuhui.gic.wxapp.homepage.dto.Ticks;
import com.lvpuhui.gic.wxapp.homepage.dto.TicksDetail;

import java.util.List;

/**
 * 打卡类别表(GlTickoffType)表服务接口
 * <AUTHOR>
 * @since 2022年5月5日 19:23:38
 */
public interface GlTickoffTypeService extends IService<GlTickoffType> {

    /**
     * 打卡列表接口
     */
    List<Ticks> ticks(TicksDto ticksDto);

    /**
     * 打卡详情信息接口
     * @param tickId 打卡类型ID
     */
    TicksDetail ticksDetail(Long tickId);
}
