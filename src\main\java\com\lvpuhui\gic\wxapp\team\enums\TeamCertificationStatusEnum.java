package com.lvpuhui.gic.wxapp.team.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 战队加V审核状态枚举
 * <AUTHOR>
 * @since 2023年05月25日 10:55:00
 */
@Getter
public enum TeamCertificationStatusEnum {

    REVIEW_IN(0,"审核中"),
    REVIEW_FAIL(-1,"审核不通过"),
    REVIEW_PASS(1,"已认证"),
    ;

    private Integer status;

    private String describe;

    TeamCertificationStatusEnum(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public static String getDescribe(Integer status){
        return Arrays.stream(TeamCertificationStatusEnum.values())
                .filter(s -> s.getStatus().equals(status))
                .map(TeamCertificationStatusEnum::getDescribe)
                .findFirst().orElse("未知状态");
    }
}
