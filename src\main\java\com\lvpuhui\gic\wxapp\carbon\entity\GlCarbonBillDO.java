package com.lvpuhui.gic.wxapp.carbon.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 提现账单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_bill")
public class GlCarbonBillDO extends Model<GlCarbonBillDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出售减排量(克)
     */
    private BigDecimal emission;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 账单金额
     */
    private BigDecimal billAmount;

    /**
     * 账单年度周期
     */
    private String billDate;

    /**
     * 转入可提现日期
     */
    private LocalDateTime transferWithdrawDate;

    /**
     * 领取开始时间
     */
    private LocalDateTime receiveStartTime;

    /**
     * 领取结束时间
     */
    private LocalDateTime receiveEndTime;

    /**
     * 领取所需资料
     */
    private String receiveMaterial;

    /**
     * 领取地点
     */
    private String receiveAddress;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改者ID
     */
    private Long updator;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
