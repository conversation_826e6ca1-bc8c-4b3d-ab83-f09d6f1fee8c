package com.lvpuhui.gic.wxapp.carbon.dao;

import com.lvpuhui.gic.wxapp.carbon.dto.OperationVo;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelOperationDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 碳汇项目实例（用户申请）操作日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
public interface GlCarbonModelOperationDao extends BaseMapper<GlCarbonModelOperationDO> {

    List<OperationVo> queryAllLogByModelId(@Param("modelId") Long modelId);
}
