package com.lvpuhui.gic.wxapp.carbon.dao;

import com.lvpuhui.gic.wxapp.carbon.dto.CarbonManagrModel;
import com.lvpuhui.gic.wxapp.carbon.dto.CarbonModelAmount;
import com.lvpuhui.gic.wxapp.carbon.dto.IncomeTakingVo;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelDO;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelIncomeDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 碳汇项目实例（用户申请）收入 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
public interface GlCarbonModelIncomeDao extends BaseMapper<GlCarbonModelIncomeDO> {

    /**
     * 查询提现金额
     * @param userId 用户ID
     */
    IncomeTakingVo selectBillAmount(@Param("userId") Long userId);

    int updateTakingAmount(@Param("userId") Long userId);

    /**
     * 计算当前用户所有Model的明细信息
     * @param userId
     * @return
     */
    BigDecimal queryAllModel(@Param("userId")Long userId);


    CarbonManagrModel sumData(@Param("userId")Long userId);

    GlCarbonModelIncomeDO selectByModelId(@Param("modelId")Long modelId);

    CarbonModelAmount sumAllAmount(@Param("userId")Long userId);

    BigDecimal queryAllRemainByUserId(@Param("userId")Long userId);
}
