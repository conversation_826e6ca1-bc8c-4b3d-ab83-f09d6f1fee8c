package com.lvpuhui.gic.wxapp.carbon.service;

import com.lvpuhui.gic.wxapp.carbon.dto.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CarbonManagerService {
    /**
     * 碳汇管理界面
     * @return
     */
    CarbonManagerVo carbonManager();

    /**
     * 预计收入明细
     * @return
     */
    CarbonProjectIncomeVo carbonProjectIncome();

    /**
     * 碳汇明细
     * @param modelId
     * @return
     */
    CarbonDetailsVo carbonDetails(Long modelId);

    /**
     * 日收益明细
     * @param modelId
     * @return
     */
    IncomeDayVo inComeDayDetails(Long modelId);

    /**
     * 所有碳汇每日实例
     * @return
     */
    BigDecimal getCarbonEveryReward();

    /**
     * model每日收益
     * @return
     */
    BigDecimal getCarbonEveryModelReward(Long modelId);

    /**
     * 开发碳汇
     * @param carbonSaveDto
     */
    void saveCarbon(CarbonSaveDto carbonSaveDto);

    /**
     * 萱蕚碳汇类型
     * @return
     */
    List<CarbonTypeListVo> getProjectList();

    /**
     * 所有日收益明细
     * @return
     */
    IncomeDayVo allInComeDayDetails();

    List<CarbonSubsidyListVo> getAllSubsidyList();
}
