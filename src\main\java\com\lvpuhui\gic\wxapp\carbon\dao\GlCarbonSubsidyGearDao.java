package com.lvpuhui.gic.wxapp.carbon.dao;

import com.lvpuhui.gic.wxapp.carbon.dto.CarbonSubsidyListVo;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonSubsidyGearDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 补贴档位 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
public interface GlCarbonSubsidyGearDao extends BaseMapper<GlCarbonSubsidyGearDO> {

    List<GlCarbonSubsidyGearDO> selectByYear(@Param("year") int year);

    List<CarbonSubsidyListVo> selectAll(@Param("year") int year);
}
