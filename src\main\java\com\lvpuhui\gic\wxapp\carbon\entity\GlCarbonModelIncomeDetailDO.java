package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 碳汇项目实例（用户申请）收入明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_model_income_detail")
public class GlCarbonModelIncomeDetailDO extends Model<GlCarbonModelIncomeDetailDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * modelID
     */
    private Long modelId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 收益名称
     */
    private String name;

    /**
     * 收益金额
     */
    private BigDecimal amount;

    /**
     * 当前剩余金额
     */
    private BigDecimal remainAmount;

    /**
     * 收益面积
     */
    private BigDecimal area;

    /**
     * 收入类型：0，补贴，1：奖励惩罚
     */
    private Integer type;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 提现状态  0：未提现  1：已提现
     */
    private Integer takingStatus;

    /**
     * 收入备注
     */
    private String remark;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 收益日期
     */
    private LocalDate incomeDate;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    private Long updator;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
