package com.lvpuhui.gic.wxapp.homepage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.entity.GlVideo;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoFinishDto;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoListDto;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoFinish;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoList;

import java.util.List;

/**
 * 视频表 服务类
 * <AUTHOR>
 * @since 2022-06-01
 */
public interface GlVideoService extends IService<GlVideo> {

    /**
     * 视频列表接口
     */
    List<VideoList> videoList(VideoListDto videoListDto);

    /**
     * 视频完成接口
     */
    VideoFinish videoFinish(VideoFinishDto videoFinishDto);

    VideoFinish videoFinish2(VideoFinishDto videoFinishDto);

    /**
     * 首页-视频列表接口
     */
    List<VideoList> indexVideoList();
}
