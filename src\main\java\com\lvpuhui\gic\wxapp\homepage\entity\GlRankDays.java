package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 用户上榜表(gl_rank_days)实体类
 * <AUTHOR>
 * @since 2022-07-25
 */
@Data
public class GlRankDays extends Model<GlRankDays> {

    /**
     * 主键/用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户手机号sha256
     */
    private String mobileSha256;

    /**
     * 上榜天数
     */
    private Integer days;

    /**
     * 上榜天数标识-基于创建时间开始计算0是未上榜1是上榜
     */
    private String daysMark;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;
}
