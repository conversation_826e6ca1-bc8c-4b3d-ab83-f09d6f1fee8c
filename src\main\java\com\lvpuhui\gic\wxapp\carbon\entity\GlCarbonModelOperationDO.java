package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 碳汇项目实例（用户申请）操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_model_operation")
public class GlCarbonModelOperationDO extends Model<GlCarbonModelOperationDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * modelID
     */
    private Long modelId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 后台日志
     */
    private String log;

    /**
     * 小程序日志
     */

    private String appLog;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 奖惩金额
     */
    private BigDecimal amount;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    private Long updator;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
