package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 购买意愿表(gl_purchase)
 * <AUTHOR>
 */
@Data

public class GlPurchaseIntention extends Model<GlPurchaseIntention> {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     *真实姓名
     */
    private String realName;

    /**
     * 主体类型id
     */
    private Long subjectId;

    /**
     *手机号
     */
    private String mobileSha256;

    /**
     * 用户公司名称
     */
    private String userCompany;

    /**
     * 行业
     */
    private String industry;

    /**
     * 地区所在地
     */
    private String zone;

    /**
     * 意向购买企业(存入企业id)
     */
    private String companyId;

    /**
     * 意向购买量
     */
    private String amount;

    /**
     * 购买用途
     */
    private String purpose;

    /**
     * 类型(0-待联系，1-无效，2-商机)
     */
    private Integer operation;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date updated;

    /**
     * 是否删除
     */

    private Integer isDelete;

}
