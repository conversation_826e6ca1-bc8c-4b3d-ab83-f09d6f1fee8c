package com.lvpuhui.gic.wxapp.other.service.impl;


import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;

import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorService;
import com.lvpuhui.gic.wxapp.other.entity.GlConsumption;
import com.lvpuhui.gic.wxapp.homepage.entity.GlPoints;
import com.lvpuhui.gic.wxapp.other.dto.DataHandleDto;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.other.service.DataHandleService;
import com.lvpuhui.gic.wxapp.other.service.GlConsumptionService;
import com.lvpuhui.gic.wxapp.infrastructure.sharding.DynamicTableNameHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicLong;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataHandleServiceImpl implements DataHandleService {

    private float blockingCoefficient = 0f;
    final String [] array = new String[]{"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"};

    @Resource
    GlBehaviorService glBehaviorService;
    @Resource
    GlConsumptionService glConsumptionService;
    @Resource
    GlPointsService glPointsService;

    @Resource
    GlAppletConfigService glAppletConfigService;

    @Value("${webconsole_api:}")
    private String webConsoleApi;

    @Override
    public long handleBehavior(DataHandleDto dto) {
        ExecutorService executorService = ThreadUtil.newExecutorByBlockingCoefficient(blockingCoefficient);
        AtomicLong remain = new AtomicLong(1);

        return 0L;
    }

    @Override
    public long handlePoints(DataHandleDto dto) {
        long sumTotal = 0L;
        ExecutorService executorService = ThreadUtil.newExecutorByBlockingCoefficient(blockingCoefficient);
        AtomicLong remain = new AtomicLong(1);
        double rate = glAppletConfigService.getBehaviorToPoints();
        try {
            for(int i=0;i<array.length;i++) {
                for (int j = 0; j < array.length; j++) {
                    String key = String.format("%s%s", array[i], array[j]);
                    try {
                        DynamicTableNameHolder.set(String.format("gl_behavior_%s",key));
                        List<GlBehavior> behaviors = glBehaviorService.list(buildGlBehavior(dto,true));
                        int size = behaviors.size();
                        sumTotal += size;
                        log.info("handlePointsV2 key:{} size: {} execute {}", key, size, remain.get());
                        CountDownLatch latch = new CountDownLatch(size);
                        behaviors.stream().forEach(b-> executorService.execute(()->{
                            try {
                                glPointsService.grantBehaviorPoints(b,rate);
                            } finally {
                                latch.countDown();
                            }
                        }));
                        try {
                            latch.await();
                        } catch (InterruptedException e) {
                            log.error(e.getMessage(),e);
                        }
                    } finally {
                        remain.incrementAndGet();
                        DynamicTableNameHolder.remove();
                    }
                }
            }
        } finally {
            glPointsService.grantUserPoints(null);
            executorService.shutdown();
        }
        return sumTotal;
    }


    @Override
    public long countBehavior(String created) {
        if(StrUtil.isNotBlank(created)){
            String [] array = created.split(",");
            return glBehaviorService.count(new LambdaQueryWrapper<GlBehavior>()
                    .between(GlBehavior::getDate,array[0],array[1]));
        }
        return glBehaviorService.count(new LambdaQueryWrapper<>());
    }

    @Override
    public long countConsumption(String created) {
        if(StrUtil.isNotBlank(created)){
            String [] array = created.split(",");
            return glConsumptionService.count(new LambdaQueryWrapper<GlConsumption>()
                    .between(GlConsumption::getCreated,array[0],array[1]));
        }
        return glConsumptionService.count(new LambdaQueryWrapper<>());
    }

    @Override
    public long countPoints(String created) {
        if(StrUtil.isNotBlank(created)){
            String [] array = created.split(",");
            return glPointsService.count(new LambdaQueryWrapper<GlPoints>()
                    .between(GlPoints::getCreated,array[0],array[1]));
        }
        return glPointsService.count(new LambdaQueryWrapper<>());
    }

    @Override
    public void callStatisticsMethod() {
        try {
            String jobHandlerUrl = webConsoleApi + "/statistics/handExecuteStatistics";
            HttpUtil.get(jobHandlerUrl);
        }catch (Exception e){
            log.error("大数据数据处理完毕后调用统计异常:",e);
        }
    }
    LambdaQueryWrapper<GlBehavior> buildGlBehavior(DataHandleDto dto,boolean orderByAsc){
        LambdaQueryWrapper<GlBehavior> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(StrUtil.isNotBlank(dto.getMobileSha256())){
            lambdaQueryWrapper.eq(GlBehavior::getMobileSha256,dto.getMobileSha256());
        }
        if(dto.getTenantId() != null){
            lambdaQueryWrapper.eq(GlBehavior::getTenantId,dto.getTenantId());
        }
        if(StrUtil.isNotBlank(dto.getStart()) && StrUtil.isNotBlank(dto.getEnd())){
            lambdaQueryWrapper.between(GlBehavior::getDate,dto.getStart(),dto.getEnd());
        }
        if (orderByAsc){
            lambdaQueryWrapper.orderByAsc(GlBehavior::getDate, GlBehavior::getEventId);
        }
        return lambdaQueryWrapper;
    }
}
