package com.lvpuhui.gic.wxapp.carbon.enums;

import lombok.Getter;

/**
 * 举报状态枚举
 * <AUTHOR>
 * @since 2023年03月22日 17:26:00
 */
@Getter
public enum ReportStatus {

    UNTREATED(0,"未处理"),
    VERIFIED(1,"已核实"),
    UN_ADOPTED(2,"未采纳"),
    ;

    private Integer code;

    private String describe;

    ReportStatus(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
