package com.lvpuhui.gic.wxapp.other.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 上传绿色消费文件表(GlFile)表实体类
 *
 * <AUTHOR>
 * @since 2022-06-21 11:31:02
 */
@Data
@Accessors(chain = true)
public class GlFile extends Model<GlFile> {
    /**
    * 文件ID
    */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /** 企业名称 */ 
    private String companyName;
    /** 企业ID */ 
    private Long companyId;
    /** 文件位置 */ 
    private String filePath;
    /** 原始文件名 */ 
    private String fileName;
    /** 后缀 */ 
    private String fileExtension;
    /** 字节数 */ 
    private Integer fileSize;
    /** 文件哈希(MD5) */ 
    private String fileHash;
    /** 更新时间 */ 
    private Date updated;
    /** 创建时间 */ 
    private Date created;
    /** 消费积分状态 0未发放 1已发放 */ 
    private Integer pointsState;
    /** 减排量状态 0未发放 1已发放 */ 
    private Integer emissionState;
    /** 减排量积分状态 0未发放 1已发放 */ 
    private Integer emissionPointsState;
    /** 创建者ID */ 
    private Long creator;
    /** 消费条数 */ 
    private Integer consumptionCount;
    /** 删除 0未删除 1已删除*/
    private Integer deleted;
}


