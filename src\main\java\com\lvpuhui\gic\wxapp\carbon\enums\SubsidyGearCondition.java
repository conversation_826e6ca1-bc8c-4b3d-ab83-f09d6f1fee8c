package com.lvpuhui.gic.wxapp.carbon.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 补贴档位判断条件枚举
 * <AUTHOR>
 * @since 2023年03月22日 17:26:00
 */
@Getter
public enum SubsidyGearCondition {

    AREA(0,"按面积"),
    EMISSION(1,"按减排量"),
    ;

    private Integer code;

    private String describe;

    SubsidyGearCondition(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static String getDescribe(Integer status){
        return Arrays.stream(SubsidyGearCondition.values())
                .filter(subsidyGearCondition -> subsidyGearCondition.getCode().equals(status))
                .findFirst()
                .get().getDescribe();
    }
}
