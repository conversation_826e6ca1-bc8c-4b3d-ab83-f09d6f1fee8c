package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.base.constant.AppletConfigConstant;
import com.lvpuhui.gic.wxapp.base.dao.GlAppletConfigDao;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlVideoDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlVideoRecordDao;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoFinish;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoFinishDto;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoList;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoListDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlVideo;
import com.lvpuhui.gic.wxapp.homepage.entity.GlVideoRecord;
import com.lvpuhui.gic.wxapp.homepage.enums.VideoState;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.homepage.service.GlVideoService;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Available;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 视频表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@Slf4j
@Service
public class GlVideoServiceImpl extends ServiceImpl<GlVideoDao, GlVideo> implements GlVideoService {

    @Autowired
    private GlUserDao glUserDao;

    @Autowired
    private GlVideoRecordDao glVideoRecordDao;

    @Autowired
    private GlPointsService glPointsService;

    @Resource
    private GlAppletConfigDao glAppletConfigDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Override
    public List<VideoList> videoList(VideoListDto videoListDto) {
        videoListDto.setMobileSha256(UserUtils.getMobileSha256());
        LambdaQueryWrapper<GlVideo> glVideoLambdaQueryWrapper = Wrappers.lambdaQuery();
        glVideoLambdaQueryWrapper.eq(GlVideo::getDeleted, Deleted.UN_DELETE.getDeleted());
        glVideoLambdaQueryWrapper.eq(GlVideo::getState, VideoState.PUBLISH.getState());
        if(Objects.nonNull(videoListDto.getTopicId())){
            glVideoLambdaQueryWrapper.eq(GlVideo::getTopicId, videoListDto.getTopicId());
        }
        glVideoLambdaQueryWrapper.orderByDesc(GlVideo::getSequence);
        List<GlVideo> glVideos = baseMapper.selectList(glVideoLambdaQueryWrapper);
        if(CollUtil.isEmpty(glVideos)){
            return Lists.newArrayList();
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        List<VideoList> videoLists = Lists.newArrayListWithCapacity(glVideos.size());
        for (GlVideo glVideo : glVideos) {
            VideoList videoList = new VideoList();
            videoList.setId(glVideo.getId());
            videoList.setVideoId(glVideo.getVideoId());
            videoList.setCoverImage(imagePrefix + glVideo.getCoverImage());
            videoList.setVideoChannelsId(glVideo.getVideoChannelsId());
            videoList.setName(glVideo.getName());
            videoList.setPoint(glVideo.getPoint());
            videoList.setFinishDuration(glVideo.getFinishDuration());
            videoList.setAvailable(Available.NO.getState());
            videoLists.add(videoList);
        }
        return videoLists;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VideoFinish videoFinish(VideoFinishDto videoFinishDto) {
        videoFinishDto.setMobileSha256(UserUtils.getMobileSha256());
        VideoFinish videoFinish = new VideoFinish();
        // 查询用户
        GlVideo glVideo = baseMapper.selectById(videoFinishDto.getVideoId());
        if(Objects.isNull(glVideo)
                || Deleted.DELETED.getDeleted().equals(glVideo.getDeleted())
                || !VideoState.PUBLISH.getState().equals(glVideo.getState())){
            throw new GicWxAppException("您看的视频已取消或不存在");
        }
        // 看视频得到的积分
        Integer videoPoint = glVideo.getPoint();
        LambdaQueryWrapper<GlAppletConfig> glAppletConfigQuery = Wrappers.lambdaQuery();
        glAppletConfigQuery.eq(GlAppletConfig::getParamKey, AppletConfigConstant.VIDEO_POINT_EVERY_DAY_LIMIT);
        GlAppletConfig glAppletConfig = glAppletConfigDao.selectOne(glAppletConfigQuery);
        if(Objects.isNull(glAppletConfig)){
            throw new GicWxAppException("看视频未配置获取的积分,请联系管理人员,感谢您的反馈");
        }
        Integer watchVideoPointSum = Integer.parseInt(glAppletConfig.getParamValue());
        LambdaQueryWrapper<GlVideoRecord> glVideoRecordLambdaQueryWrapper = Wrappers.lambdaQuery();
        glVideoRecordLambdaQueryWrapper.select(GlVideoRecord::getPoint);
        glVideoRecordLambdaQueryWrapper.eq(GlVideoRecord::getUserId,UserUtils.getUserId());
        glVideoRecordLambdaQueryWrapper.eq(GlVideoRecord::getWatchTime, DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
        List<Object> objects = glVideoRecordDao.selectObjs(glVideoRecordLambdaQueryWrapper);
        if(CollUtil.isNotEmpty(objects)){
            Integer points = objects.stream().map(o -> (int) o).mapToInt(value -> value).sum();
            if(watchVideoPointSum > points){
                int remainPoint = watchVideoPointSum - points;
                Integer point = remainPoint > videoPoint?videoPoint:remainPoint;

                GlVideoRecord glVideoRecord = new GlVideoRecord();
                glVideoRecord.setVideoId(videoFinishDto.getVideoId());
                glVideoRecord.setUserId(UserUtils.getUserId());
                glVideoRecord.setPoint(point);
                glVideoRecord.setWatchTime(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
                glVideoRecordDao.insert(glVideoRecord);

                baseMapper.updateWatchCount(glVideo.getId());

                glPointsService.grantVideo(glVideoRecord,glVideo.getCompanyId(),glVideo.getCompanyName(),UserUtils.getMobileSha256(),glVideo.getId());
                videoFinish.setPoint(point);
            }
            if(watchVideoPointSum <= points){
                log.info("当前用户{}观看视频{}今天获取的积分已上限,上线积分:{}",UserUtils.getUserId(),glVideo.getId(),watchVideoPointSum);
                throw new GicWxAppException("您今天观看视频获得的积分已达到上限");
            }
            return videoFinish;
        }

        GlVideoRecord glVideoRecord = new GlVideoRecord();
        glVideoRecord.setVideoId(videoFinishDto.getVideoId());
        glVideoRecord.setUserId(UserUtils.getUserId());
        glVideoRecord.setPoint(glVideo.getPoint());
        glVideoRecord.setWatchTime(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
        glVideoRecordDao.insert(glVideoRecord);

        baseMapper.updateWatchCount(glVideo.getId());

        glPointsService.grantVideo(glVideoRecord,glVideo.getCompanyId(),glVideo.getCompanyName(),UserUtils.getMobileSha256(),glVideo.getId());
        videoFinish.setPoint(glVideo.getPoint());
        return videoFinish;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VideoFinish videoFinish2(VideoFinishDto videoFinishDto) {
        videoFinishDto.setMobileSha256(UserUtils.getMobileSha256());
        VideoFinish videoFinish = new VideoFinish();
        // 查询用户
        GlVideo glVideo = baseMapper.selectById(videoFinishDto.getVideoId());
        if(Objects.isNull(glVideo)
                || Deleted.DELETED.getDeleted().equals(glVideo.getDeleted())
                || !VideoState.PUBLISH.getState().equals(glVideo.getState())){
            throw new GicWxAppException("您看的视频已取消或不存在");
        }
        // 看视频得到的积分
        Integer videoPoint = glVideo.getPoint();
        LambdaQueryWrapper<GlAppletConfig> glAppletConfigQuery = Wrappers.lambdaQuery();
        glAppletConfigQuery.eq(GlAppletConfig::getParamKey, AppletConfigConstant.VIDEO_POINT_EVERY_DAY_LIMIT);
        GlAppletConfig glAppletConfig = glAppletConfigDao.selectOne(glAppletConfigQuery);
        if(Objects.isNull(glAppletConfig)){
            throw new GicWxAppException("看视频未配置获取的积分,请联系管理人员,感谢您的反馈");
        }
        Integer watchVideoPointSum = Integer.parseInt(glAppletConfig.getParamValue());
        LambdaQueryWrapper<GlVideoRecord> glVideoRecordLambdaQueryWrapper = Wrappers.lambdaQuery();
        glVideoRecordLambdaQueryWrapper.eq(GlVideoRecord::getUserId,UserUtils.getUserId());
        glVideoRecordLambdaQueryWrapper.eq(GlVideoRecord::getVideoId,videoFinishDto.getVideoId());
        glVideoRecordLambdaQueryWrapper.last("limit 1");
        GlVideoRecord glVideoRecord1 = glVideoRecordDao.selectOne(glVideoRecordLambdaQueryWrapper);
        if(Objects.nonNull(glVideoRecord1)){
            videoFinish.setPoint(0);
            return videoFinish;
        }

        LambdaQueryWrapper<GlVideoRecord> glVideoRecordLambdaQueryWrapper1 = Wrappers.lambdaQuery();
        glVideoRecordLambdaQueryWrapper1.select(GlVideoRecord::getPoint);
        glVideoRecordLambdaQueryWrapper1.eq(GlVideoRecord::getUserId,UserUtils.getUserId());
        List<Object> objects = glVideoRecordDao.selectObjs(glVideoRecordLambdaQueryWrapper1);
        if(CollUtil.isNotEmpty(objects)){
            Integer points = objects.stream().map(o -> (int) o).mapToInt(value -> value).sum();
            if(watchVideoPointSum > points){
                int remainPoint = watchVideoPointSum - points;
                Integer point = remainPoint > videoPoint?videoPoint:remainPoint;

                GlVideoRecord glVideoRecord = new GlVideoRecord();
                glVideoRecord.setVideoId(videoFinishDto.getVideoId());
                glVideoRecord.setUserId(UserUtils.getUserId());
                glVideoRecord.setPoint(point);
                glVideoRecord.setWatchTime(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
                glVideoRecordDao.insert(glVideoRecord);

                baseMapper.updateWatchCount(glVideo.getId());

                glPointsService.grantVideo(glVideoRecord,glVideo.getCompanyId(),glVideo.getCompanyName(),UserUtils.getMobileSha256(),glVideo.getId());
                videoFinish.setPoint(point);
            }
            if(watchVideoPointSum <= points){
                log.info("当前用户{}观看视频{}获取的积分已上限,上线积分:{}",UserUtils.getUserId(),glVideo.getId(),watchVideoPointSum);
                throw new GicWxAppException("您观看视频获得的积分已达到上限");
            }
            return videoFinish;
        }

        GlVideoRecord glVideoRecord = new GlVideoRecord();
        glVideoRecord.setVideoId(videoFinishDto.getVideoId());
        glVideoRecord.setUserId(UserUtils.getUserId());
        glVideoRecord.setPoint(glVideo.getPoint());
        glVideoRecord.setWatchTime(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
        glVideoRecordDao.insert(glVideoRecord);

        baseMapper.updateWatchCount(glVideo.getId());

        glPointsService.grantVideo(glVideoRecord,glVideo.getCompanyId(),glVideo.getCompanyName(),UserUtils.getMobileSha256(),glVideo.getId());
        videoFinish.setPoint(glVideo.getPoint());
        return videoFinish;
    }

    @Override
    public List<VideoList> indexVideoList() {
        LambdaQueryWrapper<GlVideo> glVideoLambdaQueryWrapper = Wrappers.lambdaQuery();
        glVideoLambdaQueryWrapper.eq(GlVideo::getDeleted, Deleted.UN_DELETE.getDeleted());
        glVideoLambdaQueryWrapper.eq(GlVideo::getState, VideoState.PUBLISH.getState());
        glVideoLambdaQueryWrapper.orderByDesc(GlVideo::getSequence);
        glVideoLambdaQueryWrapper.last("limit 3");
        List<GlVideo> glVideos = baseMapper.selectList(glVideoLambdaQueryWrapper);
        if(CollUtil.isEmpty(glVideos)){
            return Lists.newArrayList();
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        List<VideoList> videoLists = Lists.newArrayListWithCapacity(glVideos.size());
        for (GlVideo glVideo : glVideos) {
            VideoList videoList = new VideoList();
            videoList.setId(glVideo.getId());
            videoList.setVideoId(glVideo.getVideoId());
            videoList.setCoverImage(imagePrefix + glVideo.getCoverImage());
            videoList.setVideoChannelsId(glVideo.getVideoChannelsId());
            videoList.setName(glVideo.getName());
            videoList.setPoint(glVideo.getPoint());
            videoList.setFinishDuration(glVideo.getFinishDuration());
            videoList.setAvailable(Available.NO.getState());
            videoLists.add(videoList);
        }
        return videoLists;
    }
}
