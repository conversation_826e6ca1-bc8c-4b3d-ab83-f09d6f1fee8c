package com.lvpuhui.gic.wxapp.team.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.base.service.GlUploadFileService;
import com.lvpuhui.gic.wxapp.base.utils.ContentCheckUtils;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankDao;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRank;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.other.utils.AES256Util;
import com.lvpuhui.gic.wxapp.team.dao.*;
import com.lvpuhui.gic.wxapp.team.dto.*;
import com.lvpuhui.gic.wxapp.team.entity.*;
import com.lvpuhui.gic.wxapp.team.enums.JoinModeEnum;
import com.lvpuhui.gic.wxapp.team.enums.TeamCertificationStatusEnum;
import com.lvpuhui.gic.wxapp.team.enums.TeamStatusEnum;
import com.lvpuhui.gic.wxapp.team.enums.TeamTypeEnum;
import com.lvpuhui.gic.wxapp.team.service.TeamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 战队service实现
 * <AUTHOR>
 * @since 2023年05月24日 15:12:00
 */
@Slf4j
@Service
public class TeamServiceImpl implements TeamService{

    @Resource
    private GlTeamDao glTeamDao;

    @Resource
    private GlTeamMemberDao glTeamMemberDao;

    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlBehaviorService glBehaviorService;

    @Resource
    private GlRankDao glRankDao;

    @Resource
    private GlTeamMemberRankDao glTeamMemberRankDao;

    @Resource
    private GlTeamRankDao glTeamRankDao;

    @Resource
    private GlUploadFileService glUploadFileService;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Resource
    private ContentCheckUtils contentCheckUtils;

    @Resource
    private GlTeamCertificationDao glTeamCertificationDao;

    @Resource
    private AES256Util aes256Util;

    @Override
    public TeamMeVo meTeam() {
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,userId);
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        if(Objects.isNull(glTeamMember)){
            return null;
        }
        Long teamId = glTeamMember.getTeamId();
        GlTeam glTeam = glTeamDao.selectById(teamId);
        if(Objects.isNull(glTeam)){
            return null;
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        Long captainId = glTeam.getCaptainId();
        GlUser captainUser = glUserDao.selectById(captainId);
        TeamMeVo teamMeVo = new TeamMeVo();
        BeanUtil.copyProperties(glTeam,teamMeVo);
        teamMeVo.setCertification(glTeam.getCertificationStatus());
        teamMeVo.setNickName(captainUser.getNickName());
        teamMeVo.setAvatarUrl(captainUser.getAvatarUrl());
        teamMeVo.setTeamLogo(imagePrefix + teamMeVo.getTeamLogo());
        return teamMeVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTeam(TeamCreateDto teamCreateDto) {
        if(Objects.nonNull(teamCreateDto.getType())
                && (teamCreateDto.getType() < TeamTypeEnum.ENTERPRISE.getType()
                || teamCreateDto.getType() > TeamTypeEnum.OTHER.getType())){
            throw new GicWxAppException("请您使用正确的方式创建战队");
        }
        if(!Objects.equals(teamCreateDto.getJoinMode(), JoinModeEnum.OPEN.getState())
                && !Objects.equals(teamCreateDto.getJoinMode(),JoinModeEnum.INVITE_ONLY.getState())){
            throw new GicWxAppException("请您使用正确的方式创建战队");
        }
        boolean checkContent = contentCheckUtils.checkContent(teamCreateDto.getTeamName());
        if(!checkContent){
            throw new GicWxAppException("您创建的战队名称不符合规范");
        }
        if(StrUtil.isNotBlank(teamCreateDto.getInviteCode())){
            if(teamCreateDto.getInviteCode().length() > 6){
                throw new GicWxAppException("战队密码最多6位数字");
            }
            if(!ReUtil.isMatch(PatternPool.NUMBERS,teamCreateDto.getInviteCode())){
                throw new GicWxAppException("战队密码只能是数字");
            }
        }

        Long userId = UserUtils.getUserId();
        String mobileSha256 = UserUtils.getMobileSha256();
        BigDecimal emission = null;
        GlRank glRank = glRankDao.selectById(mobileSha256);
        if(Objects.isNull(glRank)){
            emission = glBehaviorService.getSumEmissionByMobileSha256(mobileSha256);
        }else {
            emission = BigDecimal.valueOf(glRank.getEmission());
        }
        emission = Objects.isNull(emission)?BigDecimal.ZERO:emission;
        GlTeam glTeam = new GlTeam();
        glTeam.setTeamName(teamCreateDto.getTeamName());
        glTeam.setCaptainId(userId);
        glTeam.setTeamLogo(teamCreateDto.getTeamLogo());
        glTeam.setInviteCode(teamCreateDto.getInviteCode());
        glTeam.setJoinMode(teamCreateDto.getJoinMode());
        glTeam.setStatus(TeamStatusEnum.ACTIVE.getStatus());
        glTeam.setMemberCount(1);
        glTeam.setEmission(emission);
        glTeam.setCreated(LocalDateTime.now());
        glTeam.setUpdated(LocalDateTime.now());
        glTeam.setType(teamCreateDto.getType());
        try {
            glTeamDao.insert(glTeam);
        }catch (DuplicateKeyException duplicateKeyException){
            throw new GicWxAppException("您已有战队了");
        }

        GlTeamMember glTeamMember = new GlTeamMember();
        glTeamMember.setTeamId(glTeam.getId());
        glTeamMember.setUserId(userId);
        glTeamMember.setInviteUserId(null);
        glTeamMember.setCreated(LocalDateTime.now());
        try {
            glTeamMemberDao.insert(glTeamMember);
        }catch (DuplicateKeyException duplicateKeyException){
            throw new GicWxAppException("您已有战队了");
        }

        GlTeamMemberRank glTeamMemberRank = new GlTeamMemberRank();
        glTeamMemberRank.setTeamId(glTeam.getId());
        glTeamMemberRank.setUserId(userId);
        glTeamMemberRank.setEmission(emission);
        glTeamMemberRankDao.insert(glTeamMemberRank);

        GlTeamRank glTeamRank = new GlTeamRank();
        glTeamRank.setTeamId(glTeam.getId());
        glTeamRank.setEmission(emission);
        glTeamRank.setCreated(LocalDateTime.now());
        glTeamRank.setUpdated(LocalDateTime.now());
        glTeamRankDao.insert(glTeamRank);

        glUploadFileService.useFile(Lists.newArrayList(teamCreateDto.getTeamLogo()));
    }

    @Override
    public List<TeamListVo> teamList(TeamListDto teamListDto) {
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,userId);
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        Long myTeamId = Objects.nonNull(glTeamMember)?glTeamMember.getTeamId():null;
        int pageSize = 10;
        String imagePrefix = glAppletConfigService.getImagePrefix();
        int start = PageUtil.getStart(teamListDto.getPage() - 1, pageSize);
        teamListDto.setStart(start);
        teamListDto.setSize(pageSize);
        List<TeamListVo> teamListVos = glTeamDao.getTeamList(teamListDto);
        if (CollUtil.isNotEmpty(teamListVos)) {
            teamListVos.forEach(teamListVo -> {
                teamListVo.setTeamLogo(imagePrefix + teamListVo.getTeamLogo());
                if(Objects.nonNull(myTeamId) && teamListVo.getId().equals(myTeamId)){
                    teamListVo.setIsMyTeam(true);
                }
            });
        }
        return teamListVos;
    }

    @Override
    public TeamListDetailVo teamListDetail(Long id) {
        GlTeam glTeam = glTeamDao.selectById(id);
        if(Objects.isNull(glTeam)
                || !TeamStatusEnum.ACTIVE.getStatus().equals(glTeam.getStatus())){
            throw new GicWxAppException("此战队已解散");
        }
        TeamListDetailVo teamListDetailVo = new TeamListDetailVo();
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,userId);
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        if(Objects.nonNull(glTeamMember)){
            teamListDetailVo.setMyTeamId(glTeamMember.getTeamId());
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        Long captainId = glTeam.getCaptainId();
        GlUser captainUser = glUserDao.selectById(captainId);
        BeanUtil.copyProperties(glTeam,teamListDetailVo);
        teamListDetailVo.setCertification(glTeam.getCertificationStatus());
        teamListDetailVo.setNickName(captainUser.getNickName());
        teamListDetailVo.setAvatarUrl(captainUser.getAvatarUrl());
        teamListDetailVo.setTeamLogo(imagePrefix + teamListDetailVo.getTeamLogo());
        if(StrUtil.isNotBlank(glTeam.getInviteCode())){
            teamListDetailVo.setPasswordBlank(false);
        }
        LambdaQueryWrapper<GlTeamRank> glTeamRankLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamRankLambdaQueryWrapper.eq(GlTeamRank::getTeamId,glTeam.getId());
        glTeamRankLambdaQueryWrapper.last("limit 1");
        GlTeamRank glTeamRank = glTeamRankDao.selectOne(glTeamRankLambdaQueryWrapper);
        if(Objects.nonNull(glTeamRank)){
            teamListDetailVo.setRank(glTeamRank.getRank());
        }
        LambdaQueryWrapper<GlTeamMemberRank> glTeamMemberRankLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamMemberRankLambdaQueryWrapper.eq(GlTeamMemberRank::getTeamId,glTeam.getId());
        glTeamMemberRankLambdaQueryWrapper.eq(GlTeamMemberRank::getUserId,userId);
        glTeamMemberRankLambdaQueryWrapper.last("limit 1");
        GlTeamMemberRank glTeamMemberRank = glTeamMemberRankDao.selectOne(glTeamMemberRankLambdaQueryWrapper);
        if(Objects.nonNull(glTeamMemberRank)){
            teamListDetailVo.setMemberRank(glTeamMemberRank.getRank());
        }
        GlRank glRank = glRankDao.selectById(UserUtils.getMobileSha256());
        if(Objects.nonNull(glRank)){
            double emission = Objects.isNull(glRank.getEmission())?0D:glRank.getEmission();
            teamListDetailVo.setMyEmissionText(CalcUtil.weightFormat(emission));
        }
        if(Objects.nonNull(glTeam.getEmission()) && glTeam.getEmission().compareTo(BigDecimal.ZERO) > 0
                && Objects.nonNull(glTeam.getMemberCount()) && glTeam.getMemberCount() > 0){
            BigDecimal divide = glTeam.getEmission().divide(BigDecimal.valueOf(glTeam.getMemberCount()),2, RoundingMode.HALF_UP);
            teamListDetailVo.setAvgEmission(CalcUtil.weightFormat(divide.doubleValue()));
        }else{
            teamListDetailVo.setAvgEmission("0g");
        }

        List<TeamListDetailMemberVo> memberVos = glTeamMemberRankDao.selectTeamRankMembers(id);
        teamListDetailVo.setMemberVos(memberVos);
        return teamListDetailVo;
    }

    @Override
    public TeamDetailVo teamDetail() {
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,userId);
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        if(Objects.isNull(glTeamMember)){
            throw new GicWxAppException("您还未加入战队");
        }
        Long teamId = glTeamMember.getTeamId();
        GlTeam glTeam = glTeamDao.selectById(teamId);
        if(Objects.isNull(glTeam) || TeamStatusEnum.DELETED.getStatus().equals(glTeam.getStatus())){
            throw new GicWxAppException("您的战队不存在");
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        Long captainId = glTeam.getCaptainId();
        GlUser captainUser = glUserDao.selectById(captainId);
        TeamDetailVo teamDetailVo = new TeamDetailVo();
        BeanUtil.copyProperties(glTeam,teamDetailVo);
        teamDetailVo.setCertification(glTeam.getCertificationStatus());
        teamDetailVo.setNickName(captainUser.getNickName());
        teamDetailVo.setAvatarUrl(captainUser.getAvatarUrl());
        teamDetailVo.setTeamLogoId(teamDetailVo.getTeamLogo());
        teamDetailVo.setTeamLogo(imagePrefix + teamDetailVo.getTeamLogo());

        if(userId.equals(glTeam.getCaptainId())){
            teamDetailVo.setIsCaptain(true);
        }
        LambdaQueryWrapper<GlTeamRank> glTeamRankLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamRankLambdaQueryWrapper.eq(GlTeamRank::getTeamId,glTeam.getId());
        glTeamRankLambdaQueryWrapper.last("limit 1");
        GlTeamRank glTeamRank = glTeamRankDao.selectOne(glTeamRankLambdaQueryWrapper);
        if(Objects.nonNull(glTeamRank)){
            teamDetailVo.setRank(glTeamRank.getRank());
        }
        LambdaQueryWrapper<GlTeamMemberRank> glTeamMemberRankLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamMemberRankLambdaQueryWrapper.eq(GlTeamMemberRank::getTeamId,glTeam.getId());
        glTeamMemberRankLambdaQueryWrapper.eq(GlTeamMemberRank::getUserId,userId);
        glTeamMemberRankLambdaQueryWrapper.last("limit 1");
        GlTeamMemberRank glTeamMemberRank = glTeamMemberRankDao.selectOne(glTeamMemberRankLambdaQueryWrapper);
        if(Objects.nonNull(glTeamMemberRank)){
            teamDetailVo.setMemberRank(glTeamMemberRank.getRank());
        }

        GlRank glRank = glRankDao.selectById(UserUtils.getMobileSha256());
        if(Objects.nonNull(glRank)){
            double emission = Objects.isNull(glRank.getEmission())?0D:glRank.getEmission();
            teamDetailVo.setMyEmissionText(CalcUtil.weightFormat(emission));
        }

        List<TeamListDetailMemberVo> memberVos = glTeamMemberRankDao.selectTeamRankMembers(glTeam.getId());
        if(CollUtil.isNotEmpty(memberVos)){
            List<TeamDetailMemberVo> teamDetailMemberVos = memberVos.stream().map(teamListDetailMemberVo -> {
                TeamDetailMemberVo teamDetailMemberVo = new TeamDetailMemberVo();
                BeanUtil.copyProperties(teamListDetailMemberVo, teamDetailMemberVo);
                return teamDetailMemberVo;
            }).collect(Collectors.toList());
            teamDetailVo.setMemberVos(teamDetailMemberVos);
        }
        TeamAvgEmissionUserCountDto teamAvgEmissionUserCountDto = glTeamMemberRankDao.selectEmissionUserCount(glTeam.getId());
        if(Objects.nonNull(teamAvgEmissionUserCountDto)){
            teamDetailVo.setEmissionUserNumber(teamAvgEmissionUserCountDto.getUserCount());
        }
        if(Objects.nonNull(glTeam.getEmission()) && glTeam.getEmission().compareTo(BigDecimal.ZERO) > 0
                && Objects.nonNull(glTeam.getMemberCount()) && glTeam.getMemberCount() > 0){
            BigDecimal divide = glTeam.getEmission().divide(BigDecimal.valueOf(glTeam.getMemberCount()),2, RoundingMode.HALF_UP);
            teamDetailVo.setAvgEmission(CalcUtil.weightFormat(divide.doubleValue()));
        }else{
            teamDetailVo.setAvgEmission("0g");
        }
        return teamDetailVo;
    }

    @Override
    public TeamRankVo teamRank() {
        TeamRankVo teamRankVo = new TeamRankVo();
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,userId);
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        if(Objects.isNull(glTeamMember)){
            // 没有加入战队
            teamRankVo.setJoinTeam(false);
            String mobileSha256 = UserUtils.getMobileSha256();
            BigDecimal emission = null;
            GlRank glRank = glRankDao.selectById(mobileSha256);
            if(Objects.isNull(glRank)){
                emission = glBehaviorService.getSumEmissionByMobileSha256(mobileSha256);
            }else {
                emission = BigDecimal.valueOf(glRank.getEmission());
            }
            GlUser glUser = glUserDao.selectById(userId);
            TeamNotJoinVo teamNotJoinVo = new TeamNotJoinVo();
            teamNotJoinVo.setUserId(userId);
            teamNotJoinVo.setEmission(emission);
            teamNotJoinVo.setNickName(glUser.getNickName());
            teamNotJoinVo.setAvatarUrl(glUser.getAvatarUrl());
            teamRankVo.setTeamNotJoinVo(teamNotJoinVo);
        }
        Long teamId = Objects.isNull(glTeamMember)?null:glTeamMember.getTeamId();
        String imagePrefix = glAppletConfigService.getImagePrefix();
        // 查询前20的战队
        List<TeamRankListVo> teamRankListVos = glTeamRankDao.selectTeamRanks();
        teamRankVo.setTeamRankListVos(teamRankListVos);
        for (TeamRankListVo teamRankListVo : teamRankListVos) {
            teamRankListVo.setTeamLogo(imagePrefix + teamRankListVo.getTeamLogo());
            if(Objects.nonNull(teamId) && teamRankListVo.getTeamId().equals(teamId)){
                teamRankVo.setCurrentTeamRank(teamRankListVo);
            }
        }
        if(Objects.isNull(teamRankVo.getCurrentTeamRank()) && Objects.nonNull(teamId)){
            // 查询当前登录人的战队
            TeamRankListVo teamRankListVo = glTeamRankDao.selectTeamRankByTeamId(teamId);
            if(Objects.nonNull(teamRankListVo)){
                teamRankListVo.setTeamLogo(imagePrefix + teamRankListVo.getTeamLogo());
            }
            teamRankVo.setCurrentTeamRank(teamRankListVo);
        }
        return teamRankVo;
    }

    @Override
    public TeamMemberRankVo teamMemberRank() {
        TeamMemberRankVo teamMemberRankVo = new TeamMemberRankVo();
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,userId);
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        if(Objects.isNull(glTeamMember)){
            // 没有加入战队
            teamMemberRankVo.setJoinTeam(false);
            String mobileSha256 = UserUtils.getMobileSha256();
            BigDecimal emission = null;
            GlRank glRank = glRankDao.selectById(mobileSha256);
            if(Objects.isNull(glRank)){
                emission = glBehaviorService.getSumEmissionByMobileSha256(mobileSha256);
            }else {
                emission = BigDecimal.valueOf(glRank.getEmission());
            }
            GlUser glUser = glUserDao.selectById(userId);
            TeamNotJoinVo teamNotJoinVo = new TeamNotJoinVo();
            teamNotJoinVo.setUserId(userId);
            teamNotJoinVo.setEmission(emission);
            teamNotJoinVo.setNickName(glUser.getNickName());
            teamNotJoinVo.setAvatarUrl(glUser.getAvatarUrl());
            teamMemberRankVo.setTeamNotJoinVo(teamNotJoinVo);
            return teamMemberRankVo;
        }
        Long teamId = glTeamMember.getTeamId();
        GlTeam glTeam = glTeamDao.selectById(teamId);
        if(Objects.isNull(glTeam)){
            throw new GicWxAppException("您的战队不存在");
        }
        // 查询战队前20的贡献成员
        List<TeamMemberRankListVo> teamMemberRankListVos = glTeamMemberRankDao.selectTeamMemberRanks(teamId);
        teamMemberRankVo.setMemberRankListVos(teamMemberRankListVos);
        for (TeamMemberRankListVo teamMemberRankListVo : teamMemberRankListVos) {
            if(teamMemberRankListVo.getUserId().equals(userId)){
                teamMemberRankListVo.setTeamName(glTeam.getTeamName());
                teamMemberRankVo.setCurrentMemberRank(teamMemberRankListVo);
                break;
            }
        }
        if(Objects.isNull(teamMemberRankVo.getCurrentMemberRank())){
            // 查询当前登录人的排行
            TeamMemberRankListVo teamMemberRankListVo = glTeamMemberRankDao.selectTeamMemberRankByUserId(userId,teamId);
            teamMemberRankListVo.setTeamName(glTeam.getTeamName());
            teamMemberRankVo.setCurrentMemberRank(teamMemberRankListVo);
        }
        return teamMemberRankVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void teamJoin(TeamJoinDto teamJoinDto) {
        GlTeam glTeam = glTeamDao.selectById(teamJoinDto.getTeamId());
        if(Objects.isNull(glTeam)
                || !TeamStatusEnum.ACTIVE.getStatus().equals(glTeam.getStatus())){
            throw new GicWxAppException("此战队已解散");
        }
        if(glTeam.getJoinMode().equals(JoinModeEnum.INVITE_ONLY.getState()) && StrUtil.isNotBlank(glTeam.getInviteCode())){
            if(StrUtil.isBlank(teamJoinDto.getInviteCode())){
                throw new GicWxAppException("请输入战队密码");
            }
            if(!glTeam.getInviteCode().equals(teamJoinDto.getInviteCode())){
                throw new GicWxAppException("您输入的战队密码错误");
            }
        }

        Long userId = UserUtils.getUserId();
        GlTeamMember glTeamMember = new GlTeamMember();
        glTeamMember.setTeamId(glTeam.getId());
        glTeamMember.setUserId(userId);
        glTeamMember.setInviteUserId(teamJoinDto.getInviteUserId());
        glTeamMember.setCreated(LocalDateTime.now());
        try {
            glTeamMemberDao.insert(glTeamMember);
        }catch (DuplicateKeyException duplicateKeyException){
            throw new GicWxAppException("您已有战队了");
        }
        String mobileSha256 = UserUtils.getMobileSha256();
        BigDecimal emission = null;
        GlRank glRank = glRankDao.selectById(mobileSha256);
        if(Objects.isNull(glRank)){
            emission = glBehaviorService.getSumEmissionByMobileSha256(mobileSha256);
        }else {
            emission = BigDecimal.valueOf(glRank.getEmission());
        }

        GlTeamMemberRank glTeamMemberRank = new GlTeamMemberRank();
        glTeamMemberRank.setTeamId(glTeam.getId());
        glTeamMemberRank.setUserId(userId);
        glTeamMemberRank.setEmission(Objects.isNull(emission)?BigDecimal.ZERO:emission);
        glTeamMemberRankDao.insert(glTeamMemberRank);

        glTeamDao.updateIncrementEmission(glTeamMemberRank.getEmission(),glTeam.getId());

        glTeamRankDao.updateIncrementEmission(glTeamMemberRank.getEmission(),glTeam.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void teamExit(Long teamId) {
        GlTeam glTeam = glTeamDao.selectById(teamId);
        if(Objects.isNull(glTeam)
                || TeamStatusEnum.DELETED.getStatus().equals(glTeam.getStatus())){
            return;
        }
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getTeamId,teamId);
        teamMemberLambdaQueryWrapper.orderByDesc(GlTeamMember::getId);
        teamMemberLambdaQueryWrapper.last("limit 2");
        List<GlTeamMember> glTeamMembers = glTeamMemberDao.selectList(teamMemberLambdaQueryWrapper);
        if(CollUtil.isNotEmpty(glTeamMembers)
                && glTeamMembers.size() == 1
                && glTeamMembers.get(0).getUserId().equals(glTeam.getCaptainId())
                && glTeam.getCaptainId().equals(userId)){

            LambdaQueryWrapper<GlTeamMemberRank> deleteTeamMemberRankQuery = Wrappers.lambdaQuery();
            deleteTeamMemberRankQuery.eq(GlTeamMemberRank::getUserId,userId);
            deleteTeamMemberRankQuery.eq(GlTeamMemberRank::getTeamId,teamId);
            GlTeamMemberRank glTeamMemberRank = glTeamMemberRankDao.selectOne(deleteTeamMemberRankQuery);
            if(Objects.nonNull(glTeamMemberRank)){
                glTeamDao.updateDeductEmission(glTeamMemberRank.getEmission(),teamId);
            }
            glTeamMemberRankDao.delete(deleteTeamMemberRankQuery);
            // 如果只有当前负责人,则直接删除战队
            LambdaUpdateWrapper<GlTeam> glTeamLambdaUpdateWrapper = Wrappers.lambdaUpdate();
            glTeamLambdaUpdateWrapper.eq(GlTeam::getId,teamId);

            GlTeam updateGlTeam = new GlTeam();
            updateGlTeam.setCaptainId(null);
            updateGlTeam.setStatus(TeamStatusEnum.DELETED.getStatus());
            glTeamDao.update(updateGlTeam,glTeamLambdaUpdateWrapper);

            LambdaQueryWrapper<GlTeamMember> deleteTeamMemberQuery = Wrappers.lambdaQuery();
            deleteTeamMemberQuery.eq(GlTeamMember::getUserId,userId);
            deleteTeamMemberQuery.eq(GlTeamMember::getTeamId,teamId);
            glTeamMemberDao.delete(deleteTeamMemberQuery);

            LambdaQueryWrapper<GlTeamRank> deleteTeamRankLambdaQueryWrapper = Wrappers.lambdaQuery();
            deleteTeamRankLambdaQueryWrapper.eq(GlTeamRank::getTeamId,teamId);
            glTeamRankDao.delete(deleteTeamRankLambdaQueryWrapper);
            return;
        }
        if(glTeam.getCaptainId().equals(userId)){
            throw new GicWxAppException("请您先把战队变更为其他人才可以退出");
        }
        LambdaQueryWrapper<GlTeamMember> deleteTeamMemberQuery = Wrappers.lambdaQuery();
        deleteTeamMemberQuery.eq(GlTeamMember::getUserId,userId);
        deleteTeamMemberQuery.eq(GlTeamMember::getTeamId,teamId);
        glTeamMemberDao.delete(deleteTeamMemberQuery);

        LambdaQueryWrapper<GlTeamMemberRank> deleteTeamMemberRankQuery = Wrappers.lambdaQuery();
        deleteTeamMemberRankQuery.eq(GlTeamMemberRank::getUserId,userId);
        deleteTeamMemberRankQuery.eq(GlTeamMemberRank::getTeamId,teamId);
        GlTeamMemberRank glTeamMemberRank = glTeamMemberRankDao.selectOne(deleteTeamMemberRankQuery);
        if(Objects.nonNull(glTeamMemberRank)){
            glTeamDao.updateDeductEmission(glTeamMemberRank.getEmission(),teamId);
            glTeamRankDao.updateDeductEmission(glTeamMemberRank.getEmission(),glTeam.getId());
        }
        glTeamMemberRankDao.delete(deleteTeamMemberRankQuery);
    }

    @Override
    public void updateTeam(TeamUpdateDto teamUpdateDto) {
        if(Objects.nonNull(teamUpdateDto.getType())
                && (teamUpdateDto.getType() < TeamTypeEnum.ENTERPRISE.getType()
                || teamUpdateDto.getType() > TeamTypeEnum.OTHER.getType())){
            throw new GicWxAppException("请您使用正确的方式创建战队");
        }
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,userId);
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        if(Objects.isNull(glTeamMember)){
            throw new GicWxAppException("您还未加入战队");
        }
        Long teamId = glTeamMember.getTeamId();
        GlTeam glTeam = glTeamDao.selectById(teamId);
        if(Objects.isNull(glTeam) || TeamStatusEnum.DELETED.getStatus().equals(glTeam.getStatus())){
            throw new GicWxAppException("您的战队不存在");
        }
        if(!teamUpdateDto.getTeamId().equals(teamId)){
            throw new GicWxAppException("您只能修改自己的战队");
        }
        if(!glTeam.getCaptainId().equals(userId)){
            throw new GicWxAppException("战队只能负责人修改");
        }
        if(!Objects.equals(teamUpdateDto.getJoinMode(), JoinModeEnum.OPEN.getState())
                && !Objects.equals(teamUpdateDto.getJoinMode(),JoinModeEnum.INVITE_ONLY.getState())){
            throw new GicWxAppException("请您使用正确的方式创建战队");
        }
        boolean checkContent = contentCheckUtils.checkContent(teamUpdateDto.getTeamName());
        if(!checkContent){
            throw new GicWxAppException("您创建的战队名称不符合规范");
        }
        if(StrUtil.isNotBlank(teamUpdateDto.getInviteCode())){
            if(teamUpdateDto.getInviteCode().length() > 6){
                throw new GicWxAppException("战队密码最多6位数字");
            }
            if(!ReUtil.isMatch(PatternPool.NUMBERS,teamUpdateDto.getInviteCode())){
                throw new GicWxAppException("战队密码只能是数字");
            }
        }
        glTeam.setTeamName(teamUpdateDto.getTeamName());
        glTeam.setTeamLogo(teamUpdateDto.getTeamLogo());
        glTeam.setJoinMode(teamUpdateDto.getJoinMode());
        glTeam.setInviteCode(teamUpdateDto.getInviteCode());
        glTeam.setType(teamUpdateDto.getType());
        glTeamDao.updateById(glTeam);
    }

    @Override
    public TeamChangeSearchVo teamChangeSearch(String mobile) {
        mobile = mobile.replaceAll(" ","");
        if(!Validator.isMobile(mobile)){
            throw new GicWxAppException("请输入手机号");
        }
        String changeMobileSha256 = SecureUtil.sha256(mobile);
        LambdaQueryWrapper<GlUser> glUserLambdaQueryWrapper = Wrappers.lambdaQuery();
        glUserLambdaQueryWrapper.eq(GlUser::getMobileSha256,changeMobileSha256);
        GlUser glUser = glUserDao.selectOne(glUserLambdaQueryWrapper);
        if(Objects.isNull(glUser)){
            return null;
        }
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeam> glTeamLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamLambdaQueryWrapper.eq(GlTeam::getCaptainId,userId);
        glTeamLambdaQueryWrapper.eq(GlTeam::getStatus,TeamStatusEnum.ACTIVE.getStatus());
        glTeamLambdaQueryWrapper.last("limit 1");
        GlTeam glTeam = glTeamDao.selectOne(glTeamLambdaQueryWrapper);
        if(Objects.isNull(glTeam)){
            throw new GicWxAppException("您还没有战队哟");
        }
        if(!glTeam.getCaptainId().equals(userId)){
            throw new GicWxAppException("战队只能负责人变更");
        }
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,glUser.getId());
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getTeamId,glTeam.getId());
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        if(Objects.isNull(glTeamMember)){
            return null;
        }
        TeamChangeSearchVo teamChangeSearchVo = new TeamChangeSearchVo();
        teamChangeSearchVo.setUserId(glUser.getId());
        teamChangeSearchVo.setAvatarUrl(glUser.getAvatarUrl());
        teamChangeSearchVo.setNickName(glUser.getNickName());
        return teamChangeSearchVo;
    }

    @Override
    public void teamChangeCreator(Long changeUserId) {
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeam> glTeamLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamLambdaQueryWrapper.eq(GlTeam::getCaptainId,userId);
        glTeamLambdaQueryWrapper.eq(GlTeam::getStatus,TeamStatusEnum.ACTIVE.getStatus());
        glTeamLambdaQueryWrapper.last("limit 1");
        GlTeam glTeam = glTeamDao.selectOne(glTeamLambdaQueryWrapper);
        if(Objects.isNull(glTeam)){
            throw new GicWxAppException("战队只能负责人变更");
        }
        if(glTeam.getCaptainId().equals(changeUserId)){
            throw new GicWxAppException("您已是战队负责人");
        }
        LambdaQueryWrapper<GlTeamMember> teamMemberLambdaQueryWrapper = Wrappers.lambdaQuery();
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getUserId,changeUserId);
        teamMemberLambdaQueryWrapper.eq(GlTeamMember::getTeamId,glTeam.getId());
        GlTeamMember glTeamMember = glTeamMemberDao.selectOne(teamMemberLambdaQueryWrapper);
        if(Objects.isNull(glTeamMember)){
            throw new GicWxAppException("请您用正确的渠道变更");
        }
        glTeam.setCaptainId(changeUserId);
        glTeamDao.updateById(glTeam);
    }

    @Override
    public void teamCertificationApply(TeamCertificationApplyDto teamCertificationApplyDto) {
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeam> glTeamLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamLambdaQueryWrapper.eq(GlTeam::getCaptainId,userId);
        glTeamLambdaQueryWrapper.eq(GlTeam::getStatus,TeamStatusEnum.ACTIVE.getStatus());
        glTeamLambdaQueryWrapper.last("limit 1");
        GlTeam glTeam = glTeamDao.selectOne(glTeamLambdaQueryWrapper);
        if(Objects.isNull(glTeam)){
            throw new GicWxAppException("战队加V认证只能负责人申请");
        }
        if(StrUtil.isNotBlank(teamCertificationApplyDto.getApplicantNumber())){
            if(!Validator.isCitizenId(teamCertificationApplyDto.getApplicantNumber())){
                throw new GicWxAppException("身份证号错误");
            }
        }
        LambdaQueryWrapper<GlTeamCertificationDO> glTeamCertificationDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamCertificationDOLambdaQueryWrapper.eq(GlTeamCertificationDO::getTeamId,glTeam.getId());
        glTeamCertificationDOLambdaQueryWrapper.orderByDesc(GlTeamCertificationDO::getId);
        glTeamCertificationDOLambdaQueryWrapper.last("limit 1");
        GlTeamCertificationDO glTeamCertificationDO = glTeamCertificationDao.selectOne(glTeamCertificationDOLambdaQueryWrapper);
        if(Objects.nonNull(glTeamCertificationDO)){
            if(TeamCertificationStatusEnum.REVIEW_PASS.getStatus().equals(glTeamCertificationDO.getStatus())
                    || TeamCertificationStatusEnum.REVIEW_IN.getStatus().equals(glTeamCertificationDO.getStatus())){
                throw new GicWxAppException("您的认证申请"+TeamCertificationStatusEnum.getDescribe(glTeamCertificationDO.getStatus()));
            }
        }
        glTeamCertificationDO = new GlTeamCertificationDO();
        BeanUtils.copyProperties(teamCertificationApplyDto,glTeamCertificationDO);
        glTeamCertificationDO.setStatus(TeamCertificationStatusEnum.REVIEW_IN.getStatus());
        glTeamCertificationDO.setUpdated(LocalDateTime.now());
        glTeamCertificationDO.setUserId(userId);
        glTeamCertificationDO.setTeamId(glTeam.getId());
        glTeamCertificationDO.setCreated(LocalDateTime.now());
        glTeamCertificationDO.setSubmitTime(LocalDateTime.now());
        if(StrUtil.isNotBlank(teamCertificationApplyDto.getApplicantNumber())){
            glTeamCertificationDO.setApplicantNumber(aes256Util.encrypt(teamCertificationApplyDto.getApplicantNumber()));
        }
        try {
            glTeamCertificationDao.insert(glTeamCertificationDO);
        }catch (DuplicateKeyException e){
            throw new GicWxAppException("您已提交申请,请勿重复提交");
        }
    }

    @Override
    public TeamCertificationDetailVo teamCertificationDetail() {
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlTeam> glTeamLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamLambdaQueryWrapper.eq(GlTeam::getCaptainId,userId);
        glTeamLambdaQueryWrapper.eq(GlTeam::getStatus,TeamStatusEnum.ACTIVE.getStatus());
        glTeamLambdaQueryWrapper.last("limit 1");
        GlTeam glTeam = glTeamDao.selectOne(glTeamLambdaQueryWrapper);
        if(Objects.isNull(glTeam)){
            return null;
        }
        LambdaQueryWrapper<GlTeamCertificationDO> glTeamCertificationDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        glTeamCertificationDOLambdaQueryWrapper.eq(GlTeamCertificationDO::getTeamId,glTeam.getId());
        glTeamCertificationDOLambdaQueryWrapper.orderByDesc(GlTeamCertificationDO::getId);
        glTeamCertificationDOLambdaQueryWrapper.last("limit 1");
        GlTeamCertificationDO glTeamCertificationDO = glTeamCertificationDao.selectOne(glTeamCertificationDOLambdaQueryWrapper);
        if(Objects.isNull(glTeamCertificationDO)){
            return null;
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        TeamCertificationDetailVo teamCertificationDetailVo = new TeamCertificationDetailVo();
        BeanUtils.copyProperties(glTeamCertificationDO,teamCertificationDetailVo);
        if(StrUtil.isNotBlank(glTeamCertificationDO.getApplicantNumber())){
            teamCertificationDetailVo.setApplicantNumber(aes256Util.decrypt(glTeamCertificationDO.getApplicantNumber()));
        }
        teamCertificationDetailVo.setOrganizationLicense(imagePrefix + teamCertificationDetailVo.getOrganizationLicense());
        return teamCertificationDetailVo;
    }
}
