package com.lvpuhui.gic.wxapp.carbon.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonModelDao;
import com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonModelEmissionDao;
import com.lvpuhui.gic.wxapp.carbon.dto.CarbonEmissionSumGroupByProjectDto;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelDO;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelEmissionDO;
import com.lvpuhui.gic.wxapp.carbon.service.GlCarbonModelEmissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023年04月12日 16:57:00
 */
@Slf4j
@Service
public class GlCarbonModelEmissionServiceImpl extends ServiceImpl<GlCarbonModelEmissionDao, GlCarbonModelEmissionDO> implements GlCarbonModelEmissionService {

    @Resource
    private GlCarbonModelEmissionDao glCarbonModelEmissionDao;

    @Resource
    private GlCarbonModelDao glCarbonModelDao;

    @Override
    public List<CarbonEmissionSumGroupByProjectDto> getEmissionSumGroupByProject(String mobileSha256) {
        return glCarbonModelEmissionDao.getEmissionSumGroupByProject(mobileSha256);
    }

    @Override
    public Map<Long, String> getModelIdNameMapByModelIds(List<Long> modelIds) {
        if(CollUtil.isEmpty(modelIds)){
            return Maps.newHashMap();
        }
        List<GlCarbonModelDO> glCarbonModelDOS = glCarbonModelDao.selectBatchIds(modelIds);
        if(CollUtil.isEmpty(glCarbonModelDOS)){
            return Maps.newHashMap();
        }
        return glCarbonModelDOS.stream().collect(Collectors.toMap(GlCarbonModelDO::getId, GlCarbonModelDO::getName, (k1, k2) -> k1));
    }
}
