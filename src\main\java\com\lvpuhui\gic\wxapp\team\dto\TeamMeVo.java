package com.lvpuhui.gic.wxapp.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 我的战队返回值
 * <AUTHOR>
 * @since 2023年05月24日 15:07:00
 */
@Data
@Schema(description = "我的战队返回值")
public class TeamMeVo {

    /**
     * 战队ID
     */
    @Schema(description = "战队ID")
    private Long id;

    /**
     * 战队名称
     */
    @Schema(description = "战队名称")
    private String teamName;

    /**
     * 战队Logo
     */
    @Schema(description = "战队Logo")
    private String teamLogo;

    /**
     * 成员人数
     */
    @Schema(description = "成员人数")
    private Integer memberCount;

    /**
     * 创建人昵称
     */
    @Schema(description = "创建人昵称")
    private String nickName;

    /**
     * 创建人头像
     */
    @Schema(description = "创建人头像")
    private String avatarUrl;

    /**
     * 是否已认证 0：未认证  1：已认证
     */
    @Schema(description = "是否已认证 0：未认证  1：已认证")
    private Integer certification;

    /**
     * 类型 0:企业 1:学校 2:政府 3:其他
     */
    @Schema(description = "类型 0:企业 1:学校 2:政府 3:其他")
    private Integer type;
}
