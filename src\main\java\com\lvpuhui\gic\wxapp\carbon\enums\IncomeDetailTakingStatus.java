package com.lvpuhui.gic.wxapp.carbon.enums;

import lombok.Getter;

/**
 * 碳汇实例详情状态枚举
 * <AUTHOR>
 * @since 2023年03月22日 17:26:00
 */
@Getter
public enum IncomeDetailTakingStatus {

    UN_TAKING(0,"未提现"),
    TAKING(1,"已提现"),
    ;

    private Integer code;

    private String describe;

    IncomeDetailTakingStatus(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
