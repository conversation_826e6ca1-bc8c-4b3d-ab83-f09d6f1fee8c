package com.lvpuhui.gic.wxapp.carbon.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lvpuhui.gic.wxapp.carbon.dao.*;
import com.lvpuhui.gic.wxapp.carbon.dto.IncomeTakingVo;
import com.lvpuhui.gic.wxapp.carbon.dto.TakingCertificateVo;
import com.lvpuhui.gic.wxapp.carbon.dto.TakingVo;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonBillDO;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonBillDetailDO;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelDO;
import com.lvpuhui.gic.wxapp.carbon.enums.BillReceiveStatus;
import com.lvpuhui.gic.wxapp.carbon.enums.BillWithdrawProcessStatus;
import com.lvpuhui.gic.wxapp.carbon.service.CarbonBillService;
import com.lvpuhui.gic.wxapp.carbon.utils.IdRandomUtils;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 提现service实现
 * <AUTHOR>
 * @since 2023年03月27日 18:35:00
 */
@Slf4j
@Service
public class CarbonBillServiceImpl implements CarbonBillService {

    @Resource
    private GlCarbonModelIncomeDao glCarbonModelIncomeDao;

    @Resource
    private GlCarbonBillDao glCarbonBillDao;

    @Resource
    private GlCarbonModelDao glCarbonModelDao;

    @Resource
    private GlCarbonBillDetailDao glCarbonBillDetailDao;

    @Resource
    private GlCarbonModelIncomeDetailDao glCarbonModelIncomeDetailDao;

    @Override
    public IncomeTakingVo incomeTaking() {
        Long userId = UserUtils.getUserId();
        IncomeTakingVo incomeTakingVo = glCarbonModelIncomeDao.selectBillAmount(userId);
        if(Objects.isNull(incomeTakingVo)){
            incomeTakingVo = new IncomeTakingVo();
        }
        BigDecimal unTakingAmount = glCarbonModelIncomeDetailDao.getUnTakingAmountByUserId(userId);
        if(Objects.nonNull(unTakingAmount)){
            incomeTakingVo.setNotTakingAmount(unTakingAmount);
        }

        LambdaQueryWrapper<GlCarbonBillDO> glCarbonBillDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        glCarbonBillDOLambdaQueryWrapper.eq(GlCarbonBillDO::getBillDate, String.valueOf(DateUtil.thisYear()));
        glCarbonBillDOLambdaQueryWrapper.last("limit 1");
        GlCarbonBillDO glCarbonBillDO = glCarbonBillDao.selectOne(glCarbonBillDOLambdaQueryWrapper);
        if(Objects.nonNull(glCarbonBillDO)){
            LocalDateTime transferWithdrawDate = glCarbonBillDO.getTransferWithdrawDate();
            incomeTakingVo.setTakingDate(LocalDateTimeUtil.format(transferWithdrawDate, DatePattern.CHINESE_DATE_PATTERN));
        }else {
            incomeTakingVo.setTakingDate("本年年度提现账单暂未创建,请等待");
        }
        return incomeTakingVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TakingVo taking() {
        LambdaQueryWrapper<GlCarbonBillDO> glCarbonBillDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        glCarbonBillDOLambdaQueryWrapper.eq(GlCarbonBillDO::getBillDate, String.valueOf(DateUtil.thisYear()));
        glCarbonBillDOLambdaQueryWrapper.last("limit 1");
        GlCarbonBillDO glCarbonBillDO = glCarbonBillDao.selectOne(glCarbonBillDOLambdaQueryWrapper);
        if(Objects.isNull(glCarbonBillDO)){
            throw new GicWxAppException("本年年度提现账单暂未创建,请等待");
        }
        Long userId = UserUtils.getUserId();
        IncomeTakingVo incomeTakingVo = glCarbonModelIncomeDao.selectBillAmount(userId);
        if(Objects.isNull(incomeTakingVo) || Objects.isNull(incomeTakingVo.getAmount()) || BigDecimal.ZERO.compareTo(incomeTakingVo.getAmount()) >= 0){
            throw new GicWxAppException("您暂无可提现的金额");
        }
        glCarbonBillDao.updateBillAmount(glCarbonBillDO.getId(),incomeTakingVo.getAmount());
        LambdaQueryWrapper<GlCarbonModelDO> glCarbonModelDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        glCarbonModelDOLambdaQueryWrapper.eq(GlCarbonModelDO::getUserId,userId);
        glCarbonModelDOLambdaQueryWrapper.last("limit 1");
        GlCarbonModelDO glCarbonModelDO = glCarbonModelDao.selectOne(glCarbonModelDOLambdaQueryWrapper);

        GlCarbonBillDetailDO glCarbonBillDetailDO = new GlCarbonBillDetailDO();
        glCarbonBillDetailDO.setBillId(glCarbonBillDO.getId());
        glCarbonBillDetailDO.setWithdrawVerifyCode(IdRandomUtils.getShortId());
        glCarbonBillDetailDO.setUserId(userId);
        if(Objects.nonNull(glCarbonModelDO)){
            glCarbonBillDetailDO.setUserName(glCarbonModelDO.getMobile());
        }
        glCarbonBillDetailDO.setUserMobile(UserUtils.getUser().getMobile());
        glCarbonBillDetailDO.setAmount(incomeTakingVo.getAmount());
        glCarbonBillDetailDO.setWithdrawTime(LocalDateTime.now());
        glCarbonBillDetailDO.setStatus(BillReceiveStatus.UN_RECEIVE.getCode());
        glCarbonBillDetailDO.setWithdrawProcessStatus(BillWithdrawProcessStatus.UN_PROCESS.getCode());
        glCarbonBillDetailDO.setCreated(LocalDateTime.now());
        glCarbonBillDetailDO.setUpdated(LocalDateTime.now());
        glCarbonBillDetailDO.setCreator(UserUtils.getUserId());
        glCarbonBillDetailDO.setUpdator(UserUtils.getUserId());
        glCarbonBillDetailDao.insert(glCarbonBillDetailDO);

        glCarbonModelIncomeDao.updateTakingAmount(userId);

        TakingVo takingVo = new TakingVo();
        BeanUtil.copyProperties(glCarbonBillDetailDO,takingVo);
        if(PhoneUtil.isMobile(takingVo.getUserName())){
            takingVo.setUserName(StrUtil.hide(takingVo.getUserName(),3,7));
        }

        StringBuilder remarksBuilder = new StringBuilder();
        remarksBuilder
                .append("请在")
                .append(LocalDateTimeUtil.format(glCarbonBillDO.getReceiveStartTime(),DatePattern.CHINESE_DATE_PATTERN));
        if(Objects.nonNull(glCarbonBillDO.getReceiveEndTime())){
            remarksBuilder
                    .append("至")
                    .append(LocalDateTimeUtil.format(glCarbonBillDO.getReceiveEndTime(),DatePattern.CHINESE_DATE_PATTERN))
                    .append("之间");
        }else {
            remarksBuilder.append("之后");
        }
        remarksBuilder.append("$");
        remarksBuilder.append(glCarbonBillDO.getReceiveMaterial()).append("$").append("到")
                        .append(glCarbonBillDO.getReceiveAddress())
                        .append("领取提现");
        takingVo.setRemarks(remarksBuilder.toString());
        takingVo.setId(glCarbonBillDetailDO.getId());
        return takingVo;
    }

    @Override
    public List<TakingCertificateVo> takingCertificate() {
        Long userId = UserUtils.getUserId();
        List<TakingCertificateVo> takingCertificateVos = glCarbonBillDetailDao.selectBillDetailByUserId(userId);
        if(CollUtil.isNotEmpty(takingCertificateVos)){
            takingCertificateVos.forEach(takingCertificateVo -> {
                if(PhoneUtil.isMobile(takingCertificateVo.getUserName())){
                    takingCertificateVo.setUserName(StrUtil.hide(takingCertificateVo.getUserName(),3,7));
                }
                if(BillReceiveStatus.RECEIVE.getCode().equals(takingCertificateVo.getStatus())){
                    takingCertificateVo.setRemarks(LocalDateTimeUtil.format(takingCertificateVo.getReceiveTime(),DatePattern.CHINESE_DATE_PATTERN) + " 领取");
                }
                if(BillReceiveStatus.UN_RECEIVE.getCode().equals(takingCertificateVo.getStatus())
                        && Objects.nonNull(takingCertificateVo.getReceiveEndTime())){
                    String remarks = "请在" + LocalDateTimeUtil.format(takingCertificateVo.getReceiveEndTime(),DatePattern.CHINESE_DATE_PATTERN) + "前领取";
                    takingCertificateVo.setRemarks(remarks);
                }
            });
        }
        return takingCertificateVos;
    }

    @Override
    public TakingVo takingDetail(Long id) {
        TakingVo takingVo = new TakingVo();

        GlCarbonBillDetailDO glCarbonBillDetailDO = glCarbonBillDetailDao.selectById(id);
        if(Objects.isNull(glCarbonBillDetailDO)){
            return takingVo;
        }
        GlCarbonBillDO glCarbonBillDO = glCarbonBillDao.selectById(glCarbonBillDetailDO.getBillId());
        BeanUtil.copyProperties(glCarbonBillDetailDO,takingVo);
        if(PhoneUtil.isMobile(takingVo.getUserName())){
            takingVo.setUserName(StrUtil.hide(takingVo.getUserName(),3,7));
        }
        StringBuilder remarksBuilder = new StringBuilder();
        remarksBuilder
                .append("请在")
                .append(LocalDateTimeUtil.format(glCarbonBillDO.getReceiveStartTime(),DatePattern.CHINESE_DATE_PATTERN));
        if(Objects.nonNull(glCarbonBillDO.getReceiveEndTime())){
            remarksBuilder
                    .append("至")
                    .append(LocalDateTimeUtil.format(glCarbonBillDO.getReceiveEndTime(),DatePattern.CHINESE_DATE_PATTERN))
                    .append("之间");
        }else {
            remarksBuilder.append("之后");
        }
        remarksBuilder.append("$");
        remarksBuilder.append(glCarbonBillDO.getReceiveMaterial()).append("$").append("到")
                .append(glCarbonBillDO.getReceiveAddress())
                .append("领取提现");
        takingVo.setRemarks(remarksBuilder.toString());
        return takingVo;
    }
}
