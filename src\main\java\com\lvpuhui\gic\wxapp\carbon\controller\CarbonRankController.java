package com.lvpuhui.gic.wxapp.carbon.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbon.dto.CarbonRankVo;
import com.lvpuhui.gic.wxapp.carbon.service.CarbonRankService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tags(value = {@Tag(name = "碳汇排行1.0")})
@RestController
@RequestMapping("")
public class CarbonRankController {

    @Resource
    private CarbonRankService carbonRankService;

    @Operation(summary = "碳汇排行榜接口", description = "碳汇排行榜接口", tags = { "碳汇排行1.0" })
    @GetMapping("/carbon_rank")
    public R<CarbonRankVo> carbonRank() {
        CarbonRankVo carbonRankVo = carbonRankService.carbonRank();
        return R.ok(carbonRankVo);
    }

    @PassToken
    @Operation(summary = "首页碳汇排行榜接口", description = "首页碳汇排行榜接口", tags = { "碳汇排行1.0" })
    @GetMapping("/index_carbon_rank")
    public R<CarbonRankVo> indexCarbonRank() {
        CarbonRankVo carbonRankVo = carbonRankService.indexCarbonRank();
        return R.ok(carbonRankVo);
    }
}
