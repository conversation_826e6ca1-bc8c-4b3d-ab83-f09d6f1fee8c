package com.lvpuhui.gic.wxapp.carbon.service;

import com.lvpuhui.gic.wxapp.carbon.dto.IncomeTakingVo;
import com.lvpuhui.gic.wxapp.carbon.dto.TakingCertificateVo;
import com.lvpuhui.gic.wxapp.carbon.dto.TakingVo;

import java.util.List;

/**
 * 提现service
 * <AUTHOR>
 * @since 2023年03月27日 18:34:00
 */
public interface CarbonBillService {

    /**
     * 收入提现接口
     */
    IncomeTakingVo incomeTaking();

    /**
     * 提现接口
     */
    TakingVo taking();

    /**
     * 提现凭证列表接口
     */
    List<TakingCertificateVo> takingCertificate();

    /**
     * 提现凭证详情接口
     * @param id 凭证ID
     */
    TakingVo takingDetail(Long id);
}
