package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* 主体类型表
*/
@Schema(description="主体类型表")
@Data
@TableName(value = "gl_purchase_subject")
public class GlPurchaseSubjectDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

    /**
     * 主体类型
     */
    @TableField(value = "`name`")
    @Schema(description="主体类型")
    private String name;

    /**
     * 主体描述
     */
    @TableField(value = "description")
    @Schema(description="主体描述")
    private String description;

    /**
     * 主体排序
     */
    @TableField(value = "`rank`")
    @Schema(description="主体排序")
    private Integer rank;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    @Schema(description="是否删除")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private LocalDateTime created;

    /**
     * 修改时间
     */
    @TableField(value = "updated")
    @Schema(description="修改时间")
    private LocalDateTime updated;
}