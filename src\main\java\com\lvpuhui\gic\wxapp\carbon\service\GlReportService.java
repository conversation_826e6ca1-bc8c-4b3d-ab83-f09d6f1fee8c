package com.lvpuhui.gic.wxapp.carbon.service;

import com.lvpuhui.gic.wxapp.carbon.dto.ReportInfoDto;
import com.lvpuhui.gic.wxapp.carbon.dto.ReportInfoValidVo;
import com.lvpuhui.gic.wxapp.carbon.dto.ReportSubmitDto;
import com.lvpuhui.gic.wxapp.carbon.dto.SuperviseListVo;
import com.lvpuhui.gic.wxapp.carbon.entity.GlReportDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.LinkedHashMap;
import java.util.List;


/**
 * 举报表 服务类
 * <AUTHOR>
 * @since 2023-09-12
 */
public interface GlReportService extends IService<GlReportDO> {

    /**
     * 举报信息填写校验接口
     */
    ReportInfoValidVo reportInfoValid();

    /**
     * 举报信息填写提交接口
     */
    void reportInfo(ReportInfoDto reportInfoDto);

    /**
     * 举报提交接口
     */
    void reportSubmit(ReportSubmitDto reportSubmitDto);

    /**
     * 监督管理列表
     * @return
     */
    LinkedHashMap<String, List<SuperviseListVo>> superviseList();
}
