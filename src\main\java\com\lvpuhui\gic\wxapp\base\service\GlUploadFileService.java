package com.lvpuhui.gic.wxapp.base.service;

import com.lvpuhui.gic.wxapp.base.dto.AttachmentVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023年03月17日 14:56:00
 */
public interface GlUploadFileService {

    AttachmentVo upload(MultipartFile file,boolean contentCheck);

    void download(String fileName, HttpServletResponse response);

    /**
     * 把文件标记成使用
     * @param filenames 文件ID集合
     */
    void useFile(List<String> filenames);
}
