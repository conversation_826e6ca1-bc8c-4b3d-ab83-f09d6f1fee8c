package com.lvpuhui.gic.wxapp.carbon.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CarbonManagerStatus {

    WAIT_AUDIT(0,"待审核"),
    PASS(1,"正常"),
    NO_PASS(2,"审核未通过"),
    YEAR_AUDIT(3,"年审中"),
    STOP(4,"停用"),
    ;

    private Integer code;

    private String describe;

    CarbonManagerStatus(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
