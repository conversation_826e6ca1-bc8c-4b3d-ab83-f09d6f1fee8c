<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.activety.dao.GlActivityDao">

    <select id="queryAllActivityList" resultType="com.lvpuhui.gic.wxapp.activety.dto.ActivityListVo">
        select * from gl_activity where status = 1 and activity_end_time > #{startTime} order by activity_end_time desc
    </select>
    <select id="selectByUserId" resultType="com.lvpuhui.gic.wxapp.activety.entity.GlActivity">
        select * from gl_activity where creator = #{userId}
    </select>

</mapper>

