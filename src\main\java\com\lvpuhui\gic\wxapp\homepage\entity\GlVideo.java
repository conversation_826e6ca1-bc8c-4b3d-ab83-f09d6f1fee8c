package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 视频表实体类(gl_video)
 * <AUTHOR>
 * @since 2022-06-01
 */
@Data
public class GlVideo extends Model<GlVideo> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 场景企业名称
     */
    private String companyName;

    /**
     * 场景企业ID
     */
    private Long companyId;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 位置权重
     */
    private Integer sequence;

    /**
     * 视频号ID
     */
    private String videoChannelsId;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 看完时长判定(单位:秒)
     */
    private Integer finishDuration;

    /**
     * 看完视频积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 发布状态(0：保存  1：发布)
     */
    private Integer state;

    /**
     * 发布时间
     */
    private Date published;

    /**
     * 观看人数
     */
    private Integer watchCount;

    /**
     * 视频主题ID
     */
    private Long topicId;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private Date updated;

    /**
     * 修改者ID
     */
    private Long updator;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;
}
