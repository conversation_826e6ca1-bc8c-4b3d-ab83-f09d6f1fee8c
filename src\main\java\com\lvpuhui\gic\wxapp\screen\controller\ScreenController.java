package com.lvpuhui.gic.wxapp.screen.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbonbook.dto.CarbonBooksNewVo;
import com.lvpuhui.gic.wxapp.homepage.dto.RankAccumulateList;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenAccumulateRankDto;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenCarbonBooksMobileDto;
import com.lvpuhui.gic.wxapp.screen.service.ScreenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 屏幕相关接口控制器
 * <AUTHOR>
 */
@Tags(value = {@Tag(name = "屏幕接口1.0")})
@RestController
@RequestMapping("")
public class ScreenController {

    @Resource
    private ScreenService screenService;

    /**
     * 首页减排量统计接口
     */
    @PassToken
    @GetMapping("/screen_emission")
    public R getEmission(){
        return screenService.getEmission();
    }

    /**
     * 累积排行接口
     */
    @PostMapping("/screen_accumulate_rank")
    public R<RankAccumulateList> accumulateRank(@RequestBody @Valid ScreenAccumulateRankDto screenAccumulateRankDto){
        RankAccumulateList rankAccumulateList = screenService.accumulateRank(screenAccumulateRankDto);
        return R.ok(rankAccumulateList);
    }

    /**
     * 碳账本接口
     */
    @GetMapping("/screen_carbon_books_mobile")
    public R<CarbonBooksNewVo> carbonBooksMobile(@Valid ScreenCarbonBooksMobileDto screenCarbonBooksMobileDto){
        CarbonBooksNewVo carbonBooksNewVo = screenService.carbonBooksMobile(screenCarbonBooksMobileDto);
        return R.ok(carbonBooksNewVo);
    }
}
