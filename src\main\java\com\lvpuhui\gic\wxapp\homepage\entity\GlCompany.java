package com.lvpuhui.gic.wxapp.homepage.entity;

import java.util.Date;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 企业表(GlCompany)表实体类
 *
 * <AUTHOR>
 * @since 2022-05-06 14:41:15
 */
@Data
@SuppressWarnings("serial")
public class GlCompany extends Model<GlCompany> {
  @TableId(type = IdType.AUTO)
    /**企业id*/
    private Long id;
    /**logo*/
    private String logo;
    /**企业名称*/
    private String name;
    /**租户id*/
    private Long tenantId;
    /**创建时间*/
    private Date created;
    /**创建者ID*/
    private Long creator;
    /**更新时间*/
    private Date updated;
    /**修改者ID*/
    private Long updator;
    /**删除 0未删除 1已删除*/
    private Integer deleted;

}