package com.lvpuhui.gic.wxapp.carbon.enums;

import lombok.Getter;

/**
 * 个人提现处理状态 0：未处理 1：已处理-用于记录是否已经处理过model_income_detail表的taking状态
 * <AUTHOR>
 * @since 2023年03月22日 17:26:00
 */
@Getter
public enum BillWithdrawProcessStatus {

    UN_PROCESS(0,"未处理"),
    PROCESS(1,"已处理"),
    ;

    private Integer code;

    private String describe;

    BillWithdrawProcessStatus(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
