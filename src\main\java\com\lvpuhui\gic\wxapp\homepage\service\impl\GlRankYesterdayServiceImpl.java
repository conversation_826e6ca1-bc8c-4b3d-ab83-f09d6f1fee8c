package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.pointexchange.dao.GlGoodsDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankYesterdayDao;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.pointexchange.entity.GlGoods;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRankYesterday;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.homepage.dto.RankConfigDto;
import com.lvpuhui.gic.wxapp.homepage.dto.RankYesterdayListDto;
import com.lvpuhui.gic.wxapp.homepage.dto.RankYesterdayList;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.service.GlRankYesterdayService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 昨日排行榜服务实现类
 * <AUTHOR>
 * @since 2022-07-25
 */
@Service
public class GlRankYesterdayServiceImpl extends ServiceImpl<GlRankYesterdayDao, GlRankYesterday> implements GlRankYesterdayService {

    @Resource
    private GlGoodsDao glGoodsDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Resource
    private GlUserDao glUserDao;

    @Override
    public RankYesterdayList yesterdayRank(RankYesterdayListDto rankYesterdayListDto) {
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,rankYesterdayListDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }

        RankYesterdayList rankYesterdayList = new RankYesterdayList();

        RankConfigDto rankConfig = glAppletConfigService.getRankConfig();
        if(Objects.nonNull(rankConfig)
                && Objects.nonNull(rankConfig.getYesterday())
                && StrUtil.isNotBlank(rankConfig.getYesterday().getCommodityId())){
            // 查询可以领取的奖品
            Long goodsId = Long.parseLong(rankConfig.getYesterday().getCommodityId());
            GlGoods glGoods = glGoodsDao.selectById(goodsId);
            if(Objects.nonNull(glGoods)){
                rankYesterdayList.setGoodsName(glGoods.getName());
            }
        }
        String endDate = rankConfig.getYesterday().getEndTime();
        DateTime dateTime = DateUtil.parseDateTime(endDate);
        rankYesterdayList.setIsEnd(false);
        if(DateUtil.date().isAfter(dateTime)){
            rankYesterdayList.setIsEnd(true);
        }
        rankYesterdayList.setDays(rankConfig.getYesterday().getDays());
        // 获取前20昨日排行
        List<RankYesterdayList.YesterdayRankUser> yesterdayRankUsers = baseMapper.getYesterdayRankUserInfo();
        if(CollUtil.isEmpty(yesterdayRankUsers)){
            yesterdayRankUsers = Lists.newArrayList();
        }
        yesterdayRankUsers.forEach(yesterdayRankUser -> {
            if(rankYesterdayListDto.getMobileSha256().equals(yesterdayRankUser.getMobileSha256())){
                rankYesterdayList.setCurrentUser(yesterdayRankUser);
            }
        });
        rankYesterdayList.setYesterdayRankUsers(yesterdayRankUsers);
        if(Objects.isNull(rankYesterdayList.getCurrentUser())){
            // 查询当前用户的排行
            RankYesterdayList.YesterdayRankUser currentUser = baseMapper.getRankUserInfoBySha256(rankYesterdayListDto.getMobileSha256());
            if(Objects.nonNull(currentUser)){
                if(Objects.isNull(currentUser.getEmission()) || currentUser.getEmission() <= 0){
                    currentUser.setRank(null);
                }
            }
            rankYesterdayList.setCurrentUser(currentUser);
        }
        if(Objects.isNull(rankYesterdayList.getCurrentUser())){
            RankYesterdayList.YesterdayRankUser currentUser = new RankYesterdayList.YesterdayRankUser();
            currentUser.setMobileSha256(glUser.getMobileSha256());
            currentUser.setNickName(glUser.getNickName());
            rankYesterdayList.setCurrentUser(currentUser);
        }
        return rankYesterdayList;
    }
}
