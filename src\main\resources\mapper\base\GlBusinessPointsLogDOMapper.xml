<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.my.dao.GlBusinessPointsLogDao">

    <select id="selectByUserIdAndKeyAndCreated"
            resultType="com.lvpuhui.gic.wxapp.my.entity.GlBusinessPointsLogDO">
        select * from gl_business_points_log where user_id = #{userId} and business_key = #{key} and created = #{today}
    </select>
    <select id="queryUserReadPoints" resultType="java.lang.Integer">
        select sum(points) from gl_business_points_log where user_id = #{userId} and business_type = #{readType} and created = #{today}
    </select>
</mapper>
