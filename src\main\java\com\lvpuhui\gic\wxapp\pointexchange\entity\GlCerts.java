package com.lvpuhui.gic.wxapp.pointexchange.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 兑换凭证表(GlCerts)实体类
 * <AUTHOR>
 * @since 2022年5月5日 19:11:58
 */
@Data
public class GlCerts extends Model<GlCerts> {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 兑换码
     */
    private String code;

    /**
     * 是否已兑换(0：未兑换  1：已兑换)
     */
    private Integer isExchanged;

    /**
     * 导入批次号
     */
    private String batchNo;

    /**
     * 所属商品ID
     */
    private Long goodsId;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改时间
     */
    private Date updated;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;
}
