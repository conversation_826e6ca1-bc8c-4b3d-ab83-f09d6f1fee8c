package com.lvpuhui.gic.wxapp.carbon.enums;

import lombok.Getter;

/**
 * 累积统计类型枚举
 * <AUTHOR>
 * @since 2023年04月11日 11:01:00
 */
@Getter
public enum AccumulateStatisticsType {

    ACCUMULATE(0,"累积"),
    ONE_DAY(1,"每日"),
    ;

    private Integer code;

    private String describe;

    AccumulateStatisticsType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
