package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 碳汇统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_statistics")
public class GlCarbonStatisticsDO extends Model<GlCarbonStatisticsDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 累计收购减排量
     */
    private BigDecimal emission;

    /**
     * 累计应支付金额
     */
    private BigDecimal amount;

    /**
     * 实际已支出金额
     */
    private BigDecimal realityAmount;

    /**
     * 碳汇人数
     */
    private Integer carbonUserNumber;

    /**
     * 碳汇面积
     */
    private BigDecimal carbonArea;

    /**
     * 今年预计收益金额
     */
    private BigDecimal yearAmount;

    /**
     * 今年已确认提现金额
     */
    private BigDecimal yearTakingAmount;

    /**
     * 今年已提现金额
     */
    private BigDecimal yearWithdrawnAmount;

    /**
     * 累积收益金额
     */
    private BigDecimal accumulateAmount;

    /**
     * 碳汇减排次数
     */
    private Long carbonEmissionNumber;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
