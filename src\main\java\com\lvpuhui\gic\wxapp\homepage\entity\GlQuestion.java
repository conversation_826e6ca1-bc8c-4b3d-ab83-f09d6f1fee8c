package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 小程序问卷实体类(gl_question)
 * <AUTHOR>
 * @since 2022-06-13
 */
@Data
public class GlQuestion extends Model<GlQuestion> {

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 问卷名称
     */
    private String name;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 位置权重
     */
    private Integer sequence;

    /**
     * 问卷地址
     */
    private String jumpUrl;

    /**
     * 问卷id
     */
    private String questionId;

    /**
     * 问卷积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 状态 0草稿 1已发布 2删除
     */
    private Integer state;

    /**
     * 填写次数
     */
    private Integer writeCount;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date updated;
}
