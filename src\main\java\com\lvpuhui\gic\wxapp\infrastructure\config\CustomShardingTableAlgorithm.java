package com.lvpuhui.gic.wxapp.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;


/**
 * <AUTHOR>
 */
@Slf4j
public class CustomShardingTableAlgorithm implements PreciseShardingAlgorithm<String> {
    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {
        for (String tableName : availableTargetNames) {
            String index = shardingValue.getValue().substring(0,2).toLowerCase();
            if (tableName.equals(String.format("gl_points_%s",index))||
                    tableName.equals(String.format("gl_behavior_%s",index))||
                    tableName.equals(String.format("gl_consumption_%s",index))) {
//                log.info("【{}】 主键为:{}", tableName,shardingValue.getValue());
                return tableName;
            }
        }
        throw new IllegalArgumentException();
    }


}