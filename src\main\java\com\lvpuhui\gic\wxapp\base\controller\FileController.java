package com.lvpuhui.gic.wxapp.base.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.base.dto.AttachmentVo;
import com.lvpuhui.gic.wxapp.base.service.GlUploadFileService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Tags(value = {@Tag(name = "文件服务1.0")})
@RestController
@RequestMapping("/file")
public class FileController {

	@Resource
	private GlUploadFileService glUploadFileService;

	@PassToken
	@Operation(summary = "上传文件接口", description = "上传文件接口", tags = { "文件服务1.0" })
	@PostMapping("/upload")
	public R<AttachmentVo> upload(@RequestParam("file") @Schema(description = "文件对象") MultipartFile file) {
		AttachmentVo attachmentVo = glUploadFileService.upload(file,false);
		return R.ok(attachmentVo);
	}

	@Operation(summary = "上传文件-战队接口", description = "上传文件-战队接口", tags = { "文件服务1.0" })
	@PostMapping("/team_upload")
	public R<AttachmentVo> teamUpload(@RequestParam("file") @Schema(description = "文件对象") MultipartFile file) {
		AttachmentVo attachmentVo = glUploadFileService.upload(file,true);
		return R.ok(attachmentVo);
	}

	@PassToken
	@Operation(summary = "下载文件接口", description = "下载文件接口", tags = { "文件服务1.0" })
	@GetMapping("/download/**")
	public void download(HttpServletRequest request, HttpServletResponse response) {
		String fileName = getExtractPath(request);
		glUploadFileService.download(fileName,response);
	}

	private String getExtractPath(final HttpServletRequest request) {
		String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
		String bestMatchPattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
		return new AntPathMatcher().extractPathWithinPattern(bestMatchPattern, path);
	}
}
