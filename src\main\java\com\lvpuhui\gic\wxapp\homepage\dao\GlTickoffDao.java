package com.lvpuhui.gic.wxapp.homepage.dao;

import com.lvpuhui.gic.wxapp.homepage.dto.TickTypeNameDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlTickoff;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 打卡数据 (用户扫码) (GlTickoff)表数据库访问层
 * <AUTHOR>
 * @since 2022年5月5日 19:21:27
 */
public interface GlTickoffDao extends BaseMapper<GlTickoff> {

    /**
     * 查询打卡类型名称
     * @param tickOffIds 打卡ID集合
     */
    List<TickTypeNameDto> selectTickTypeName(@Param("tickOffIds") List<Long> tickOffIds);
}
