package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "提现凭证列表返回值")
public class TakingCertificateVo {

    /**
     * 提现凭证ID
     */
    @Schema(description = "提现凭证ID")
    private Long id;

    /**
     * 提现金额
     */
    @Schema(description = "提现金额")
    private BigDecimal amount;

    /**
     * 提现时间
     */
    @Schema(description = "提现时间")
    private LocalDateTime withdrawTime;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String userName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remarks;

    /**
     * 状态 0：未领取 1：已领取
     */
    @Schema(description = "状态 0：未领取 1：已领取")
    private Integer status;

    /**
     * 领取结束时间
     */
    @Schema(description = "领取结束时间",hidden = true)
    private LocalDateTime receiveEndTime;

    /**
     * 领取时间（发放时间）
     */
    @Schema(description = "领取时间（发放时间）",hidden = true)
    private LocalDateTime receiveTime;
}