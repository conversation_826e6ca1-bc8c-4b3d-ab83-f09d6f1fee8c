package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 打卡判断异常表
 * <AUTHOR>
 * @since 2022-08-22
 */
@Data
public class GlTickoffJudge extends Model<GlTickoffJudge> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id(sha256)
     */
    private String mobileSha256;

    /**
     * 最后一次打卡维度
     */
    private Double lastLatitude;

    /**
     * 最后一次打卡经度
     */
    private Double lastLongitude;

    /**
     * 上次打卡时间
     */
    private Date lastTime;

    /**
     * 打卡判断出异常的次数
     */
    private Integer errorNum;

    /**
     * 锁定时间
     */
    private Date lockTime;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date updated;
}
