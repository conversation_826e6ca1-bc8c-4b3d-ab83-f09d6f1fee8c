package com.lvpuhui.gic.wxapp.pointexchange.enums;

import lombok.Getter;

/**
 * 商品兑换类型枚举
 * <AUTHOR>
 * @since 2022年07月01日 18:25:00
 */
@Getter
public enum GoodsExchangeType {
    CERTS(0,"兑换码"),
    JUMP(1,"直接跳转"),
    ;

    private Integer code;

    private String describe;

    GoodsExchangeType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
