package com.lvpuhui.gic.wxapp.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.homepage.dto.RankConfigDto;
import com.lvpuhui.gic.wxapp.homepage.dto.RankGoodsConfigDto;

/**
 * 系统配置信息表(GlAppletConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-07 14:41:32
 */
public interface GlAppletConfigService extends IService<GlAppletConfig> {
    /**
     * 获取double类型的值
     * @param key
     * @return
     */
    double getDoubleValue(String key);

    /**
     * 获取配置表中 行为转积分
     * @return
     */
    double getBehaviorToPoints();

    /**
     * 获取积分
     * @param money
     * @return
     */
    double getPoints(double money);

    /**
     * 获取排行配置
     * @return
     */
    RankConfigDto getRankConfig();

    /**
     * 获取排行榜商品限制配置
     * @return
     */
    RankGoodsConfigDto getRankGoodsConfig();

    /**
     * 获取打卡锁定时长
     */
    Integer getTickLockDuration();

    /**
     * 获取打卡每公里必须的间隔时间
     */
    Integer getTickLimit();

    /**
     * 获取打卡最小距离 小于等于此距离的不做判断
     */
    Integer getTickMinDistance();

    /**
     * 获取图片前缀
     */
    String getImagePrefix();

    /**
     * 获取存储类型
     */
    String getStorageType();
}