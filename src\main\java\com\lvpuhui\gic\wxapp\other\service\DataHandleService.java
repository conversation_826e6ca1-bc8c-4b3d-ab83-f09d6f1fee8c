package com.lvpuhui.gic.wxapp.other.service;

import com.lvpuhui.gic.wxapp.other.dto.DataHandleDto;

/**
 * <AUTHOR>
 */
public interface DataHandleService {
    /**
     * 处理绿色行为
     * @param dto
     */
    long handleBehavior(DataHandleDto dto);
    /**
     * 处理积分
     * @param dto
     */
    long handlePoints(DataHandleDto dto);

    long countBehavior(String created);
    long countConsumption(String created);
    long countPoints(String created);
    /**
     * 执行webConsole的数据统计方法
     */
    void callStatisticsMethod();


}
