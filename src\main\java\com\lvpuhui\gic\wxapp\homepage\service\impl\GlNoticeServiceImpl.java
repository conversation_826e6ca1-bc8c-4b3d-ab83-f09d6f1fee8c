package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.homepage.entity.GlNotice;
import com.lvpuhui.gic.wxapp.homepage.dao.GlNoticeDao;
import com.lvpuhui.gic.wxapp.homepage.dto.NoticeDetailDto;
import com.lvpuhui.gic.wxapp.homepage.dto.NoticeDetail;
import com.lvpuhui.gic.wxapp.homepage.dto.NoticeList;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.homepage.enums.NoticeState;
import com.lvpuhui.gic.wxapp.homepage.service.GlNoticeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 公告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@Service
public class GlNoticeServiceImpl extends ServiceImpl<GlNoticeDao, GlNotice> implements GlNoticeService {

    @Override
    public List<NoticeList> noticeList() {
        LambdaQueryWrapper<GlNotice> glNoticeLambdaQueryWrapper = Wrappers.lambdaQuery();
        glNoticeLambdaQueryWrapper.eq(GlNotice::getDeleted, Deleted.UN_DELETE.getDeleted());
        glNoticeLambdaQueryWrapper.eq(GlNotice::getState, NoticeState.PUBLISH.getState());
        glNoticeLambdaQueryWrapper.orderByDesc(GlNotice::getId);
        List<GlNotice> glNotices = baseMapper.selectList(glNoticeLambdaQueryWrapper);
        if(CollUtil.isEmpty(glNotices)){
            return Lists.newLinkedList();
        }
        List<NoticeList> noticeLists = Lists.newArrayListWithCapacity(glNotices.size());
        for (GlNotice glNotice : glNotices) {
            NoticeList noticeList = new NoticeList();
            noticeList.setId(glNotice.getId());
            noticeList.setTitle(glNotice.getTitle());
            noticeLists.add(noticeList);
        }
        return noticeLists;
    }

    @Override
    public NoticeDetail noticeDetail(NoticeDetailDto noticeDetailDto) {
        LambdaQueryWrapper<GlNotice> glNoticeLambdaQueryWrapper = Wrappers.lambdaQuery();
        glNoticeLambdaQueryWrapper.eq(GlNotice::getDeleted, Deleted.UN_DELETE.getDeleted());
        glNoticeLambdaQueryWrapper.eq(GlNotice::getState, NoticeState.PUBLISH.getState());
        glNoticeLambdaQueryWrapper.eq(GlNotice::getId, noticeDetailDto.getId());
        GlNotice glNotice = baseMapper.selectOne(glNoticeLambdaQueryWrapper);
        if(Objects.isNull(glNotice)){
            return null;
        }
        Integer readCount = ObjectUtil.defaultIfNull(glNotice.getReadCount(),0) + 1;
        glNotice.setReadCount(readCount);
        baseMapper.updateById(glNotice);
        NoticeDetail noticeDetail = new NoticeDetail();
        BeanUtil.copyProperties(glNotice,noticeDetail);
        return noticeDetail;
    }
}
