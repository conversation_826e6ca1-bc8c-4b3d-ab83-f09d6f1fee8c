<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonModelDao">


    <select id="selectByUserId" resultType="com.lvpuhui.gic.wxapp.carbon.dto.CarbonModelMessgae">
        select
        a.name as modelName,
        a.id as modelId,
        a.status as `status`,
        b.amount as modelAmount,
        a.area as area
        from
        gl_carbon_model as a left join gl_carbon_model_income as b
        on a.id = b.model_id
        where a.user_id = #{userId}
    </select>
    <select id="selectAll" resultType="com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelDO">
        select * from gl_carbon_model where user_id = #{userId}
    </select>
    <select id="selectByArea" resultType="com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelDO">
        select * from gl_carbon_model where user_id = #{userId} and `status` in(1,3,4)
    </select>
    <select id="selectByProjectId" resultType="java.lang.Long">
        select count(*) from gl_carbon_model where project_id = #{projectId}
    </select>
    <select id="sumAreaByUser" resultType="java.math.BigDecimal">
        select sum(area) from gl_carbon_model where user_id = #{userId} and `status` in(1,3,4)
    </select>
    <select id="selectByUserIdTotalEmission" resultType="com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelDO">
        select * from gl_carbon_model where user_id = #{userId} and `status` in(1,3,4)
    </select>
</mapper>
