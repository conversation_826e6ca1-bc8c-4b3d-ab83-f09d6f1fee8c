package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GreenPointsDetailVo implements Serializable {
    private String productName;
    private Date soldTime;
    private Integer point;
    private String companyName;

    /**
     * 是否正负数  0:负数 1:正数
     */
    private Integer pointType;
}
