package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 账单明细表(每个人的账单）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_bill_detail")
public class GlCarbonBillDetailDO extends Model<GlCarbonBillDetailDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 提现验证码
     */
    private String withdrawVerifyCode;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户联系电话
     */
    private String userMobile;

    /**
     * 提现金额
     */
    private BigDecimal amount;

    /**
     * 提现时间
     */
    private LocalDateTime withdrawTime;

    /**
     * 提现处理状态 0：未处理 1：已处理-用于记录是否已经处理过model_income_detail表的taking状态
     */
    private Integer withdrawProcessStatus;

    /**
     * 领取时间（发放时间）
     */
    private LocalDateTime receiveTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 发放人ID
     */
    private Long grantor;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改者ID
     */
    private Long updator;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
