package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 视频主题表实体类(gl_video_topic)
 * <AUTHOR>
 * @since 2022-07-01
 */
@Data
public class GlVideoTopic extends Model<GlVideoTopic> {

    /**
     * 视频主题ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 名称
     */
    private String name;

    /**
     * 位置权重
     */
    private Integer sequence;

    /**
     * 发布状态(0：保存  1：发布)
     */
    private Integer state;

    /**
     * 发布时间
     */
    private Date published;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private Date updated;

    /**
     * 修改者ID
     */
    private Long updator;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;
}
