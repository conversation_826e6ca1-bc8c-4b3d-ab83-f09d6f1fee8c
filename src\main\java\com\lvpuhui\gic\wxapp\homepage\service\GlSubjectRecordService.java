package com.lvpuhui.gic.wxapp.homepage.service;

import com.lvpuhui.gic.wxapp.homepage.entity.GlSubjectRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectFinishDto;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectNumberDto;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectConfig;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectFinish;
import com.lvpuhui.gic.wxapp.homepage.dto.SubjectNumber;

/**
 * 答题记录表 服务类
 * <AUTHOR>
 * @since 2022-06-21
 */
public interface GlSubjectRecordService extends IService<GlSubjectRecord> {

    /**
     * 当天是否还有做题次数接口
     */
    SubjectNumber checkSubject(SubjectNumberDto subjectNumberDto);

    /**
     * 问答配置接口
     */
    SubjectConfig subjectConfig();

    /**
     * 问答完成接口
     */
    SubjectFinish subjectFinish(SubjectFinishDto subjectFinishDto);
}
