<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonModelEmissionDao">

    <select id="getEmissionSumGroupByProject" resultType="com.lvpuhui.gic.wxapp.carbon.dto.CarbonEmissionSumGroupByProjectDto">
        SELECT project_id,SUM(emission) AS emissionSum FROM gl_carbon_model_emission
        WHERE mobile_sha256 = #{mobileSha256} AND `status` = 0
        GROUP BY project_id
    </select>
</mapper>
