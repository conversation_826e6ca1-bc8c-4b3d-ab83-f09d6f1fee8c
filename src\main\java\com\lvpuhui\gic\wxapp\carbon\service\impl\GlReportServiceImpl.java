package com.lvpuhui.gic.wxapp.carbon.service.impl;

import cn.hutool.core.lang.Validator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lvpuhui.gic.wxapp.carbon.dao.*;
import com.lvpuhui.gic.wxapp.carbon.dto.ReportInfoDto;
import com.lvpuhui.gic.wxapp.carbon.dto.ReportInfoValidVo;
import com.lvpuhui.gic.wxapp.carbon.dto.ReportSubmitDto;
import com.lvpuhui.gic.wxapp.carbon.dto.SuperviseListVo;
import com.lvpuhui.gic.wxapp.carbon.entity.*;
import com.lvpuhui.gic.wxapp.carbon.enums.ReportStatus;
import com.lvpuhui.gic.wxapp.carbon.service.GlReportService;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.other.utils.AES256Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 举报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Service
public class GlReportServiceImpl extends ServiceImpl<GlReportDao, GlReportDO> implements GlReportService {

    @Resource
    private GlReportMediaDao glReportMediaDao;

    @Resource
    private GlReportUserInfoDao glReportUserInfoDao;

    @Resource
    private AES256Util aes256Util;

    @Resource
    private GlReportSuperviseCategoryMapper glReportSuperviseCategoryMapper;


    @Resource
    private GlReportSuperviseProjectMapper glReportSuperviseProjectMapper;

    @Override
    public ReportInfoValidVo reportInfoValid() {
        ReportInfoValidVo reportInfoValidVo = new ReportInfoValidVo();
        reportInfoValidVo.setReportWriteValid(false);
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlReportUserInfoDO> glReportUserInfoDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        glReportUserInfoDOLambdaQueryWrapper.eq(GlReportUserInfoDO::getUserId,userId);
        glReportUserInfoDOLambdaQueryWrapper.last("limit 1");
        GlReportUserInfoDO glReportUserInfoDO = glReportUserInfoDao.selectOne(glReportUserInfoDOLambdaQueryWrapper);
        if(Objects.isNull(glReportUserInfoDO)){
            return reportInfoValidVo;
        }
        if(StringUtils.isBlank(glReportUserInfoDO.getFullName()) || StringUtils.isBlank(glReportUserInfoDO.getIdCard())){
            return reportInfoValidVo;
        }
        reportInfoValidVo.setReportWriteValid(true);
        return reportInfoValidVo;
    }

    @Override
    public void reportInfo(ReportInfoDto reportInfoDto) {
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlReportUserInfoDO> glReportUserInfoDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        glReportUserInfoDOLambdaQueryWrapper.eq(GlReportUserInfoDO::getUserId,userId);
        glReportUserInfoDOLambdaQueryWrapper.last("limit 1");
        GlReportUserInfoDO glReportUserInfoDO = glReportUserInfoDao.selectOne(glReportUserInfoDOLambdaQueryWrapper);
        if(Objects.nonNull(glReportUserInfoDO)){
            throw new GicWxAppException("您已填写过个人举报信息");
        }
        if(!Validator.isCitizenId(reportInfoDto.getIDCard())){
            throw new GicWxAppException("请填写有效的身份证号");
        }
        GlReportUserInfoDO reportUserInfoDO = new GlReportUserInfoDO();
        reportUserInfoDO.setUserId(userId);
        reportUserInfoDO.setFullName(reportInfoDto.getFullName());
        reportUserInfoDO.setIdCard(aes256Util.encrypt(reportInfoDto.getIDCard()));
        reportUserInfoDO.setMobile(UserUtils.getUser().getMobile());
        glReportUserInfoDao.insert(reportUserInfoDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportSubmit(ReportSubmitDto reportSubmitDto) {
        Long userId = UserUtils.getUserId();
        LambdaQueryWrapper<GlReportUserInfoDO> glReportUserInfoDOLambdaQueryWrapper = Wrappers.lambdaQuery();
        glReportUserInfoDOLambdaQueryWrapper.eq(GlReportUserInfoDO::getUserId,userId);
        glReportUserInfoDOLambdaQueryWrapper.last("limit 1");
        GlReportUserInfoDO glReportUserInfoDO = glReportUserInfoDao.selectOne(glReportUserInfoDOLambdaQueryWrapper);
        if(Objects.isNull(glReportUserInfoDO)){
            throw new GicWxAppException("请您先填写个人信息");
        }
        if(StringUtils.isBlank(glReportUserInfoDO.getFullName()) || StringUtils.isBlank(glReportUserInfoDO.getIdCard())){
            throw new GicWxAppException("请您先填写完整个人信息");
        }
        GlReportDO glReportDO = new GlReportDO();
        glReportDO.setReportType(reportSubmitDto.getReportType());
        glReportDO.setReportLongitude(reportSubmitDto.getReportLongitude());
        glReportDO.setReportLatitude(reportSubmitDto.getReportLatitude());
        glReportDO.setStatus(ReportStatus.UNTREATED.getCode());
        glReportDO.setReportTime(LocalDateTime.now());
        glReportDO.setCreated(LocalDateTime.now());
        glReportDO.setReportUserId(userId);
        glReportDO.setUpdated(LocalDateTime.now());
        glReportDO.setUpdator(userId);
        baseMapper.insert(glReportDO);

        List<GlReportMediaDO> glReportMediaDOS = reportSubmitDto.getSubmitResourceDtos().stream()
                .map(reportSubmitResourceDto -> {
                    GlReportMediaDO glReportMediaDO = new GlReportMediaDO();
                    glReportMediaDO.setReportId(glReportDO.getId());
                    glReportMediaDO.setMediaType(reportSubmitResourceDto.getMediaType());
                    glReportMediaDO.setMediaUrl(reportSubmitResourceDto.getMediaUrl());
                    glReportMediaDO.setCreated(LocalDateTime.now());
                    glReportMediaDO.setUpdated(LocalDateTime.now());
                    return glReportMediaDO;
                }).collect(Collectors.toList());
        glReportMediaDao.insertList(glReportMediaDOS);
    }

    @Override
    public LinkedHashMap<String, List<SuperviseListVo>> superviseList() {
        LinkedHashMap<String, List<SuperviseListVo>> map = new LinkedHashMap<>();
        LambdaQueryWrapper<GlReportSuperviseCategoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(GlReportSuperviseCategoryDO :: getDeleted,0);
        queryWrapper.orderByDesc(GlReportSuperviseCategoryDO :: getRank);
        List<GlReportSuperviseCategoryDO> list = glReportSuperviseCategoryMapper.selectList(queryWrapper);
        for (GlReportSuperviseCategoryDO item :list) {
            LambdaQueryWrapper<GlReportSuperviseProjectDO> queryProjectWrapper = Wrappers.lambdaQuery();
            queryProjectWrapper.eq(GlReportSuperviseProjectDO :: getSuperviseId,item.getId());
            queryProjectWrapper.eq(GlReportSuperviseProjectDO :: getDeleted,0);
            queryProjectWrapper.orderByDesc(GlReportSuperviseProjectDO :: getRank);
            List<GlReportSuperviseProjectDO> projectDOList  = glReportSuperviseProjectMapper.selectList(queryProjectWrapper);
            List<SuperviseListVo> superviseListVoList = new ArrayList<>();
            for (GlReportSuperviseProjectDO projectItem: projectDOList) {
                SuperviseListVo superviseListVo = new SuperviseListVo();
                superviseListVo.setId(projectItem.getId());
                superviseListVo.setLogo(projectItem.getLogo());
                superviseListVo.setName(projectItem.getName());
                superviseListVo.setRank(projectItem.getRank());
                superviseListVo.setSuperViseId(projectItem.getSuperviseId());
                superviseListVoList.add(superviseListVo);
            }
            map.put(item.getName(),superviseListVoList);
        }
        return map;
    }
}
