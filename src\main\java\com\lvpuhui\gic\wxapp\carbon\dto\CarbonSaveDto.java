package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "开发碳汇")
public class CarbonSaveDto {

    private String name;

    private String card;

    private Long projectId;

    private BigDecimal area;

    private String address;

    private List<String> url;
}
