package com.lvpuhui.gic.wxapp.my.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 换绑记录表(GlChangeBind)表实体类
 * <AUTHOR>
 * @since 2023年3月2日17:16:12
 */
@Data
public class GlChangeBind extends Model<GlChangeBind> {

    /**
     * 换绑记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 原手机号码
     */
    private String originalMobile;

    /**
     * 原手机号sha256
     */
    private String originalMobileSha256;

    /**
     * 新手机号码
     */
    private String mobile;

    /**
     * 新手机号sha256
     */
    private String mobileSha256;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 用户id
     */
    private Long userId;
}