package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 排行用户信息vo
 * <AUTHOR>
 * @since 2023年04月04日 14:02:00
 */
@Data
public class CarbonRankUserVo {

    /**
     * 手机号
     */
    @Schema(description = "手机号",hidden = true)
    private String mobileSha256;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickName;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatarUrl;

    /**
     * 预计今年收益
     */
    @Schema(description = "预计今年收益")
    private BigDecimal estimateAmountSum;

    /**
     * 排名
     */
    @Schema(description = "排名")
    private Integer rank;
}
