<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonModelIncomeDao">
    <update id="updateTakingAmount">
        UPDATE gl_carbon_model_income SET accumulate_taking_amount = accumulate_taking_amount + remain_amount,remain_amount = 0
        WHERE user_id = #{userId} AND remain_amount >= 0
    </update>

    <select id="selectBillAmount" resultType="com.lvpuhui.gic.wxapp.carbon.dto.IncomeTakingVo">
        SELECT SUM(remain_amount) AS amount FROM gl_carbon_model_income WHERE user_id = #{userId}
    </select>
    <select id="queryAllModel" resultType="java.math.BigDecimal">
        select sum(amount) from gl_carbon_model_income where user_id = #{userId};
    </select>
    <select id="sumData" resultType="com.lvpuhui.gic.wxapp.carbon.dto.CarbonManagrModel">
        select
        sum(amount) as modelAmount,
        sum(emission) as modelEmission,
        sum(accumulate_reward_amount) as rewardPunAmount
        from gl_carbon_model_income
        where user_id = #{userId}
    </select>
    <select id="selectByModelId" resultType="com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelIncomeDO">
        select * from gl_carbon_model_income where model_id = #{modelId}
    </select>
    <select id="sumAllAmount" resultType="com.lvpuhui.gic.wxapp.carbon.dto.CarbonModelAmount">
        select sum(amount) as amount,sum(accumulate_amount) as accumulateAmount from gl_carbon_model_income where user_id = #{userId}
    </select>
    <select id="queryAllRemainByUserId" resultType="java.math.BigDecimal">
        select sum(remain_amount) from gl_carbon_model_income where user_id = #{userId}
    </select>

</mapper>
