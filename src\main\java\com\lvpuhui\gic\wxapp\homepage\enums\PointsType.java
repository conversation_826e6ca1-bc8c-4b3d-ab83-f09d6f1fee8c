package com.lvpuhui.gic.wxapp.homepage.enums;

/**
 * <AUTHOR>
 */

public enum PointsType  {
    /**积分产生类型 1绿色消费 2绿色行为 3探店打卡 4绿色问卷 5积分兑换（负）*/
    CONSUMPTION(1,"购买"),
    BEHAVIOR(2,"绿色行为"),
    TICK_OFF(3,"探店打卡"),
    QUESTION(4,"绿色问卷"),
    EXCHANGE(5,"兑换"),
    VIDEO(6,"观看视频"),
    LV_BEHAVIOR(7,"绿享生活家电减排积分"),
    RECYCLE(8,"回收积分(负)"),
    COMPENSATION(9,"9补偿积分(正)"),
    SUBJECT(10,"答题领积分"),
    EMISSION_RECYCLE(11,"减排发放回收"),
    INVITE(12,"邀请"),
    CARBON(13,"碳汇积分"),
    TIP_OFF(14,"举报奖励"),
    READ_DAY(15,"用户阅读"),
    TREE_PLANTING(16,"用户阅读"),
    ;

    /**
     * 后缀 大写字母
     */
    private final Integer code;
    private final String msg;

    PointsType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getCode() {
        return code;
    }

    public static PointsType get(int code){
        for (PointsType pointsType : PointsType.values()) {
            if(pointsType.getCode().equals(code)){
                return pointsType;
            }
        }
        return null;
    }
}
