package com.lvpuhui.gic.wxapp.infrastructure.utils;

import cn.hutool.core.io.FileTypeUtil;

import java.io.InputStream;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class ImageUtils {
    // 定义常用图片后缀集合（统一用小写）
    private static final Set<String> IMAGE_EXTENSIONS = new HashSet<>(
            Arrays.asList("jpg", "jpeg", "png", "gif")
    );

    public static boolean isImageFile(InputStream inputStream) {
        String type = FileTypeUtil.getType(inputStream,false);
        return IMAGE_EXTENSIONS.contains(type);
    }
}
