<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonModelOperationDao">

    <select id="queryAllLogByModelId" resultType="com.lvpuhui.gic.wxapp.carbon.dto.OperationVo">
                select app_log,created,content,log,amount from gl_carbon_model_operation where model_id = #{modelId} order by created desc
    </select>
</mapper>
