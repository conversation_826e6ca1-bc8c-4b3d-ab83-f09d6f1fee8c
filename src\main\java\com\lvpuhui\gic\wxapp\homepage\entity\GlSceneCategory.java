package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 场景类别表(GlSceneCategory)实体类
 * <AUTHOR>
 * @since 2022年5月5日 19:05:49
 */
@Data
public class GlSceneCategory extends Model<GlSceneCategory> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类别名称
     */
    private String name;

    /**
     * 大类ID
     */
    private Integer bigCategoryId;

    /**
     * 大类名称
     */
    private String bigCategoryName;

    /**
     * logo图
     */
    private String logoImage;

    /**
     * 位置权重
     */
    private Integer sequence;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private Date updated;

    /**
     * 修改者ID
     */
    private Long updator;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;
}
