package com.lvpuhui.gic.wxapp.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023年07月27日 15:45:00
 */
@Data
@Schema(description = "战队认证申请Dto")
public class TeamCertificationApplyDto {

    /**
     * 组织名称
     */
    @Schema(description = "组织名称")
    @NotBlank(message = "请您输入组织名称")
    private String organizationName;

    /**
     * 营业执照照片URL
     */
    @Schema(description = "营业执照照片URL")
    @NotBlank(message = "请您上传营业执照")
    private String organizationLicense;

    /**
     * 申请人姓名
     */
    @Schema(description = "申请人姓名")
    private String applicant;

    /**
     * 申请人身份证号
     */
    @Schema(description = "申请人身份证号")
    private String applicantNumber;
}