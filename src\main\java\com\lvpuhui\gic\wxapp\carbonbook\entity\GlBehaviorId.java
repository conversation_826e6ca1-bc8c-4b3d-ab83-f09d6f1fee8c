package com.lvpuhui.gic.wxapp.carbonbook.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 绿色行为记录表ID表(GlBehaviorId)表实体类
 *
 * <AUTHOR>
 * @since 2022-07-13 17:08:14
 */
@Data
@Accessors(chain = true)
public class GlBehaviorId extends Model<GlBehaviorId> {
    /**
    * 自增id
    */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /** ${column.comment} */ 
    private String currentValue;
}


