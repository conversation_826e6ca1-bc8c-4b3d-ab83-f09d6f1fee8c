package com.lvpuhui.gic.wxapp.other.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 绿色消费记录ID表(GlConsumptionId)表实体类
 *
 * <AUTHOR>
 * @since 2022-07-13 17:08:56
 */
@Data
@Accessors(chain = true)
public class GlConsumptionId extends Model<GlConsumptionId> {
    /**
    * 自增id
    */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /** ${column.comment} */ 
    private String currentValue;
}


