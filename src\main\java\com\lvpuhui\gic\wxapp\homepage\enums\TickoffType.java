package com.lvpuhui.gic.wxapp.homepage.enums;

import lombok.Getter;

/**
 * 打卡类型状态枚举
 * <AUTHOR>
 * @since 2022年5月6日 19:06:32
 */
@Getter
public enum TickoffType {
    ONCE(0,"仅一次不可能重复"),
    EVERY_DAY(1,"每天"),

    // 目前每周和每月没有做
    EVERY_WEEK(2,"每周"),
    EVERY_MONTH(3,"每月"),
    ;

    private Integer type;

    private String describe;

    TickoffType(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }
}
