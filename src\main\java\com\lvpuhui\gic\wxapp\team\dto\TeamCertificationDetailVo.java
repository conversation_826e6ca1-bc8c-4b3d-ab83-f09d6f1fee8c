package com.lvpuhui.gic.wxapp.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "战队认证详情Vo")
public class TeamCertificationDetailVo {

    /**
     * 组织名称
     */
    @Schema(description = "组织名称")
    private String organizationName;

    /**
     * 营业执照照片URL
     */
    @Schema(description = "营业执照照片URL")
    private String organizationLicense;

    /**
     * 申请人姓名
     */
    @Schema(description = "申请人姓名")
    private String applicant;

    /**
     * 申请人身份证号
     */
    @Schema(description = "申请人身份证号")
    private String applicantNumber;

    /**
     * 审核状态 0：审核中  -1：审核不通过  1：已认证
     */
    @Schema(description = "审核状态 0：审核中  -1：审核不通过  1：已认证")
    private Integer status;
}