package com.lvpuhui.gic.wxapp.carbon.enums;

import lombok.Getter;

/**
 * 碳汇实例详情状态枚举
 * <AUTHOR>
 * @since 2023年03月22日 17:26:00
 */
@Getter
public enum IncomeDetailStatus {

    NOT_AVAILABLE(0,"不可用"),
    AVAILABLE(1,"可用"),
    ;

    private Integer code;

    private String describe;

    IncomeDetailStatus(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
