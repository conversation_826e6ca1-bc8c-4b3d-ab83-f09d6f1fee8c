package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 整体统计表实体类
 * <AUTHOR>
 * @since 2022-06-20
 */
@Data
public class GlStatistics extends Model<GlStatistics> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消费人数(人)
     */
    private Long customers;

    /**
     * 消费商品数(件)
     */
    private Long consumable;

    /**
     * 累积销售额(元)
     */
    private BigDecimal cumulativeSales;

    /**
     * 积分人数(人)
     */
    private Long pointsNumber;

    /**
     * 消费积分发放
     */
    private Long consumePoints;

    /**
     * 减排积分发放
     */
    private Long emissionPoints;

    /**
     * 任务发放积分
     */
    private Long jobPointSum;

    /**
     * 积分发放总量
     */
    private Long pointsSum;

    /**
     * 积分消耗总量
     */
    private Long consumePointsSum;

    /**
     * 当前积分余额
     */
    private Long pointsBalance;

    /**
     * 减排人数(人)
     */
    private Long emissionNumber;

    /**
     * 减排次数(次)
     */
    private Long emissionCount;

    /**
     * 减排量(吨)
     */
    private BigDecimal emission;

    /**
     * 能效家电减排量(吨)
     */
    private BigDecimal homeApplianceEmission;

    /**
     * 行为减排量(吨)
     */
    private BigDecimal behaviorEmission;

    /**
     * 已兑换人数(人)
     */
    private Long exchangeNumber;

    /**
     * 已兑换件数(件)
     */
    private Long inventoryConsumed;

    /**
     * 库存剩余件数(件)
     */
    private Long inventoryRemain;

    /**
     * 已兑换价值
     */
    private BigDecimal exchangePrice;

    /**
     * 库存剩余价值
     */
    private BigDecimal remainPrice;

    /**
     * 积分发放次数
     */
    private Long pointGrantNumber;

    /**
     * 使用积分人数
     */
    private Long pointUseNumber;

    /**
     * 使用次数
     */
    private Long useNumber;

    /**
     * 使用积分总数
     */
    private Long usePointSum;

    /**
     * 有余额人数
     */
    private Long haveBalanceNumber;
}
