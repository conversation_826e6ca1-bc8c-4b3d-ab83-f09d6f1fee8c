package com.lvpuhui.gic.wxapp.my.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.lvpuhui.gic.wxapp.my.entity.GlBusinessPointsLogDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
public interface GlBusinessPointsLogDao extends BaseMapper<GlBusinessPointsLogDO> {
    GlBusinessPointsLogDO selectByUserIdAndKeyAndCreated(@Param("userId") Long userId, @Param("key") String key, @Param("today") LocalDate today);

    Integer queryUserReadPoints(@Param("readType") int readType, @Param("today") LocalDate today, @Param("userId") Long userId);
}
