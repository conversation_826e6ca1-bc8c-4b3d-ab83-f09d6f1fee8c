package com.lvpuhui.gic.wxapp.homepage.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class QueryPurchaseSubjectListVo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

    /**
     * 主体类型
     */
    @TableField(value = "`name`")
    @Schema(description="主体类型")
    private String name;

    /**
     * 主体描述
     */
    @TableField(value = "description")
    @Schema(description="主体描述")
    private String description;

    /**
     * 主体排序
     */
    @TableField(value = "`rank`")
    @Schema(description="主体排序")
    private Integer rank;

}
