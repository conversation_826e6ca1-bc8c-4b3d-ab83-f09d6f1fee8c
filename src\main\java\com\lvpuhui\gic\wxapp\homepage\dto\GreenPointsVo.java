package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GreenPointsVo implements Serializable {
    /**剩余积分点数*/
    private Long pointRemain;
    /**消耗积分点数*/
    private Long pointConsumed;
    /**获取次数*/
    private Integer obtainTimes;
    /**使用次数*/
    private Integer exchangeTimes;
    /**总积分点数*/
    private Long point;
    /** 明细列表*/
    List<GreenPointsDetailVo> list;

}
