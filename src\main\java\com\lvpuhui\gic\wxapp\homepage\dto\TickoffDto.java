package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 打卡DTO
 * <AUTHOR>
 * @since 2022年05月07日 13:48:00
 */
@Data
@Accessors(chain = true)
public class TickoffDto {

    /**
     * 打卡类型ID
     */
    @NotNull(message = "请您选择打卡")
    private Long id;

    /**
     * 手机号-sha256加密
     */
    @NotBlank(message = "请您先进行授权")
    private String mobileSha256;

    /**
     * 维度
     */
    @NotNull(message = "位置信息不正确")
    private Double latitude;

    /**
     * 经度
     */
    @NotNull(message = "位置信息不正确")
    private Double longitude;
}
