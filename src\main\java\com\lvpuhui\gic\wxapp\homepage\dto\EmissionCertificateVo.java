package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class EmissionCertificateVo implements Serializable {
    private String emission;
    private Double emissionNum;
    private String title;
    private String certificateUrl;
    /**昵称*/
    private String nickName;
    /**头像*/
    private String avatarUrl;

    /**
     * 减排天数
     */
    private Long days;
}
