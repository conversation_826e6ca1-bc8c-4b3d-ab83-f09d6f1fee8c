package com.lvpuhui.gic.wxapp.homepage.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023年07月14日 10:04:00
 */
@Data
@Schema(description = "首页-我的碳账本数据返回参数")
public class MyCarbonBookDataVo {

    /**
     * 减排量
     */
    @Schema(description = "减排量")
    private String emissionText;

    /**
     * 剩余积分点数
     */
    @Schema(description = "碳积分")
    private Long pointRemain;

    /**
     * 减排排名
     */
    @Schema(description = "减排排名")
    private Long rank;

    /**
     * 打败了多少人的占比
     */
    @Schema(description = "打败了多少人的占比")
    private Double radio;
}
