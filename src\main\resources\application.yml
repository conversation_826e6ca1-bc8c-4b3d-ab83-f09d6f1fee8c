server:
  port: 9078
spring:
  jackson:
    time-zone: GMT+8
    dateFormat: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
      enabled: true
  profiles:
    active: @profile.active@ #此处由maven的环境选择决定
  # 开启循环依赖
  main:
    allow-circular-references: true
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  mapper-locations: classpath:/mapper/*/*Mapper.xml,classpath:/mapper/*/*Dao.xml
  type-aliases-package: com.lvpuhui.gic.wxapp.*.entity
  global-config:
    banner: false
logging:
  config: classpath:logback-springcloud.xml
  level:
    com.baomidou: info
scene-category:
  green-life: 25
  green-capital: 26