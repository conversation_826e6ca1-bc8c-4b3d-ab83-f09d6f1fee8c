package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
* 举报监督项
*/
@Schema(description="举报监督项")
@Data
@TableName(value = "gl_report_supervise_project")
public class GlReportSuperviseProjectDO {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键id")
    private Long id;

    /**
     * 监督类型id
     */
    @TableField(value = "supervise_id")
    @Schema(description="监督类型id")
    private Long superviseId;

    /**
     * 监督项名称
     */
    @TableField(value = "`name`")
    @Schema(description="监督项名称")
    private String name;

    /**
     * 监督项logo
     */
    @TableField(value = "logo")
    @Schema(description="监督项logo")
    private String logo;

    /**
     * 奖励积分
     */
    @TableField(value = "points")
    @Schema(description="奖励积分")
    private Long points;

    /**
     * 排序权重
     */
    @TableField(value = "`rank`")
    @Schema(description="排序权重")
    private Integer rank;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    private String description;

    /**
     * 删除状态
     */
    @TableField(value = "deleted")
    @Schema(description="删除状态")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private LocalDateTime created;

    @TableField(value = "updated")
    @Schema(description="")
    private LocalDateTime updated;
}