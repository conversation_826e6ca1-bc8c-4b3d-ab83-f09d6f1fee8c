package com.lvpuhui.gic.wxapp.homepage.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 企业产品表(GlCompanyProduct)表实体类
 *
 * <AUTHOR>
 * @since 2022-06-21 14:28:43
 */
@Data
@Accessors(chain = true)
public class GlCompanyProduct extends Model<GlCompanyProduct> {
    /**
    * 主键
    */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /** 产品名称 */ 
    private String name;
    /** 企业名称 */ 
    private String companyName;
    /** 企业ID */ 
    private Integer companyId;
    /** 场景类别ID */ 
    private Long categoryId;
    /** logo图 */ 
    private String logoImage;
    /** 位置权重 */ 
    private Integer sequence;
    /** 跳转地址 */ 
    private String jumpUrl;
    /** 创建时间 */ 
    private Date created;
    /** 创建者ID */ 
    private Long creator;
    /** 更新时间 */ 
    private Date updated;
    /** 修改者ID */ 
    private Long updator;
    /** 删除 0未删除 1已删除 */ 
    private Integer deleted;
    /** 小程序appid */ 
    private String appletAppid;
    /** 小程序跳转地址 */ 
    private String appletUrl;
    /** 说明 */
    private String productDescribe;
}


