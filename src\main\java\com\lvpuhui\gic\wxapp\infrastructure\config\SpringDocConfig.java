package com.lvpuhui.gic.wxapp.infrastructure.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * 文档Doc配置
 * <AUTHOR>
 * @date 2022/11/01 15:36
 **/
@Configuration
public class SpringDocConfig {
    @Bean
    @Profile("!prod")
    public OpenAPI springShopOpenAPI() {
        return new OpenAPI()
                .info(new Info().title("合肥小程序API")
                        .description("hefei wxapp application")
                        .version("v1.0.0"));
    }

    @Bean
    @Profile("!prod")
    public GroupedOpenApi userApi(){
        String[] paths = {"/**"};
        String[] packagedToMatch = { "com.lvpuhui.gic.wxapp" };
        return GroupedOpenApi.builder().group("")
                .pathsToMatch(paths)
                .packagesToScan(packagedToMatch).build();
    }
}
