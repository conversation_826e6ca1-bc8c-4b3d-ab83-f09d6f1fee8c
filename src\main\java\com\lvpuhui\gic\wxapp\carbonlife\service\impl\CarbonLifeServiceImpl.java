package com.lvpuhui.gic.wxapp.carbonlife.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorService;
import com.lvpuhui.gic.wxapp.carbonlife.dao.GlCarbonLifeSceneMapper;
import com.lvpuhui.gic.wxapp.carbonlife.dao.GlCarbonLifeTypeMapper;
import com.lvpuhui.gic.wxapp.carbonlife.dao.GlCompanyProductMapper;
import com.lvpuhui.gic.wxapp.carbonlife.dto.CarbonLifeVo;
import com.lvpuhui.gic.wxapp.carbonlife.entity.GlCarbonLifeSceneDO;
import com.lvpuhui.gic.wxapp.carbonlife.entity.GlCarbonLifeTypeDO;
import com.lvpuhui.gic.wxapp.carbonlife.entity.LifeCompanyProductDO;
import com.lvpuhui.gic.wxapp.carbonlife.service.CarbonLifeService;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023年10月30日 17:56:00
 */
@Service
public class CarbonLifeServiceImpl implements CarbonLifeService {

    @Resource
    private GlCarbonLifeTypeMapper glCarbonLifeTypeMapper;

    @Resource
    private GlCarbonLifeSceneMapper glCarbonLifeSceneMapper;

    @Resource
    private GlCompanyProductMapper glCompanyProductMapper;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Resource
    private GlBehaviorService glBehaviorService;

    @Override
    public List<CarbonLifeVo> carbonLife() {
        String imagePrefix = glAppletConfigService.getImagePrefix();
        LambdaQueryWrapper<GlCarbonLifeSceneDO> glCarbonLifeSceneLambdaQueryWrapper = Wrappers.lambdaQuery();
        glCarbonLifeSceneLambdaQueryWrapper.eq(GlCarbonLifeSceneDO::getDeleted, Deleted.UN_DELETE.getDeleted());
        glCarbonLifeSceneLambdaQueryWrapper.orderByDesc(GlCarbonLifeSceneDO::getSequence);
        List<GlCarbonLifeSceneDO> glCarbonLifeScenes = glCarbonLifeSceneMapper.selectList(glCarbonLifeSceneLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(glCarbonLifeScenes)){
            return Lists.newArrayList();
        }
        Map<Long, List<GlCarbonLifeSceneDO>> groupByTypeIdMap = glCarbonLifeScenes.stream()
                .collect(Collectors.groupingBy(GlCarbonLifeSceneDO::getTypeId));

        List<Long> productIds = glCarbonLifeScenes.stream()
                .map(GlCarbonLifeSceneDO::getProductId)
                .map(productId -> Arrays.stream(productId.split(","))
                        .map(Long::valueOf).collect(Collectors.toList()))
                .collect(ArrayList::new,ArrayList::addAll,ArrayList::addAll);

        List<Long> tenantIds = glCarbonLifeScenes.stream()
                .map(GlCarbonLifeSceneDO::getTenantId)
                .map(tenantId -> Arrays.stream(tenantId.split(","))
                        .map(Long::valueOf).collect(Collectors.toList()))
                .collect(ArrayList::new,ArrayList::addAll,ArrayList::addAll);

        List<Long> tenantIdList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(tenantIds) && UserUtils.userIsAuth()){
            String mobileSha256 = UserUtils.getMobileSha256();
            QueryWrapper<GlBehavior> glBehaviorQueryWrapper = Wrappers.query();
            glBehaviorQueryWrapper.select(" DISTINCT tenant_id ");
            glBehaviorQueryWrapper.eq("mobile_sha256",mobileSha256);
            glBehaviorQueryWrapper.in("tenant_id",tenantIds);
            List<Object> objectList = glBehaviorService.listObjs(glBehaviorQueryWrapper);
            if(CollectionUtils.isNotEmpty(objectList)){
                tenantIdList = objectList.stream().map(String::valueOf).map(Long::valueOf).collect(Collectors.toList());
            }
        }

        Map<Long, LifeCompanyProductDO> glCompanyProductMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(productIds)){
            LambdaQueryWrapper<LifeCompanyProductDO> glCompanyProductLambdaQueryWrapper = Wrappers.lambdaQuery();
            glCompanyProductLambdaQueryWrapper.eq(LifeCompanyProductDO::getDeleted,Deleted.UN_DELETE.getDeleted());
            glCompanyProductLambdaQueryWrapper.in(LifeCompanyProductDO::getId,productIds);
            List<LifeCompanyProductDO> glCompanyProducts = glCompanyProductMapper.selectList(glCompanyProductLambdaQueryWrapper);
            if(CollectionUtils.isNotEmpty(glCompanyProducts)){
                glCompanyProductMap = glCompanyProducts.stream()
                        .collect(Collectors.toMap(LifeCompanyProductDO::getId, Function.identity()));
            }
        }
        Set<Long> typeIdSet = glCarbonLifeScenes.stream()
                .map(GlCarbonLifeSceneDO::getTypeId)
                .collect(Collectors.toSet());
        Map<Long, GlCarbonLifeTypeDO> glCarbonLifeTypeMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(typeIdSet)){
            LambdaQueryWrapper<GlCarbonLifeTypeDO> glCarbonLifeTypeLambdaQueryWrapper = Wrappers.lambdaQuery();
            glCarbonLifeTypeLambdaQueryWrapper.eq(GlCarbonLifeTypeDO::getDeleted,Deleted.UN_DELETE.getDeleted());
            glCarbonLifeTypeLambdaQueryWrapper.in(GlCarbonLifeTypeDO::getId,typeIdSet);
            List<GlCarbonLifeTypeDO> glCarbonLifeTypes = glCarbonLifeTypeMapper.selectList(glCarbonLifeTypeLambdaQueryWrapper);
            glCarbonLifeTypes = glCarbonLifeTypes.stream().sorted(Comparator.comparing(GlCarbonLifeTypeDO::getSequence)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(glCarbonLifeTypes)){
                glCarbonLifeTypeMap = glCarbonLifeTypes.stream()
                        .collect(Collectors.toMap(GlCarbonLifeTypeDO::getId, Function.identity()));
            }
        }
        List<CarbonLifeVo> carbonLifeVos = Lists.newArrayListWithCapacity(glCarbonLifeTypeMap.size());
        for (Map.Entry<Long, GlCarbonLifeTypeDO> entry : glCarbonLifeTypeMap.entrySet()){
            CarbonLifeVo carbonLifeVo = new CarbonLifeVo();
            carbonLifeVo.setId(entry.getValue().getId());
            carbonLifeVo.setName(entry.getValue().getName());
            if(!groupByTypeIdMap.containsKey(entry.getKey())){
                continue;
            }
            List<GlCarbonLifeSceneDO> carbonLifeScenes = groupByTypeIdMap.get(entry.getKey());
            List<CarbonLifeVo.CarbonLifeSceneVo> carbonLifeSceneVos = Lists.newArrayListWithCapacity(carbonLifeScenes.size());
            for (GlCarbonLifeSceneDO carbonLifeScene : carbonLifeScenes) {
                CarbonLifeVo.CarbonLifeSceneVo carbonLifeSceneVo = new CarbonLifeVo.CarbonLifeSceneVo();
                BeanUtils.copyProperties(carbonLifeScene,carbonLifeSceneVo);
                carbonLifeSceneVo.setLogoImage(imagePrefix + carbonLifeSceneVo.getLogoImage());
                if(StringUtils.isNotBlank(carbonLifeScene.getTenantId())){
                    String[] split = carbonLifeScene.getTenantId().split(",");
                    for (String tenantId : split) {
                        if(tenantIdList.contains(Long.valueOf(tenantId))){
                            carbonLifeSceneVo.setAction(true);
                            break;
                        }
                    }
                }
                carbonLifeSceneVos.add(carbonLifeSceneVo);
                if(StringUtils.isBlank(carbonLifeScene.getProductId())){
                    continue;
                }
                List<Long> productIdList = Arrays.stream(carbonLifeScene.getProductId().split(","))
                        .map(Long::valueOf).collect(Collectors.toList());
                List<CarbonLifeVo.NameIdVo> nameIdVos = Lists.newArrayListWithCapacity(productIdList.size());
                for (Long productId : productIdList) {
                    if(!glCompanyProductMap.containsKey(productId)){
                       continue;
                    }
                    LifeCompanyProductDO glCompanyProduct = glCompanyProductMap.get(productId);
                    CarbonLifeVo.NameIdVo nameIdVo = new CarbonLifeVo.NameIdVo();
                    nameIdVo.setId(glCompanyProduct.getId());
                    nameIdVo.setCompanyName(glCompanyProduct.getName());
                    nameIdVo.setLogoImage(imagePrefix + glCompanyProduct.getLogoImage());
                    nameIdVo.setAppletAppid(glCompanyProduct.getAppletAppid());
                    nameIdVo.setAppletUrl(glCompanyProduct.getAppletUrl());
                    nameIdVos.add(nameIdVo);
                }
                carbonLifeSceneVo.setProductList(nameIdVos);
            }
            carbonLifeVo.setCarbonLifeSceneVos(carbonLifeSceneVos);
            carbonLifeVos.add(carbonLifeVo);
        }
        return carbonLifeVos;
    }
}