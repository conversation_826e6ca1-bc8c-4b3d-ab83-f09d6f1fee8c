package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.api.R;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonAccumulateStatisticsDao;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorService;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorTmpService;
import com.lvpuhui.gic.wxapp.homepage.dao.*;
import com.lvpuhui.gic.wxapp.homepage.dto.GreenPointsDetailVo;
import com.lvpuhui.gic.wxapp.homepage.dto.GreenPointsVo;
import com.lvpuhui.gic.wxapp.homepage.dto.TickTypeNameDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlPoints;
import com.lvpuhui.gic.wxapp.homepage.entity.GlQuestion;
import com.lvpuhui.gic.wxapp.homepage.entity.GlVideo;
import com.lvpuhui.gic.wxapp.homepage.enums.PointsType;
import com.lvpuhui.gic.wxapp.homepage.service.GlCompanyService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.homepage.service.PointsQueryService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("pointsQueryService")
@Slf4j
public class PointsQueryServiceImpl implements PointsQueryService {

    @Resource
    GlUserService glUserService;
    @Resource
    GlPointsService glPointsService;
    @Resource
    GlBehaviorService glBehaviorService;
    @Resource
    GlCompanyService glCompanyService;
    @Resource
    GlBehaviorTmpService glBehaviorTmpService;
    @Resource
    GlTickoffDao glTickoffDao;
    @Resource
    GlVideoDao glVideoDao;
    @Resource
    GlQuestionDao glQuestionDao;

    @Resource
    private GlStatisticsDao glStatisticsDao;

    @Resource
    private GlStatisticsDayChartDao glStatisticsDayChartDao;

    @Resource
    private GlCarbonAccumulateStatisticsDao glCarbonAccumulateStatisticsDao;

    @Override
    public R getEmission() {
        String result = HttpUtil.createGet("https://wsermap.lvpuhui.com/emission?region=340100").execute().body();
        R r = JSON.parseObject(result,R.class);
        return r;

//        IndexEmissionVo indexEmissionVo = new IndexEmissionVo();
//
//        // 获取昨天日期
//        String yesterday = DateUtil.offsetDay(new DateTime(), -1).toString(DatePattern.PURE_DATE_PATTERN);
//        // 获取前天日期
//        String beforeYesterday = DateUtil.offsetDay(new DateTime(), -2).toString(DatePattern.PURE_DATE_PATTERN);
//
//        IndexEmissionVo.YesterdayBean yesterdayBean = new IndexEmissionVo.YesterdayBean();
//
//        LambdaQueryWrapper<GlStatisticsDayChart> glStatisticsDayChartLambdaQueryWrapper = Wrappers.lambdaQuery();
//        glStatisticsDayChartLambdaQueryWrapper.in(GlStatisticsDayChart::getStatisticsTime,yesterday,beforeYesterday);
//        glStatisticsDayChartLambdaQueryWrapper.orderByDesc(GlStatisticsDayChart::getStatisticsTime);
//        List<GlStatisticsDayChart> glStatisticsDayCharts = glStatisticsDayChartDao.selectList(glStatisticsDayChartLambdaQueryWrapper);
//        if(CollUtil.isNotEmpty(glStatisticsDayCharts)){
//            GlStatisticsDayChart glStatisticsDayChart = glStatisticsDayCharts.get(0);
//            if(BigDecimal.ZERO.compareTo(glStatisticsDayChart.getEmissionSum()) == 0
//                    && glStatisticsDayChart.getEmissionNumber() == 0
//                    && glStatisticsDayChart.getEmissionUserNumber() == 0 && glStatisticsDayCharts.size() >= 2){
//                glStatisticsDayChart = glStatisticsDayCharts.get(1);
//            }
//            yesterdayBean.setEmission(glStatisticsDayChart.getEmissionSum());
//            yesterdayBean.setPeopleCount(glStatisticsDayChart.getEmissionUserNumber());
//            yesterdayBean.setEmissionCount(glStatisticsDayChart.getEmissionNumber());
//        }
//        indexEmissionVo.setYesterday(yesterdayBean);
//
//        IndexEmissionVo.CumulativeBean cumulativeBean = new IndexEmissionVo.CumulativeBean();
//
//        LambdaQueryWrapper<GlStatistics> glStatisticsLambdaQueryWrapper = Wrappers.lambdaQuery();
//        glStatisticsLambdaQueryWrapper.orderByDesc(GlStatistics::getId);
//        glStatisticsLambdaQueryWrapper.last("limit 1");
//        GlStatistics glStatistics = glStatisticsDao.selectOne(glStatisticsLambdaQueryWrapper);
//        if(Objects.nonNull(glStatistics)){
//            cumulativeBean.setEmission(glStatistics.getEmission());
//            cumulativeBean.setPeopleCount(glStatistics.getEmissionNumber());
//            cumulativeBean.setEmissionCount(glStatistics.getEmissionCount());
//        }
//        LambdaQueryWrapper<GlCarbonAccumulateStatisticsDO> yesterdayDoLambdaQueryWrapper = Wrappers.lambdaQuery();
//        yesterdayDoLambdaQueryWrapper.eq(GlCarbonAccumulateStatisticsDO::getType, AccumulateStatisticsType.ACCUMULATE.getCode());
//        yesterdayDoLambdaQueryWrapper.eq(GlCarbonAccumulateStatisticsDO::getProjectId, AccumulateStatisticsProject.ACCUMULATE_PROJECT_ID.getCode());
//        yesterdayDoLambdaQueryWrapper.eq(GlCarbonAccumulateStatisticsDO::getBatchNo, Global.ACCUMULATE_NO);
//        GlCarbonAccumulateStatisticsDO yesterdayCarbonAccumulateStatisticsDO = glCarbonAccumulateStatisticsDao.selectOne(yesterdayDoLambdaQueryWrapper);
//        if(Objects.nonNull(yesterdayCarbonAccumulateStatisticsDO)){
//            cumulativeBean.setEmission(ObjectUtil.defaultIfNull(cumulativeBean.getEmission(),BigDecimal.ZERO).add(yesterdayCarbonAccumulateStatisticsDO.getAccumulateData()));
//            cumulativeBean.setEmissionCount(ObjectUtil.defaultIfNull(cumulativeBean.getEmissionCount(),0L) + (Objects.nonNull(yesterdayCarbonAccumulateStatisticsDO.getEmissionNumber())?yesterdayCarbonAccumulateStatisticsDO.getEmissionNumber().intValue():0));
//            cumulativeBean.setPeopleCount(ObjectUtil.defaultIfNull(cumulativeBean.getPeopleCount(),0L) + (Objects.nonNull(yesterdayCarbonAccumulateStatisticsDO.getEmissionUserNumber())?yesterdayCarbonAccumulateStatisticsDO.getEmissionUserNumber().intValue():0));
//        }
//        indexEmissionVo.setCumulative(cumulativeBean);
//        return R.ok(indexEmissionVo);
    }

    @Override
    public GreenPointsVo getGreenPoints2(String mobileSha256, Integer page) {
        mobileSha256 = UserUtils.getMobileSha256();
        GreenPointsVo greenPoints = new GreenPointsVo();

        LambdaQueryWrapper<GlUser> glUserLambdaQueryWrapper = Wrappers.lambdaQuery();
        glUserLambdaQueryWrapper.eq(GlUser::getMobileSha256,mobileSha256);
        GlUser user = glUserService.getOne(glUserLambdaQueryWrapper);
        if(Objects.isNull(user)){
            return greenPoints;
        }
        page = Objects.isNull(page)?1:page <= 0 ? 1 : page;
        int pageSize = 15;
        int start = (page - 1) * pageSize;
        BeanUtil.copyProperties(user, greenPoints);
        greenPoints.setPoint(greenPoints.getPointRemain());

        LambdaQueryWrapper<GlPoints> glPointsLambdaQueryWrapper = Wrappers.lambdaQuery();
        glPointsLambdaQueryWrapper.eq(GlPoints::getMobileSha256,mobileSha256);
        glPointsLambdaQueryWrapper.orderByDesc(GlPoints::getCreated);
        glPointsLambdaQueryWrapper.last("limit "+ start + "," + pageSize);
        List<GlPoints> glPointsList = glPointsService.list(glPointsLambdaQueryWrapper);

        List<GreenPointsDetailVo> greenPointsDetailVos = Lists.newArrayList();
        if(CollUtil.isEmpty(glPointsList)){
            greenPoints.setList(greenPointsDetailVos);
            return greenPoints;
        }
        // 减排量ID集合
        List<Long> behaviorIds = glPointsList.stream()
                .filter(glPoints -> PointsType.BEHAVIOR.getCode().equals(glPoints.getType()))
                .map(GlPoints::getSourceId).collect(Collectors.toList());

        // 减排量ID集合
        List<String> orderIds = glPointsList.stream()
                .filter(glPoints -> PointsType.BEHAVIOR.getCode().equals(glPoints.getType()))
                .map(GlPoints::getOrderId).collect(Collectors.toList());
        Map<String, GlBehavior> glBehaviorMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(orderIds)){
            LambdaQueryWrapper<GlBehavior> glBehaviorLambdaQueryWrapper = Wrappers.lambdaQuery();
            glBehaviorLambdaQueryWrapper.eq(GlBehavior::getMobileSha256,mobileSha256);
            glBehaviorLambdaQueryWrapper.in(GlBehavior::getEventId,orderIds);
            List<GlBehavior> glBehaviors = glBehaviorService.list(glBehaviorLambdaQueryWrapper);
            if(CollUtil.isNotEmpty(glBehaviors)){
                glBehaviorMap = glBehaviors.stream().collect(Collectors.toMap(GlBehavior::getEventId, Function.identity(), (k1, k2) -> k1));
            }
        }
        // 打卡ID集合
        List<Long> tickOffIds = glPointsList.stream()
                .filter(glPoints -> PointsType.TICK_OFF.getCode().equals(glPoints.getType()))
                .map(GlPoints::getSourceId).collect(Collectors.toList());
        Map<Long, TickTypeNameDto> typeNameDtoMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(tickOffIds)){
            List<TickTypeNameDto> tickTypeNameDtoList = glTickoffDao.selectTickTypeName(tickOffIds);
            typeNameDtoMap = tickTypeNameDtoList.stream().collect(Collectors.toMap(TickTypeNameDto::getTickOffId, Function.identity(), (k1, k2) -> k1));
        }
        // 视频ID集合
        List<String> videoIds = glPointsList.stream()
                .filter(glPoints -> PointsType.VIDEO.getCode().equals(glPoints.getType()))
                .map(GlPoints::getOrderId).collect(Collectors.toList());
        Map<Long, GlVideo> glVideoMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(videoIds)){
            List<GlVideo> glVideos = glVideoDao.selectBatchIds(videoIds);
            if(CollUtil.isNotEmpty(glVideos)){
                glVideoMap = glVideos.stream().collect(Collectors.toMap(GlVideo::getId, Function.identity(), (k1, k2) -> k1));
            }
        }
        // 问卷ID集合
        List<String> questionIds = glPointsList.stream()
                .filter(glPoints -> PointsType.QUESTION.getCode().equals(glPoints.getType()))
                .map(GlPoints::getOrderId).collect(Collectors.toList());
        Map<Long, GlQuestion> glQuestionMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(questionIds)){
            List<GlQuestion> glQuestions = glQuestionDao.selectBatchIds(questionIds);
            if(CollUtil.isNotEmpty(glQuestions)){
                glQuestionMap = glQuestions.stream().collect(Collectors.toMap(GlQuestion::getId, Function.identity(), (k1, k2) -> k1));
            }
        }
        // 负数的类型
        List<Integer> subtractTypeList = Lists.newArrayList(PointsType.EXCHANGE.getCode(),PointsType.RECYCLE.getCode(),PointsType.EMISSION_RECYCLE.getCode());
        for (GlPoints glPoint : glPointsList) {
            GreenPointsDetailVo detailVo = new GreenPointsDetailVo();
            detailVo.setPoint(glPoint.getPoint().intValue());
            detailVo.setCompanyName(glPoint.getCompanyName());
            detailVo.setSoldTime(glPoint.getCreated());
            detailVo.setProductName(StrUtil.isBlank(glPoint.getDescription())?"":glPoint.getDescription());
            if(PointsType.BEHAVIOR.getCode().equals(glPoint.getType())){
                GlBehavior glBehavior = glBehaviorMap.get(glPoint.getOrderId());
                if(Objects.nonNull(glBehavior)){
                    detailVo.setSoldTime(glBehavior.getDate());
                }
            }
            if(PointsType.TICK_OFF.getCode().equals(glPoint.getType())){
                TickTypeNameDto tickTypeNameDto = typeNameDtoMap.get(glPoint.getSourceId());
                if(Objects.nonNull(tickTypeNameDto)){
                    detailVo.setProductName(tickTypeNameDto.getTickTypeName());
                }
            }
            if(PointsType.VIDEO.getCode().equals(glPoint.getType())){
                if(NumberUtil.isNumber(glPoint.getOrderId())){
                    GlVideo glVideo = glVideoMap.get(Long.parseLong(glPoint.getOrderId()));
                    if(Objects.nonNull(glVideo)){
                        detailVo.setProductName(glVideo.getName());
                    }
                }
            }
            if(PointsType.QUESTION.getCode().equals(glPoint.getType())){
                if(NumberUtil.isNumber(glPoint.getOrderId())){
                    GlQuestion glQuestion = glQuestionMap.get(Long.parseLong(glPoint.getOrderId()));
                    if(Objects.nonNull(glQuestion)){
                        detailVo.setProductName(glQuestion.getName());
                    }
                }
            }
            if(PointsType.SUBJECT.getCode().equals(glPoint.getType())){
                detailVo.setProductName("答题领积分");
            }
            if(PointsType.CARBON.getCode().equals(glPoint.getType())){
                detailVo.setProductName("碳汇积分");
            }
            if(PointsType.TIP_OFF.getCode().equals(glPoint.getType())){
                detailVo.setProductName("举报得积分");
            }
            if(PointsType.TREE_PLANTING.getCode().equals(glPoint.getType())){
                detailVo.setProductName("义务植树积分");
            }
            detailVo.setPointType(1);
            if(subtractTypeList.contains(glPoint.getType())){
                detailVo.setPointType(0);
            }
            greenPointsDetailVos.add(detailVo);
        }
        // 按时间降序
        greenPointsDetailVos.sort(Comparator.comparing(GreenPointsDetailVo::getSoldTime).reversed());
        greenPoints.setList(greenPointsDetailVos);
        return greenPoints;
    }
}
