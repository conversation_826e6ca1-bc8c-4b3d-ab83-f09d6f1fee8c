package com.lvpuhui.gic.wxapp.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbonbook.dto.CarbonBooksNewVo;
import com.lvpuhui.gic.wxapp.carbonbook.service.CarbonBookService;
import com.lvpuhui.gic.wxapp.homepage.dto.RankAccumulateList;
import com.lvpuhui.gic.wxapp.homepage.dto.RankAccumulateListDto;
import com.lvpuhui.gic.wxapp.homepage.service.GlRankService;
import com.lvpuhui.gic.wxapp.homepage.service.PointsQueryService;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenAccumulateRankDto;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenCarbonBooksMobileDto;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenCarbonBooksVo;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenEmissionVo;
import com.lvpuhui.gic.wxapp.screen.dto.ScreenRankAccumulateVo;
import com.lvpuhui.gic.wxapp.screen.service.ScreenService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 屏幕相关服务实现类
 * <AUTHOR>
 */
@Service
public class ScreenServiceImpl implements ScreenService {

    @Resource
    private PointsQueryService pointsQueryService;

    @Resource
    private GlRankService glRankService;

    @Resource
    private CarbonBookService carbonBookService;

    /**
     * 首页减排量统计接口
     * @return 减排量统计结果
     */
    @Override
    public ScreenEmissionVo getEmission() {
        R originalResult = pointsQueryService.getEmission();

        // 转换为Screen专用的VO
        ScreenEmissionVo screenEmissionVo = new ScreenEmissionVo();
        if (originalResult != null) {
            screenEmissionVo.setCode(Integer.valueOf(String.valueOf(originalResult.getCode())));
            screenEmissionVo.setMsg(originalResult.getMsg());
            screenEmissionVo.setData(originalResult.getData());
            // R类没有isSuccess方法，根据code判断成功状态
            screenEmissionVo.setSuccess(originalResult.getCode() == 200);
        }

        return screenEmissionVo;
    }

    /**
     * 累积排行接口
     * @param screenAccumulateRankDto 累积排行请求参数
     * @return 累积排行结果
     */
    @Override
    public ScreenRankAccumulateVo accumulateRank(ScreenAccumulateRankDto screenAccumulateRankDto) {
        // 转换为原始DTO
        RankAccumulateListDto rankAccumulateListDto = new RankAccumulateListDto();
        rankAccumulateListDto.setMobileSha256(screenAccumulateRankDto.getMobileSha256());

        // TODO: 原方法GlRankService.accumulateRank内部使用UserUtils.getUserId()和UserUtils.getMobileSha256()
        // 虽然我们传入了mobileSha256参数，但原方法内部仍会调用UserUtils获取用户信息
        RankAccumulateList originalResult = glRankService.accumulateRank(rankAccumulateListDto);

        // 转换为Screen专用的VO
        ScreenRankAccumulateVo screenRankAccumulateVo = new ScreenRankAccumulateVo();
        BeanUtil.copyProperties(originalResult, screenRankAccumulateVo);

        return screenRankAccumulateVo;
    }

    /**
     * 碳账本接口
     * @param screenCarbonBooksMobileDto 碳账本请求参数
     * @return 碳账本结果
     */
    @Override
    public ScreenCarbonBooksVo carbonBooksMobile(ScreenCarbonBooksMobileDto screenCarbonBooksMobileDto) {
        // TODO: 原方法carbonBooksMobile()使用UserUtils.getMobileSha256()获取用户信息
        // 现在需要使用传入的mobileSha256参数，但原service方法不支持参数
        // 这里暂时调用原方法，实际使用时需要修改CarbonBookService添加支持mobileSha256参数的方法

        // 由于原方法carbonBooksMobile()没有参数且内部使用UserUtils，
        // 这里直接调用原方法，mobileSha256参数暂时未使用
        // 实际项目中需要扩展CarbonBookService接口添加带参数的方法
        CarbonBooksNewVo originalResult = carbonBookService.carbonBooksMobile();

        // 转换为Screen专用的VO
        ScreenCarbonBooksVo screenCarbonBooksVo = new ScreenCarbonBooksVo();
        BeanUtil.copyProperties(originalResult, screenCarbonBooksVo);

        return screenCarbonBooksVo;
    }
}
