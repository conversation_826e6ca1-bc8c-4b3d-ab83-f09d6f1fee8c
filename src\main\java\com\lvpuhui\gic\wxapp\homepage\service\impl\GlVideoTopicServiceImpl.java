package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.homepage.dao.GlVideoDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlVideoTopicDao;
import com.lvpuhui.gic.wxapp.homepage.entity.GlVideo;
import com.lvpuhui.gic.wxapp.homepage.entity.GlVideoTopic;
import com.lvpuhui.gic.wxapp.homepage.dto.VideoTopic;
import com.lvpuhui.gic.wxapp.homepage.dto.UrlLinkVo;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.homepage.enums.VideoState;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import com.lvpuhui.gic.wxapp.homepage.service.GlVideoTopicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 视频主题表 服务实现类
 * <AUTHOR>
 * @since 2022-07-01
 */
@Slf4j
@Service
public class GlVideoTopicServiceImpl extends ServiceImpl<GlVideoTopicDao, GlVideoTopic> implements GlVideoTopicService {

    @Resource
    private GlVideoDao glVideoDao;

    @Resource
    private GlUserService glUserService;

    @Override
    public List<VideoTopic> videoTopicList() {
        LambdaQueryWrapper<GlVideoTopic> videoTopicLambdaQueryWrapper = Wrappers.lambdaQuery();
        videoTopicLambdaQueryWrapper.eq(GlVideoTopic::getDeleted, Deleted.UN_DELETE.getDeleted());
        videoTopicLambdaQueryWrapper.eq(GlVideoTopic::getState, VideoState.PUBLISH.getState());
        videoTopicLambdaQueryWrapper.orderByDesc(GlVideoTopic::getSequence);
        List<GlVideoTopic> glVideoTopics = baseMapper.selectList(videoTopicLambdaQueryWrapper);
        if(CollUtil.isEmpty(glVideoTopics)){
            return Lists.newArrayList();
        }
        Set<Long> videoTopicIds = glVideoTopics.stream().map(GlVideoTopic::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<GlVideo> glVideoLambdaQueryWrapper = Wrappers.lambdaQuery();
        glVideoLambdaQueryWrapper.select(GlVideo::getId,GlVideo::getTopicId);
        glVideoLambdaQueryWrapper.eq(GlVideo::getState,VideoState.PUBLISH.getState());
        glVideoLambdaQueryWrapper.eq(GlVideo::getDeleted,Deleted.UN_DELETE.getDeleted());
        glVideoLambdaQueryWrapper.in(GlVideo::getTopicId,videoTopicIds);
        List<GlVideo> glVideos = glVideoDao.selectList(glVideoLambdaQueryWrapper);
        Map<Long, Long> topicIdVideoNumberMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(glVideos)){
            topicIdVideoNumberMap = glVideos.stream()
                    .collect(Collectors.groupingBy(GlVideo::getTopicId, Collectors.mapping(GlVideo::getId, Collectors.counting())));
        }
        List<VideoTopic> videoTopics = Lists.newArrayListWithCapacity(glVideoTopics.size());
        for (GlVideoTopic glVideoTopic : glVideoTopics) {
            VideoTopic videoTopic = new VideoTopic();
            BeanUtil.copyProperties(glVideoTopic,videoTopic);
            if(topicIdVideoNumberMap.containsKey(glVideoTopic.getId())){
                videoTopic.setVideoNumber(topicIdVideoNumberMap.get(glVideoTopic.getId()));
            }
            videoTopics.add(videoTopic);
        }
        return videoTopics;
    }

    @Override
    public UrlLinkVo getUrlLink() {
        UrlLinkVo urlLinkVo = new UrlLinkVo();

        String accessToken = glUserService.getAccessToken();
        String urlLink = StrUtil.format(Global.GENERATE_URL_LINK, accessToken);
        Map<String,Object> params = Maps.newHashMap();
        params.put("path","/pages/Homepage/Homepage");
        params.put("env_version","release");
        params.put("expire_interval",30);
        params.put("expire_type",1);
        params.put("query","");

        String result = HttpUtil.post(urlLink, JSON.toJSONString(params));
        log.info("请求微信生成短链,请求地址:{},参数:{},返回结果:{}",urlLink, JSON.toJSONString(params),result);
        if(StrUtil.isBlank(result)){
            return urlLinkVo;
        }
        JSONObject jsonObject = JSON.parseObject(result);
        Integer errcode = jsonObject.getInteger("errcode");
        if(errcode != 0){
            log.info("调用微信生成链接出错");
            return urlLinkVo;
        }
        String url_link = jsonObject.getString("url_link");
        urlLinkVo.setUrlLink(url_link);
        return urlLinkVo;
    }
}
