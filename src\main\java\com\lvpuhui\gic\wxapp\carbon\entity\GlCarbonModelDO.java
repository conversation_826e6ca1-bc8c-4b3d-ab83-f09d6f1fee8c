package com.lvpuhui.gic.wxapp.carbon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 碳汇项目实例（用户申请）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_carbon_model")
public class GlCarbonModelDO extends Model<GlCarbonModelDO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * projectID
     */
    private Long projectId;

    private Long year;

    private Integer yearStatus;

    /**
     * 减排因子ID
     */

    private Long factorId;

    private String card;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 碳汇项目名称
     */
    private String name;


    /**
     * 业主
     */
    private String owner;


    /**
     * 电话
     */
    private String mobile;

    /**
     * 碳汇面积
     */
    private BigDecimal area;

    /**
     * 项目地址
     */
    private String address;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private LocalDateTime updated;

    /**
     * 修改者ID
     */
    private Long updator;

    /**
     * 审核时间
     */
    private LocalDateTime audited;

    /**
     * 审核者ID
     */
    private Long auditor;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
