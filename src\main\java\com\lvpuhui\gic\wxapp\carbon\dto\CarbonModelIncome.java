package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "项目信息")
public class CarbonModelIncome {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;



    private Long modelId;


    private String modelName;

    @Schema(description = "预计收入(按model分)")
    private BigDecimal modelAmount;


    @Schema(description = "面积")
    private BigDecimal area;


    @Schema(description = "预计减排量")
    private BigDecimal emission;

    /**
     * 补贴价格
     */
    @Schema(description = "补贴价格")
    private BigDecimal subsidyPrice;


    /**
     * 累计奖惩
     */
    @Schema(description = "累计奖惩")
    private BigDecimal rewordAmount;


    /**
     * 起始补贴量
     */

    @Schema(description = "起始补贴")
    private BigDecimal beginEmission;


    @Schema(description = "终止补贴")
    private BigDecimal endEmission;


    @Schema(description = "补贴限额")
    private BigDecimal subsidyQuota;

    @Schema(description = "补贴主体 0-按业主，1-按地")
    private Integer subject;

    @Schema(description = "补贴判断条件 0-按面积，1-案件排量")
    private Integer condition;

    @Schema(description = "补贴年度")
    private Integer year;



    private BigDecimal totalEmission;

    private BigDecimal totalPrice;



}
