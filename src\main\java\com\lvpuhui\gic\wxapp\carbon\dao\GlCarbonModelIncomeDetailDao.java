package com.lvpuhui.gic.wxapp.carbon.dao;

import com.lvpuhui.gic.wxapp.carbon.dto.InComeDetails;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonModelIncomeDetailDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 碳汇项目实例（用户申请）收入明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
public interface GlCarbonModelIncomeDetailDao extends BaseMapper<GlCarbonModelIncomeDetailDO> {

    /**
     * 计算累计奖励金额
     * @param modelId
     * @return
     */
    BigDecimal sumReward(@Param("modelId") Long modelId);

    /**
     * 计算惩罚金额
     * @param modelId
     * @return
     */
    BigDecimal punAmount(@Param("modelId")Long modelId);

    /**
     * 查询收益明细
     * @param modelId
     * @return
     */
    List<InComeDetails> selectByModelId(@Param("modelId")Long modelId,@Param("dateTime") LocalDate dateTime);


    /**
     * 查询所有昨日总共收益
     * @param yesterday
     * @param userId
     * @return
     */
    BigDecimal getCarbonEveryReward(@Param("yesterday")String yesterday, @Param("userId")Long userId);

    /**
     * 查询单model昨日收益
     * @param yesterday
     * @param modelId
     * @param userId
     * @return
     */
    BigDecimal getCarbonEveryModelReward(@Param("yesterday")String yesterday,@Param("modelId")Long modelId,@Param("userId")Long userId);

    /**
     * 根据用户ID获取不可提现金额
     * @param userId 用户ID
     * @return 不可提现金额
     */
    BigDecimal getUnTakingAmountByUserId(@Param("userId") Long userId);

    List<InComeDetails> selectByUserId(@Param("userId")Long userId,@Param("dateTime") LocalDate dateTime);
}
