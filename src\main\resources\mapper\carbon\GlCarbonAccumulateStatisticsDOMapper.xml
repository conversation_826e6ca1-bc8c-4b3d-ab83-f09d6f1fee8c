<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbon.dao.GlCarbonAccumulateStatisticsDao">

    <select id="getAccumulateStatistics"
            resultType="com.lvpuhui.gic.wxapp.homepage.dto.CarbonAccumulateProjectDto">
        SELECT
            gcas.project_id,
            gcp.`name`,
            gcas.accumulate_data AS accumulateArea,
            yesterday.accumulate_data AS yesterdayArea
        FROM
                ( SELECT * FROM gl_carbon_project ORDER BY id DESC LIMIT 2 ) gcp
                    INNER JOIN gl_carbon_accumulate_statistics gcas ON gcp.id = gcas.project_id
                    LEFT JOIN (SELECT * FROM gl_carbon_accumulate_statistics WHERE type = 1 AND batch_no = #{batchNo}) yesterday
                              ON gcp.id = yesterday.project_id
        WHERE
            gcas.type = 0
    </select>
    <select id="getAccumulateAndYesterdayStatistics" resultType="com.lvpuhui.gic.wxapp.homepage.dto.CarbonAccumulateProjectDto">
        SELECT
            gcas.project_id,
            gcas.accumulate_data AS accumulateArea,
            yesterdayAccumulate.accumulate_data AS yesterdayArea
        FROM
            gl_carbon_accumulate_statistics gcas
                INNER JOIN ( SELECT accumulate_data, project_id FROM gl_carbon_accumulate_statistics WHERE type = 1 AND project_id = 0 ORDER BY batch_no DESC LIMIT 1 ) yesterdayAccumulate ON gcas.project_id = yesterdayAccumulate.project_id
        WHERE
            gcas.type = 0
          AND gcas.project_id = 0
    </select>
</mapper>
