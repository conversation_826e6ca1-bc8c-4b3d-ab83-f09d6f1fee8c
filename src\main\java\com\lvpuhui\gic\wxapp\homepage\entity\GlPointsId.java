package com.lvpuhui.gic.wxapp.homepage.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 积分记录ID表(GlPointsId)表实体类
 *
 * <AUTHOR>
 * @since 2022-07-13 17:09:21
 */
@Data
@Accessors(chain = true)
public class GlPointsId extends Model<GlPointsId> {
    /**
    * 自增id
    */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /** ${column.comment} */ 
    private String currentValue;
}


