package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 打卡类别表(GlSceneCategory)实体类
 * <AUTHOR>
 * @since 2022年5月5日 19:17:59
 */
@Data
public class GlTickoffType extends Model<GlTickoffType> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 场景企业名称
     */
    private String companyName;

    /**
     * 场景企业ID
     */
    private Long companyId;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 打卡前显示图片
     */
    private String beforeImage;

    /**
     * 打卡后显示图片
     */
    private String afterImage;

    /**
     * 位置权重
     */
    private Integer sequence;

    /**
     * 打卡积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 小程序显示(0：不显示  1：显示)
     */
    @TableField(value = "is_show")
    private Integer isShow;

    /**
     * 重复周期(0：仅一次不可能重复  1：每天)
     */
    private Integer type;

    /**
     * 发布状态(0：保存  1：发布)
     */
    private Integer state;

    /**
     * 打卡地址
     */
    private String url;

    /**
     * 打卡人数
     */
    private Integer consumed;

    /**
     * 发布时间
     */
    private Date published;

    /**
     * 位置判断开关
     */
    private Integer positionSwitch;

    /**
     * 地理位置关键词
     */
    private String positionKeyword;

    /**
     * 打卡距离(单位/米)
     */
    private Integer distance;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 更新时间
     */
    private Date updated;

    /**
     * 修改者ID
     */
    private Long updator;

    /**
     * 删除 0未删除 1已删除
     */
    private Integer deleted;
}
