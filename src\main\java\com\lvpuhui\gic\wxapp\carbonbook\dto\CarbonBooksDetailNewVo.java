package com.lvpuhui.gic.wxapp.carbonbook.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 新碳账详情本返回值
 */
@Data
@Accessors(chain = true)
public class CarbonBooksDetailNewVo{

    /**
     * 总数
     */
    private Integer total = 0;

    /**
     * 详情数据
     */
    private List<CarbonBooksDetailVo> details = Lists.newArrayList();

    @Data
    public static class CarbonBooksDetailVo{

        /**
         * 行为名称
         */
        private String title;

        /**
         * 应用名称
         */
        private String campus;

        /**
         * 格式化减排量
         */
        private String emission;

        /**
         * 减排量
         */
        private Double emissionNum;

        /**
         * 行为发生日期
         */
        private String date;

        /**
         * 积分
         */
        private Double point;
    }
}
