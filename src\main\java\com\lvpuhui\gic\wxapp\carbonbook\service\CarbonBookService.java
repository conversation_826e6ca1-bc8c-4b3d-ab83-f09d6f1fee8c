package com.lvpuhui.gic.wxapp.carbonbook.service;

import com.lvpuhui.gic.wxapp.carbonbook.dto.*;

/**
 * <AUTHOR>
 */
public interface CarbonBookService {
    /**
     * 我的积分减排量
     * @param mobileSha256
     * @return
     */
    PointsEmission myPointsEmission(String mobileSha256);

    /**
     * 获取碳账本场景数据
     * @param mobileSha256 手机号
     */
    CarbonBooksVo getCarbonBooks2(String mobileSha256);

    /**
     * 获取场景减排明细
     * @param mobileSha256 手机号
     * @param sceneId 场景ID
     * @param page 页码
     * @param size 每页大小
     */
    SceneTotalVo getSceneDetails2(String mobileSha256, Integer sceneId, Integer page, Integer size);

    /**
     * 碳账本接口
     */
    CarbonBooksNewVo carbonBooksMobile();

    /**
     * 碳账本减排明细接口
     */
    CarbonBooksDetailNewVo carbonBooksMobileDetail(Integer actId, Integer page, Integer size);

    CarbonBooksVo getCarbonBooksByMobileSha256(String mobileSha256);
}
