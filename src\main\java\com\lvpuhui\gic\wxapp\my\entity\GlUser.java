package com.lvpuhui.gic.wxapp.my.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户积分表(GlUser)表实体类
 *
 * <AUTHOR>
 * @since 2022-05-06 15:16:32
 */
@Data
@SuppressWarnings("serial")
public class GlUser extends Model<GlUser> {
  @TableId(type = IdType.AUTO)
  /**自增id*/
  private Long id;
  /**微信Open ID*/
  private String openId;
  /**
   * 支付宝小程序用户Id
   */
  private String alipayOpenId;
  /**昵称*/
  private String nickName;
  /**头像*/
  private String avatarUrl;
  /**手机号码*/
  private String mobile;
  /**用户id(sha256)*/
  private String mobileSha256;
  /**总积分点数*/
  private Long pointTotal;
  /**剩余积分点数*/
  private Long pointRemain;
  /**消耗积分点数*/
  private Long pointConsumed;
  /**获取次数*/
  private Integer obtainTimes;
  /**使用次数*/
  private Integer exchangeTimes;
  /**用户微众私钥*/
  private String privateKey;
  /**用户微众地址*/
  private String wzAddress;
  /**创建时间*/
  private Date created;
  /**更新时间*/
  private Date updated;

  /**
   * 个人碳汇减排量
   */
  private BigDecimal carbonEmission;
}