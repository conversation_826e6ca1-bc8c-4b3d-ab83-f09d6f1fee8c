package com.lvpuhui.gic.wxapp.other.entity;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 绿色消费记录表 (来源对接企业)(GlConsumption)表实体类
 *
 * <AUTHOR>
 * @since 2022-06-21 11:29:35
 */
@Data
@Accessors(chain = true)
public class GlConsumption extends Model<GlConsumption> {
    /**
    * 自增id
    */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;
    /** 减排行为数据的id */ 
    private String orderId;
    /** 商品id */ 
    private String productId;
    /** 商品名称 */ 
    private String productName;
    /** 一级品类 */ 
    private String category;
    /** 二级品类 */ 
    private String subCategory;
    /** 能耗等级 */ 
    private String energyLevel;
    /** 封面图 */ 
    private String coverImage;
    /** 商品价格 */ 
    private Double productPrice;
    /** 数量 */ 
    private Integer quantity;
    /** 手机号码密文 */ 
    private String mobileSha256;
    /** 销售时间 */ 
    private Date soldTime;
    /** 渠道店面 */ 
    private String store;
    /** 减排量(如有) */ 
    private Double emission;
    /** 企业ID */ 
    private Long companyId;
    /** 企业名称 */ 
    private String companyName;
    /** 创建时间 */ 
    private Date created;
    /** 文件id */ 
    private Integer fileId;
    /** 删除 0未删除 1已删除 */ 
    private Integer deleted;
}


