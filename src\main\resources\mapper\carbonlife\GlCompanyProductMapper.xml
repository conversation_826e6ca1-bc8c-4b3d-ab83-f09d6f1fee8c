<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbonlife.dao.GlCompanyProductMapper">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.carbonlife.entity.LifeCompanyProductDO">
    <!--@mbg.generated-->
    <!--@Table gl_company_product-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="logo_image" jdbcType="VARCHAR" property="logoImage" />
    <result column="sequence" jdbcType="INTEGER" property="sequence" />
    <result column="jump_url" jdbcType="VARCHAR" property="jumpUrl" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
    <result column="updator" jdbcType="BIGINT" property="updator" />
    <result column="deleted" jdbcType="BOOLEAN" property="deleted" />
    <result column="applet_appid" jdbcType="VARCHAR" property="appletAppid" />
    <result column="applet_url" jdbcType="VARCHAR" property="appletUrl" />
    <result column="product_describe" jdbcType="VARCHAR" property="productDescribe" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, company_name, company_id, category_id, logo_image, `sequence`, jump_url, 
    created, creator, updated, updator, deleted, applet_appid, applet_url, product_describe
  </sql>
</mapper>