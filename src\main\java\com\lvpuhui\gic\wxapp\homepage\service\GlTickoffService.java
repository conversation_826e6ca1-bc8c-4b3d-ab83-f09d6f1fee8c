package com.lvpuhui.gic.wxapp.homepage.service;

import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.entity.GlTickoff;
import com.lvpuhui.gic.wxapp.homepage.dto.CheckTickDto;
import com.lvpuhui.gic.wxapp.homepage.dto.TickoffDto;
import com.lvpuhui.gic.wxapp.homepage.dto.CheckTick;
import com.lvpuhui.gic.wxapp.homepage.dto.Tickoff;

/**
 * 打卡数据 (用户扫码)(GlTickoff)表服务接口
 * <AUTHOR>
 * @since 2022年5月5日 19:23:32
 */
public interface GlTickoffService extends IService<GlTickoff> {

    /**
     * 打卡接口
     */
    R<Tickoff> punchClock(TickoffDto tickoffDto);

    /**
     * 判断是否已打卡接口
     */
    CheckTick checkTick(CheckTickDto checkTickDto);
}
