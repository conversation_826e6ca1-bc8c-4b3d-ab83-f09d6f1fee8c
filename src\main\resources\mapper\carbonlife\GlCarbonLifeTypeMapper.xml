<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.carbonlife.dao.GlCarbonLifeTypeMapper">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.carbonlife.entity.GlCarbonLifeTypeDO">
    <!--@mbg.generated-->
    <!--@Table gl_carbon_life_type-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sequence" jdbcType="INTEGER" property="sequence" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, `sequence`, description, deleted, created, updated
  </sql>
</mapper>