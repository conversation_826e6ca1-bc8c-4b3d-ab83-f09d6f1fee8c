package com.lvpuhui.gic.wxapp.screen.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 屏幕累积排行榜返回值VO
 * <AUTHOR>
 */
@Data
@Schema(description = "屏幕累积排行榜返回值")
public class ScreenRankAccumulateVo {

    /**
     * 是否已过截止日期 true:已过  false:未过
     */
    @Schema(description = "是否已过截止日期 true:已过  false:未过")
    private Boolean isEnd;

    /**
     * 领取商品名称
     */
    @Schema(description = "领取商品名称")
    private String goodsName;

    /**
     * 排行前多少名可以领取
     */
    @Schema(description = "排行前多少名可以领取")
    private Integer top;

    /**
     * 截止日期
     */
    @Schema(description = "截止日期")
    private String endTime;

    /**
     * 当前用户信息
     */
    @Schema(description = "当前用户信息")
    private AccumulateRankUser currentUser;

    /**
     * 累积上榜用户信息集合
     */
    @Schema(description = "累积上榜用户信息集合")
    private List<AccumulateRankUser> accumulateRankUsers;

    @Data
    @Schema(description = "累积排行用户信息")
    public static class AccumulateRankUser{

        /**
         * 排名
         */
        @Schema(description = "排名")
        private Long rank;

        /**
         * 在整个城市中的排行榜
         */
        @Schema(description = "在整个城市中的排行榜")
        private Long cityRank;

        /**
         * 打败了多少人的占比
         */
        @Schema(description = "打败了多少人的占比")
        private Double radio;

        /**
         * 减排量
         */
        @Schema(description = "减排量")
        private Double emission;

        /**
         * 昵称
         */
        @Schema(description = "昵称")
        private String nickName;

        /**
         * 头像
         */
        @Schema(description = "头像")
        private String avatarUrl;

        /**
         * 用户id(sha256)
         */
        @Schema(description = "用户id(sha256)")
        private String mobileSha256;

        public Long getRank() {
            if(Objects.nonNull(cityRank)){
                return cityRank;
            }
            return rank;
        }
    }
}
