package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "碳汇排行返回值")
public class CarbonRankVo {

    /**
     * 排行前20用户
     */
    @Schema(description = "排行前20用户")
    private List<CarbonRankUserVo> carbonRankUserVos;

    /**
     * 当前用户
     */
    @Schema(description = "当前用户")
    private CarbonRankUserVo currentUser;
}
