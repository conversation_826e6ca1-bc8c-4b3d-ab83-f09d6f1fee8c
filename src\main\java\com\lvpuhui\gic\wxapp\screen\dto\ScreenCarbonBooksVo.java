package com.lvpuhui.gic.wxapp.screen.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 屏幕碳账本返回值VO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "屏幕碳账本返回值")
public class ScreenCarbonBooksVo{

    /**
     * 格式化减排量
     */
    @Schema(description = "格式化减排量")
    private String emission;

    /**
     * 减排量
     */
    @Schema(description = "减排量")
    private Double emissionNum = 0.0;

    /**
     * 积分
     */
    @Schema(description = "积分")
    private Integer point = 0;

    /**
     * 减排级别名称
     */
    @Schema(description = "减排级别名称")
    private String title;

    /**
     * 排行
     */
    @Schema(description = "排行")
    private Long rank;

    /**
     * 打败了多少人的占比
     */
    @Schema(description = "打败了多少人的占比")
    private Double radio;

    /**
     * 行为减排量
     */
    @Schema(description = "行为减排量")
    private List<EmissionInfoVo> sceneList;

    @Data
    @Schema(description = "减排信息VO")
    public static class EmissionInfoVo{

        /**
         * 行为ID
         */
        @Schema(description = "行为ID")
        private Integer id;

        /**
         * 行为名称
         */
        @Schema(description = "行为名称")
        private String name;

        /**
         * 格式化行为减排量
         */
        @Schema(description = "格式化行为减排量")
        private String emission;

        /**
         * 行为减排量
         */
        @Schema(description = "行为减排量")
        private Double emissionNum;

        /**
         * 占比
         */
        @Schema(description = "占比")
        private Double ratio;
    }
}
