package com.lvpuhui.gic.wxapp.sms.utils;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponseBody;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class SmsUtils {

    /**
     * key
     */
    private final String accessKeyId = "LTAI5t7N1U1jXotrkykvDWnW";

    /**
     * secret
     */
    private final String accessKeySecret = "******************************";

    /**
     * 域名
     */
    private final String endpointOverride = "dysmsapi.aliyuncs.com";

    /**
     * 签名
     */
    private final String signName = "绿普惠科技北京";

    /**
     * 模板code
     */
    private final String templateCode = "SMS_490030113";

    public String sendSms(String phoneNum) {
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKeyId)
                .accessKeySecret(accessKeySecret)
                .build());
        String randomNumbers = RandomUtil.randomNumbers(6);
        try (AsyncClient client = AsyncClient.builder()
                .credentialsProvider(provider)
                .overrideConfiguration(ClientOverrideConfiguration.create().setEndpointOverride(endpointOverride))
                .build()){
            Map<String, Object> templateParam = new HashMap<>();
            templateParam.put("code", randomNumbers);
            SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                    .phoneNumbers(phoneNum)
                    .signName(signName)
                    .templateCode(templateCode)
                    .templateParam(JSON.toJSONString(templateParam))
                    .build();
            CompletableFuture<SendSmsResponse> sendSmsResponseCompletableFuture = client.sendSms(sendSmsRequest);
            SendSmsResponse sendSmsResponse = sendSmsResponseCompletableFuture.get();
            if(sendSmsResponse.getStatusCode() == 200){
                SendSmsResponseBody body = sendSmsResponse.getBody();
                if("OK".equals(body.getCode())){
                    return randomNumbers;
                }
            }
        }catch (Exception e){
            log.error("发送短信异常:", e);
            return null;
        }
        return randomNumbers;
    }
}
