package com.lvpuhui.gic.wxapp.carbon.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.carbon.entity.GlCarbonAccumulateStatisticsDO;
import com.lvpuhui.gic.wxapp.homepage.dto.CarbonAccumulateProjectDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 碳汇累积统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface GlCarbonAccumulateStatisticsDao extends BaseMapper<GlCarbonAccumulateStatisticsDO> {

    /**
     * 获取累积统计数据
     */
    List<CarbonAccumulateProjectDto> getAccumulateStatistics(@Param("batchNo")String batchNo);

    /**
     * 获取累积和昨天累积数据
     */
    CarbonAccumulateProjectDto getAccumulateAndYesterdayStatistics();

}
