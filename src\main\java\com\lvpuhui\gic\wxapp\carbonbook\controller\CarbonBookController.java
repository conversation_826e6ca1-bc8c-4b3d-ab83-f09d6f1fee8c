package com.lvpuhui.gic.wxapp.carbonbook.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbonbook.dto.*;
import com.lvpuhui.gic.wxapp.carbonbook.service.CarbonBookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("")
@Slf4j
public class CarbonBookController {

    @Resource
    CarbonBookService carbonBookService;

    /**
     * 碳账本
     * @param mobileSha256 手机号
     */
    @GetMapping("/carbon_books")
    public R<CarbonBooksVo> carbonBooks2(String mobileSha256){
        return R.ok(carbonBookService.getCarbonBooks2(mobileSha256));
    }

    /**
     * 减排明细
     */
    @GetMapping("/emission_scene_details")
    public R<SceneTotalVo> emissionSceneDetails2(String mobileSha256, Integer sceneId, Integer page, Integer size){
        return R.ok(carbonBookService.getSceneDetails2(mobileSha256,sceneId,page,size));
    }

    /**
     * 获取我的累计减排量
     * GET /my_emission
     * 接口ID：18797289
     * 接口地址：https://www.apifox.cn/web/project/929917/apis/api-18797289
     * @param mobileSha256
     * @return
     */
    @GetMapping("/my_emission")
    public R<PointsEmission> myEmission(String mobileSha256){
        return R.ok(carbonBookService.myPointsEmission(mobileSha256));
    }

    /**
     * 碳账本接口
     */
    @GetMapping("/carbon_books_mobile")
    public R<CarbonBooksNewVo> carbonBooksMobile(){
        return R.ok(carbonBookService.carbonBooksMobile());
    }

    /**
     * 碳账本减排明细接口
     */
    @GetMapping("/carbon_books_mobile_detail")
    public R<CarbonBooksDetailNewVo> carbonBooksMobileDetail(Integer sceneId, Integer page, Integer size){
        return R.ok(carbonBookService.carbonBooksMobileDetail(sceneId,page,size));
    }
}
