package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lvpuhui.gic.wxapp.homepage.dao.GlCompanyDao;
import com.lvpuhui.gic.wxapp.homepage.entity.GlCompany;
import com.lvpuhui.gic.wxapp.homepage.service.GlCompanyService;
import org.springframework.stereotype.Service;

/**
 * 企业表(GlCompany)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 14:41:15
 */
@Service("glCompanyService")
public class GlCompanyServiceImpl extends ServiceImpl<GlCompanyDao, GlCompany> implements GlCompanyService {
    Cache<String,GlCompany> tenantIdLruCache = CacheUtil.newLRUCache(200);
    Cache<String,GlCompany> idLruCache = CacheUtil.newLRUCache(200);

    @Override
    public GlCompany getCacheCompanyByTenantId(Long tenantId) {
        GlCompany glCompany = tenantIdLruCache.get(String.valueOf(tenantId));
        if(glCompany != null){
            return glCompany;
        }
        synchronized (this) {
            glCompany = getOne(new LambdaQueryWrapper<GlCompany>().eq(GlCompany::getTenantId,tenantId));
        }
        if(glCompany !=null){
            tenantIdLruCache.put(String.valueOf(tenantId),glCompany);
            return glCompany;
        }
        return null;
    }

    @Override
    public GlCompany getCacheCompanyById(Long id) {
        GlCompany glCompany = idLruCache.get(String.valueOf(id));
        if(glCompany != null){
            return glCompany;
        }
        synchronized (this) {
            glCompany = getOne(new LambdaQueryWrapper<GlCompany>().eq(GlCompany::getId,id));
        }
        if(glCompany !=null){
            idLruCache.put(String.valueOf(id),glCompany);
            return glCompany;
        }
        return null;
    }
}