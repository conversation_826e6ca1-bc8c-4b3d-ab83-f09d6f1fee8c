package com.lvpuhui.gic.wxapp.carbonbook.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.function.Function;

/**
 * 碳账本名称枚举
 * <AUTHOR>
 * @since 2023年07月11日 11:30:00
 */
@Getter
@AllArgsConstructor
public enum CarbonBookTitleEnum {

    XX("减排新秀",score -> score < 10),
    XS( "减排小生",score -> score >=10 && score < 20),
    DR( "减排达人",score -> score >=20 && score < 50),
    XW( "减排学委",score -> score >=50 && score < 100),
    ZJ( "减排专家",score -> score >=100 && score < 1000),
    JS( "减排教授",score -> score > 1000),
    ;

    private String desc;

    private Function<Double,Boolean> compute;

    public static CarbonBookTitleEnum getTitle(double emissionKg) {
        for (CarbonBookTitleEnum type : values()) {
            if (type.compute.apply(emissionKg)) {
                return Optional.of(type).orElse(CarbonBookTitleEnum.XX);
            }
        }
        return CarbonBookTitleEnum.XX;
    }
}
