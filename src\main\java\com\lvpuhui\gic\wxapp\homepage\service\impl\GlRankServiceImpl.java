package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankSnapshotDao;
import com.lvpuhui.gic.wxapp.homepage.dto.RankAccumulateList;
import com.lvpuhui.gic.wxapp.homepage.dto.RankAccumulateListDto;
import com.lvpuhui.gic.wxapp.homepage.dto.RankConfigDto;
import com.lvpuhui.gic.wxapp.homepage.dto.RankInviteList;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRank;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRankSnapshot;
import com.lvpuhui.gic.wxapp.homepage.service.GlRankService;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.my.entity.GlUserFriends;
import com.lvpuhui.gic.wxapp.my.service.GlUserFriendsService;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import com.lvpuhui.gic.wxapp.pointexchange.dao.GlGoodsDao;
import com.lvpuhui.gic.wxapp.pointexchange.entity.GlGoods;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 累积排行榜 服务实现类
 * <AUTHOR>
 * @since 2022-07-25
 */
@Service
public class GlRankServiceImpl extends ServiceImpl<GlRankDao, GlRank> implements GlRankService {

    @Resource
    private GlGoodsDao glGoodsDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;
    @Resource
    private GlUserFriendsService glUserFriendsService;
    @Resource
    private GlUserService glUserService;
    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlRankSnapshotDao glRankSnapshotDao;

    @Resource
    private GlRankDao glRankDao;


    @Override
    public RankAccumulateList accumulateRank(RankAccumulateListDto rankAccumulateListDto) {
        // 查询用户
        GlUser glUser = glUserDao.selectById(UserUtils.getUserId());
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        rankAccumulateListDto.setMobileSha256(UserUtils.getMobileSha256());

        RankAccumulateList rankAccumulateList = new RankAccumulateList();
        rankAccumulateList.setIsEnd(false);

        RankConfigDto rankConfig = glAppletConfigService.getRankConfig();
        RankConfigDto.CumulativeBean configCumulative = rankConfig.getCumulative();

        rankAccumulateList.setTop(configCumulative.getTop());
        rankAccumulateList.setEndTime(configCumulative.getEndTime());

        Long goodsId = Long.parseLong(configCumulative.getCommodityId());
        GlGoods glGoods = glGoodsDao.selectById(goodsId);
        if(Objects.nonNull(glGoods)){
            rankAccumulateList.setGoodsName(glGoods.getName());
        }
        Integer top = rankConfig.getCumulative().getTop();
        String endDate = configCumulative.getEndTime();
        DateTime dateTime = DateUtil.parseDateTime(endDate);
        if(DateUtil.date().isAfter(dateTime)){
            rankAccumulateList.setIsEnd(true);
            // 查询快照获取前20累积排行
            List<RankAccumulateList.AccumulateRankUser> accumulateRankUsers = glRankSnapshotDao.getSnapshotRankUserInfo(configCumulative.getTop());
            if(CollUtil.isEmpty(accumulateRankUsers)){
                return rankAccumulateList;
            }
            LambdaQueryWrapper<GlRankSnapshot> glRankSnapshotQuery = Wrappers.lambdaQuery();
            glRankSnapshotQuery.orderByDesc(GlRankSnapshot::getRank);
            glRankSnapshotQuery.last("limit 1");
            GlRankSnapshot glRankLast = glRankSnapshotDao.selectOne(glRankSnapshotQuery);
            accumulateRankUsers.forEach(yesterdayRankUser -> {
                if(rankAccumulateListDto.getMobileSha256().equals(yesterdayRankUser.getMobileSha256())){
                    if(Objects.nonNull(yesterdayRankUser.getRank())){
                        if(yesterdayRankUser.getRank() > top){
                            yesterdayRankUser.setRadio(CalcUtil.calcRankRadio(yesterdayRankUser.getCityRank(),glRankLast.getCityRank()));
                        }else {
                            yesterdayRankUser.setRadio(CalcUtil.calcRankRadio(yesterdayRankUser.getRank(),glRankLast.getCityRank()));
                        }
                    }
                    rankAccumulateList.setCurrentUser(yesterdayRankUser);
                }
            });
            rankAccumulateList.setAccumulateRankUsers(accumulateRankUsers);
            if(Objects.isNull(rankAccumulateList.getCurrentUser())){
                // 查询当前用户的排行
                RankAccumulateList.AccumulateRankUser currentUser = glRankSnapshotDao.getRankUserInfoBySha256(rankAccumulateListDto.getMobileSha256());
                rankAccumulateList.setCurrentUser(currentUser);
            }
            if(Objects.isNull(rankAccumulateList.getCurrentUser())){
                RankAccumulateList.AccumulateRankUser currentUser = new RankAccumulateList.AccumulateRankUser();
                currentUser.setMobileSha256(glUser.getMobileSha256());
                currentUser.setNickName(glUser.getNickName());
                currentUser.setAvatarUrl(glUser.getAvatarUrl());
                rankAccumulateList.setCurrentUser(currentUser);
            }
            return rankAccumulateList;
        }
        LambdaQueryWrapper<GlRank> glRankLambdaQueryWrapper = Wrappers.lambdaQuery();
        glRankLambdaQueryWrapper.orderByDesc(GlRank::getRank);
        glRankLambdaQueryWrapper.last("limit 1");
        GlRank glRankLast = glRankDao.selectOne(glRankLambdaQueryWrapper);
        // 获取前20累积排行
        List<RankAccumulateList.AccumulateRankUser> accumulateRankUsers = baseMapper.getAccumulateRankUserInfo(configCumulative.getTop());
        if(CollUtil.isEmpty(accumulateRankUsers)){
            accumulateRankUsers = new ArrayList<>();
        }
        accumulateRankUsers.forEach(yesterdayRankUser -> {
            if(rankAccumulateListDto.getMobileSha256().equals(yesterdayRankUser.getMobileSha256())){
                if(Objects.nonNull(yesterdayRankUser.getRank())){
                    if(yesterdayRankUser.getRank() > top){
                        yesterdayRankUser.setRadio(CalcUtil.calcRankRadio(yesterdayRankUser.getCityRank(),glRankLast.getCityRank()));
                    }else {
                        yesterdayRankUser.setRadio(CalcUtil.calcRankRadio(yesterdayRankUser.getRank(),glRankLast.getCityRank()));
                    }
                }
                rankAccumulateList.setCurrentUser(yesterdayRankUser);
            }
        });
        rankAccumulateList.setAccumulateRankUsers(accumulateRankUsers);
        if(Objects.isNull(rankAccumulateList.getCurrentUser())){
            // 查询当前用户的排行
            RankAccumulateList.AccumulateRankUser currentUser = baseMapper.getRankUserInfoBySha256(rankAccumulateListDto.getMobileSha256());
            if(Objects.nonNull(currentUser.getRank())){
                if(currentUser.getRank() > top){
                    currentUser.setRadio(CalcUtil.calcRankRadio(currentUser.getCityRank(),glRankLast.getCityRank()));
                }else {
                    currentUser.setRadio(CalcUtil.calcRankRadio(currentUser.getRank(),glRankLast.getCityRank()));
                }
            }
            rankAccumulateList.setCurrentUser(currentUser);
        }
        if(Objects.isNull(rankAccumulateList.getCurrentUser())){
            RankAccumulateList.AccumulateRankUser currentUser = new RankAccumulateList.AccumulateRankUser();
            currentUser.setMobileSha256(glUser.getMobileSha256());
            currentUser.setNickName(glUser.getNickName());
            currentUser.setAvatarUrl(glUser.getAvatarUrl());
            rankAccumulateList.setCurrentUser(currentUser);
        }
        return rankAccumulateList;
    }

    @Override
    public RankInviteList getRankInviteList(RankAccumulateListDto rankDto) {
        rankDto.setMobileSha256(UserUtils.getMobileSha256());
        RankInviteList rankInviteList = new RankInviteList();
        RankConfigDto rankConfig = glAppletConfigService.getRankConfig();
        RankConfigDto.InvitationBean invitationBean = rankConfig.getInvitation();
        rankInviteList.setPoints(invitationBean.getPoints());
        rankInviteList.setMaximumPoints(invitationBean.getMaximumPoints());

        List<RankInviteList.InviteRankUser> inviteRankUsers = Lists.newArrayList();
        List<GlUserFriends> glUserFriends = glUserFriendsService.list(new LambdaQueryWrapper<GlUserFriends>()
                .eq(GlUserFriends::getMobileSha256,rankDto.getMobileSha256())
                .eq(GlUserFriends::getDeleted,0));

        inviteRankUsers.add(build(rankDto.getMobileSha256()));
        glUserFriends.forEach(u->{
            String sha256 = u.getFriendsSha256();
            RankInviteList.InviteRankUser inviteRankUser = build(sha256);
            if(Objects.nonNull(inviteRankUser)){
                inviteRankUsers.add(inviteRankUser);
            }
        });
        List<RankInviteList.InviteRankUser> sortList = inviteRankUsers.stream()
                .sorted(Comparator.comparing(RankInviteList.InviteRankUser::getEmission).reversed())
                .collect(Collectors.toList());
        AtomicLong atomicLong = new AtomicLong(1);
        AtomicReference<RankInviteList.InviteRankUser> current = new AtomicReference<>();
        sortList.forEach(i->{
            i.setRank(atomicLong.get());
            atomicLong.incrementAndGet();
            if(rankDto.getMobileSha256().equals(i.getMobileSha256())){
                current.set(i);
            }
        });
        rankInviteList.setCurrentUser(current.get());
        sortList.remove(current.get());
        rankInviteList.setInviteRankUsers(sortList);
        return rankInviteList;
    }
    RankInviteList.InviteRankUser build(String mobileSha256){
        RankInviteList.InviteRankUser inviteRankUser = new RankInviteList.InviteRankUser();
        GlUser gluser = glUserService.getOne(new LambdaQueryWrapper<GlUser>().eq(GlUser::getMobileSha256,mobileSha256).last(" LIMIT 1"));
        if(Objects.isNull(gluser)){
            return null;
        }
        GlRank glRank = getOne(new LambdaQueryWrapper<GlRank>().eq(GlRank::getMobileSha256,mobileSha256).last(" LIMIT 1"));
        if(glRank !=null){
            inviteRankUser.setRank(glRank.getRank());
            inviteRankUser.setEmission(glRank.getEmission());
        }else {
            inviteRankUser.setRank(0L);
            inviteRankUser.setEmission(0d);
        }
        inviteRankUser.setAvatarUrl(gluser.getAvatarUrl());
        inviteRankUser.setNickName(gluser.getNickName());
        inviteRankUser.setMobileSha256(mobileSha256);
        return inviteRankUser;
    }

}
