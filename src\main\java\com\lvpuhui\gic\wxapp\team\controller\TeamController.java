package com.lvpuhui.gic.wxapp.team.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.team.dto.*;
import com.lvpuhui.gic.wxapp.team.service.TeamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 战队服务
 * <AUTHOR>
 * @since 2023年05月24日 15:03:00
 */
@RestController
@RequestMapping("/team")
@Tags(value = {@Tag(name = "战队服务API 1.0")})
public class TeamController {

    @Resource
    private TeamService teamService;

    /**
     * 我的战队信息接口
     */
    @Operation(summary = "我的战队信息接口", description = "我的战队信息接口", tags = { "战队服务API 1.0" })
    @GetMapping("/me_team")
    public R<TeamMeVo> meTeam(){
        TeamMeVo meTeamVo = teamService.meTeam();
        return R.ok(meTeamVo);
    }

    /**
     * 创建战队接口
     */
    @Operation(summary = "创建战队接口", description = "创建战队接口", tags = { "战队服务API 1.0" })
    @PostMapping("/create_team")
    public R<String> createTeam(@RequestBody @Valid TeamCreateDto teamCreateDto){
        teamService.createTeam(teamCreateDto);
        return R.ok(null);
    }

    /**
     * 战队列表接口
     */
    @Operation(summary = "战队列表接口", description = "战队列表接口", tags = { "战队服务API 1.0" })
    @PostMapping("/team_list")
    public R<List<TeamListVo>> teamList(@RequestBody TeamListDto teamListDto){
        List<TeamListVo> teamListVos = teamService.teamList(teamListDto);
        return R.ok(teamListVos);
    }

    /**
     * 战队列表-战队详情接口
     */
    @Operation(summary = "战队列表-战队详情接口", description = "战队列表-战队详情接口", tags = { "战队服务API 1.0" })
    @GetMapping("/team_list_detail")
    public R<TeamListDetailVo> teamListDetail(@RequestParam("teamId") Long teamId){
        TeamListDetailVo teamListDetailVo = teamService.teamListDetail(teamId);
        return R.ok(teamListDetailVo);
    }

    /**
     * 我的战队信息接口
     */
    @Operation(summary = "我的战队信息接口", description = "我的战队信息接口", tags = { "战队服务API 1.0" })
    @GetMapping("/team_detail")
    public R<TeamDetailVo> teamDetail(){
        TeamDetailVo teamDetailVo = teamService.teamDetail();
        return R.ok(teamDetailVo);
    }

    /**
     * 战队排行榜接口
     */
    @Operation(summary = "战队排行榜接口", description = "战队排行榜接口", tags = { "战队服务API 1.0" })
    @GetMapping("/team_rank")
    public R<TeamRankVo> teamRank(){
        TeamRankVo teamRankVo = teamService.teamRank();
        return R.ok(teamRankVo);
    }

    /**
     * 对内排行榜接口
     */
    @Operation(summary = "队内排行榜接口", description = "队内排行榜接口", tags = { "战队服务API 1.0" })
    @GetMapping("/team_member_rank")
    public R<TeamMemberRankVo> teamMemberRank(){
        TeamMemberRankVo teamMemberRankVo = teamService.teamMemberRank();
        return R.ok(teamMemberRankVo);
    }

    /**
     * 加入战队接口
     */
    @Operation(summary = "加入战队接口", description = "加入战队接口", tags = { "战队服务API 1.0" })
    @PostMapping("/team_join")
    public R<String> teamJoin(@RequestBody @Valid TeamJoinDto teamJoinDto){
        teamService.teamJoin(teamJoinDto);
        return R.ok(null);
    }

    /**
     * 退出战队接口
     */
    @Operation(summary = "退出战队接口", description = "退出战队接口", tags = { "战队服务API 1.0" })
    @GetMapping("/team_exit")
    public R<String> teamExit(@RequestParam("teamId") Long teamId){
        teamService.teamExit(teamId);
        return R.ok(null);
    }

    /**
     * 修改战队接口
     */
    @Operation(summary = "修改战队接口", description = "修改战队接口", tags = { "战队服务API 1.0" })
    @PostMapping("/update_team")
    public R<String> updateTeam(@RequestBody @Valid TeamUpdateDto teamUpdateDto){
        teamService.updateTeam(teamUpdateDto);
        return R.ok(null);
    }

    @Operation(summary = "战队变更搜索接口", description = "战队变更搜索接口", tags = { "战队服务API 1.0" })
    @GetMapping("/team_change_search")
    public R<TeamChangeSearchVo> teamChangeSearch(@RequestParam("mobile") @Schema(description = "手机号") String mobile){
        TeamChangeSearchVo teamChangeSearchVo = teamService.teamChangeSearch(mobile);
        return R.ok(teamChangeSearchVo);
    }

    @Operation(summary = "战队变更接口", description = "战队变更接口", tags = { "战队服务API 1.0" })
    @GetMapping("/team_change_creator")
    public R<String> teamChangeCreator(@RequestParam("changeUserId") @Schema(description = "更换的队长用户ID") Long changeUserId){
        teamService.teamChangeCreator(changeUserId);
        return R.ok(null);
    }

    @Operation(summary = "战队加V申请接口", description = "战队加V申请接口", tags = { "战队服务API 1.0" })
    @PostMapping("/team_certification_apply")
    public R<String> teamCertificationApply(@RequestBody @Valid TeamCertificationApplyDto teamCertificationApplyDto){
        teamService.teamCertificationApply(teamCertificationApplyDto);
        return R.ok(null);
    }

    @Operation(summary = "战队认证详情接口", description = "战队认证详情接口", tags = { "战队服务API 1.0" })
    @GetMapping("/team_certification_detail")
    public R<TeamCertificationDetailVo> teamCertificationDetail(){
        TeamCertificationDetailVo teamCertificationDetailVo = teamService.teamCertificationDetail();
        return R.ok(teamCertificationDetailVo);
    }
}
