package com.lvpuhui.gic.wxapp.screen.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 屏幕碳账本请求DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "屏幕碳账本请求DTO")
public class ScreenCarbonBooksMobileDto {

    /**
     * 用户id(sha256)
     */
    @Schema(description = "用户id(sha256)", required = true)
    @NotBlank(message = "请您先授权")
    private String mobileSha256;
}
