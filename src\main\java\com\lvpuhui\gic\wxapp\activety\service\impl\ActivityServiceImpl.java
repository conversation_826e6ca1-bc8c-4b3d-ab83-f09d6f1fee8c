package com.lvpuhui.gic.wxapp.activety.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.lvpuhui.gic.wxapp.activety.dao.GlActivityDao;
import com.lvpuhui.gic.wxapp.activety.dao.GlActivityDetailDao;
import com.lvpuhui.gic.wxapp.activety.dao.GlActivityRegistrationDao;
import com.lvpuhui.gic.wxapp.activety.dao.GlNewsDao;
import com.lvpuhui.gic.wxapp.activety.dto.*;
import com.lvpuhui.gic.wxapp.activety.entity.GlActivity;
import com.lvpuhui.gic.wxapp.activety.entity.GlActivityDetail;
import com.lvpuhui.gic.wxapp.activety.entity.GlActivityRegistration;
import com.lvpuhui.gic.wxapp.activety.enums.ActivityEnums;
import com.lvpuhui.gic.wxapp.activety.service.ActivityService;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.base.service.GlUploadFileService;

import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ActivityServiceImpl implements ActivityService {


    @Resource
    private GlActivityDao glActivityDao;

    @Resource
    private GlActivityDetailDao glActivityDetailDao;

    @Resource
    private GlActivityRegistrationDao glActivityRegistrationDao;

    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlUploadFileService glUploadFileService;

    @Resource
    private GlNewsDao glNewsDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;





    @Override
    public void publishActivity(PublishActivityDto publishActivityDto) {
        if (Objects.isNull(publishActivityDto)){
            throw new GicWxAppException("参数为空");
        }
        GlActivity glActivity = new GlActivity();
        glActivity.setActivityDescription(publishActivityDto.getActivityDescription());
        if(Objects.nonNull(publishActivityDto.getActivityDetail())){
            glActivity.setActivityDetail(publishActivityDto.getActivityDetail());
        }
        glActivity.setActivityLocation(publishActivityDto.getActivityLocation());
        glActivity.setActivityStartTime(publishActivityDto.getActivityStartTime());
        glActivity.setCreated(LocalDateTime.now());
        glActivity.setLatitude(publishActivityDto.getLatitude());
        glActivity.setLongitude(publishActivityDto.getLongitude());
        glActivity.setActivityEndTime(publishActivityDto.getActivityEndTime());
        glActivity.setName(publishActivityDto.getName());
        glActivity.setCreator(UserUtils.getUserId());
        glActivity.setLogo(publishActivityDto.getLogo());
        glActivity.setStatus(ActivityEnums.TO_BE_REVIEWED.getCode());
        glActivity.setRegistrationDeadline(publishActivityDto.getRegistrationDeadline());
        int index = glActivityDao.insert(glActivity);
        if(index < 0){
            throw new GicWxAppException("保存失败");
        }
        //保存图片
        Long activityId = glActivity.getId();
        if(CollUtil.isNotEmpty(publishActivityDto.getUrlList())){
            for (String item: publishActivityDto.getUrlList()) {
                GlActivityDetail glActivityDetail = new GlActivityDetail();
                glActivityDetail.setActivityId(activityId);
                glActivityDetail.setDetailImgUrl(item);
                glActivityDetail.setCreated(LocalDateTime.now());
                glActivityDetail.setCreator(UserUtils.getUserId());
                glActivityDetailDao.insert(glActivityDetail);
            }
            glUploadFileService.useFile(publishActivityDto.getUrlList());
        }
    }


    @Override
    public List<ActivityListVo> queryAllActivityList() {
        String imagePrefix = glAppletConfigService.getImagePrefix();
        LocalDateTime startTime = LocalDateTime.now();
        List<ActivityListVo> list = glActivityDao.queryAllActivityList(startTime);
        List<ActivityListVo> newList = new ArrayList<>();
        Long userId = UserUtils.getUserId();
        for (ActivityListVo item:list) {
            String loge = imagePrefix+item.getLogo();
            item.setLogo(loge);
            GlActivityRegistration glActivityRegistration = glActivityRegistrationDao.selectByUserIdAndActivityId(userId,item.getId());
            if(Objects.nonNull(glActivityRegistration)){
                item.setStatusCode(ActivityEnums.SIGN_UP.getCode());
            }
            newList.add(item);
        }
        return newList;
    }

    @Override
    public ActivityDetailsVo getActivityDetails(Long activityId) {
        String imagePrefix = glAppletConfigService.getImagePrefix();

        LocalDateTime startTime = LocalDateTime.now();
        GlActivity glActivity  = glActivityDao.selectById(activityId);
        ActivityDetailsVo activityDetailsVo = new ActivityDetailsVo();
        activityDetailsVo.setActivityDescription(glActivity.getActivityDescription());
        activityDetailsVo.setActivityDetail(glActivity.getActivityDetail());
        activityDetailsVo.setActivityLocation(glActivity.getActivityLocation());
        activityDetailsVo.setLogo(imagePrefix+glActivity.getLogo());
        activityDetailsVo.setHashLogo(glActivity.getLogo());
        activityDetailsVo.setLongitude(glActivity.getLongitude());
        activityDetailsVo.setRegistrationsCount(glActivity.getRegistrationsCount());
        activityDetailsVo.setLatitude(glActivity.getLatitude());
        activityDetailsVo.setCreator(glActivity.getCreator());
        activityDetailsVo.setRegistrationDeadline(glActivity.getRegistrationDeadline());
        activityDetailsVo.setActivityStartTime(glActivity.getActivityStartTime());
        activityDetailsVo.setActivityEndTime(glActivity.getActivityEndTime());
        activityDetailsVo.setName(glActivity.getName());
        GlUser user = glUserDao.selectById(glActivity.getCreator());
        activityDetailsVo.setUserName(user.getNickName());
        activityDetailsVo.setUserUrl(user.getAvatarUrl());
        activityDetailsVo.setCreated(glActivity.getCreated());
        List<String> url = glActivityDetailDao.selectByActivityId(activityId);
        List<String> activeImgList = new ArrayList<>();
        for (String item: url) {
            String urlImg = imagePrefix+item;
            activeImgList.add(urlImg);
        }
        activityDetailsVo.setUrlList(activeImgList);
        activityDetailsVo.setUrlImageHash(url);
        //查询是否报名
        GlActivityRegistration glActivityRegistration = glActivityRegistrationDao.selectByUserIdAndActivityId(UserUtils.getUserId(),activityId);
        if(Objects.nonNull(glActivityRegistration)){
            activityDetailsVo.setIsSignUp(8);
            activityDetailsVo.setStatusDescription("待参加");
            if(startTime.isAfter(glActivity.getActivityEndTime())){
                activityDetailsVo.setIsSignUp(5);
                activityDetailsVo.setStatusDescription("已结束");
            }
            if(ActivityEnums.END.getCode().equals(glActivity.getStatus())){
                activityDetailsVo.setIsSignUp(5);
                activityDetailsVo.setStatusDescription("已结束");
            }
        }else {
            //如果没有报名并且审核通过
            if(ActivityEnums.PASS.getCode().equals(glActivity.getStatus())){
                if(startTime.isAfter(glActivity.getActivityEndTime())){
                    activityDetailsVo.setIsSignUp(5);
                    activityDetailsVo.setStatusDescription("已结束");
                }else if (startTime.isAfter(glActivity.getRegistrationDeadline())){
                    activityDetailsVo.setIsSignUp(7);
                    activityDetailsVo.setStatusDescription("报名已截止");
                }else {
                    activityDetailsVo.setIsSignUp(6);
                    activityDetailsVo.setStatusDescription("报名");
                }
            }
            if(ActivityEnums.END.getCode().equals(glActivity.getStatus())){
                activityDetailsVo.setIsSignUp(5);
                activityDetailsVo.setStatusDescription("已结束");
            }
            if(ActivityEnums.TO_BE_REVIEWED.getCode().equals(glActivity.getStatus())){
                activityDetailsVo.setIsSignUp(0);
                activityDetailsVo.setStatusDescription("待审核");
            }
            if(ActivityEnums.REFUSE.getCode().equals(glActivity.getStatus())){
                activityDetailsVo.setIsSignUp(2);
                activityDetailsVo.setStatusDescription("审核未通过");
            }
        }
        return activityDetailsVo;
    }

    @Override
    public List<ActivityListVo> getMyActivityList() {
        String imagePrefix = glAppletConfigService.getImagePrefix();

        Long userId = UserUtils.getUserId();
        LocalDateTime startTime = LocalDateTime.now();
        List<GlActivity> mySignUpList =new ArrayList<>();
        //查询我报名的活动
        List<GlActivityRegistration> signUpList = glActivityRegistrationDao.selectByUserId(userId);
        if(signUpList.size() > 0){
            List<Long> activityIdList = signUpList.stream().map(GlActivityRegistration:: getActivityId).collect(Collectors.toList());
            for (Long item: activityIdList) {
                GlActivity glActivity = glActivityDao.selectById(item);
                mySignUpList.add(glActivity);
            }
        }
        //查我创建的活动
        List<GlActivity> myCreateList = glActivityDao.selectByUserId(userId);
        if(myCreateList.size() > 0){
            mySignUpList.addAll(myCreateList);
        }
        if(mySignUpList.size() <= 0){
            return new ArrayList<>();
        }
        //合并我创建的活动与我报名的集合并去重
        List<GlActivity> newList = mySignUpList.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(GlActivity::getId))
                ), ArrayList::new));
        List<ActivityListVo> list = new ArrayList<>();
        for (GlActivity item: newList) {
            ActivityListVo activityListVo = new ActivityListVo();
            activityListVo.setId(item.getId());
            activityListVo.setActivityDescription(item.getActivityDescription());
            activityListVo.setActivityLocation(item.getActivityLocation());
            activityListVo.setActivityStartTime(item.getActivityStartTime());
            activityListVo.setActivityEndTime(item.getActivityEndTime());
            activityListVo.setName(item.getName());
            activityListVo.setLogo(imagePrefix+item.getLogo());
            activityListVo.setRegistrationDeadline(item.getRegistrationDeadline());
            activityListVo.setRegistrationsCount(item.getRegistrationsCount());
            //判断是否是自己创建的活动
            if(item.getCreator().equals(userId)){
                //待审核
                if(item.getStatus().equals(ActivityEnums.TO_BE_REVIEWED.getCode())){
                    activityListVo.setStatusCode(ActivityEnums.TO_BE_REVIEWED.getCode());
                    activityListVo.setStatusDescription("待审核");
                }
                //审核未通过
                if(item.getStatus().equals(ActivityEnums.REFUSE.getCode())){
                    activityListVo.setStatusCode(ActivityEnums.REFUSE.getCode());
                    activityListVo.setStatusDescription("审核未通过");
                }
                //已结束
                if(item.getStatus().equals(ActivityEnums.END.getCode())){
                    activityListVo.setStatusCode(ActivityEnums.END.getCode());
                    activityListVo.setStatusDescription("已结束");
                }
                if(item.getStatus().equals(ActivityEnums.PASS.getCode())) {
                    //查询他是否报名自己创建的活动
                    GlActivityRegistration glActivityRegistration = glActivityRegistrationDao.selectByUserIdAndActivityId(userId,item.getId());
                    //不为空说明报名了
                    if(Objects.nonNull(glActivityRegistration)){
                        //判断是否在活动之前，之前的话显示待参加
                        if(startTime.isBefore(item.getActivityEndTime())){
                            activityListVo.setStatusCode(ActivityEnums.WAIT_JOIN.getCode());
                            activityListVo.setStatusDescription("待参加");
                        }else {
                            activityListVo.setStatusCode(ActivityEnums.END.getCode());
                            activityListVo.setStatusDescription("已结束");
                        }
                    }else {
                        if(startTime.isAfter(item.getActivityEndTime())){
                            activityListVo.setStatusCode(ActivityEnums.END.getCode());
                            activityListVo.setStatusDescription("已结束");
                        }else if (startTime.isAfter(item.getRegistrationDeadline())){
                            activityListVo.setStatusCode(ActivityEnums.SIGN_UP_END.getCode());
                            activityListVo.setStatusDescription("报名已截止");
                        }else {
                            activityListVo.setStatusCode(ActivityEnums.SIGN_UP_ING.getCode());
                            activityListVo.setStatusDescription("报名中");
                        }

                    }
                }
            }else {
                //不是自己创建的活动,则一定为通过状态，并且已报名
                /**
                 * 1:在活动之前则为待参加
                 * 2：状态为已结束则显示已结束
                 * 3：状态为已结束
                 */
                if(startTime.isBefore(item.getActivityEndTime())){
                    activityListVo.setStatusCode(ActivityEnums.WAIT_JOIN.getCode());
                    activityListVo.setStatusDescription("待参加");
                }else {
                    activityListVo.setStatusCode(ActivityEnums.END.getCode());
                    activityListVo.setStatusDescription("已结束");
                }
            }
            list.add(activityListVo);
        }
        return list.stream().sorted(Comparator.comparing(ActivityListVo::getActivityStartTime).reversed()).collect(Collectors.toList());
    }

    @Override
    public void signUp(SignUpDto signUpDto) {
        Long userId = UserUtils.getUserId();
        GlActivityRegistration isExit = glActivityRegistrationDao.selectByUserIdAndActivityId(userId,signUpDto.getActivityId());
        if(Objects.nonNull(isExit)){
            throw new GicWxAppException("您已报名");
        }
        GlActivityRegistration glActivityRegistration = new GlActivityRegistration();
        glActivityRegistration.setActivityId(signUpDto.getActivityId());
        glActivityRegistration.setUserId(userId);
        glActivityRegistration.setCreated(LocalDateTime.now());
        glActivityRegistration.setRegistrationTime(LocalDateTime.now());
        glActivityRegistrationDao.insert(glActivityRegistration);
        GlActivity glActivity = glActivityDao.selectById(signUpDto.getActivityId());
        if(Objects.isNull(glActivity)){
            throw new GicWxAppException("活动不存在");
        }
        glActivity.setRegistrationsCount(glActivity.getRegistrationsCount()+1);
        glActivityDao.updateById(glActivity);
    }

    @Override
    public List<NewsListVo> newsList() {
        return glNewsDao.newsList();
    }

    @Override
    public void updateActity(UpdateDto updateDto) {
        GlActivityDetail glActivityDetail = new GlActivityDetail();
        GlActivity glActivity = new GlActivity();
        glActivity.setId(updateDto.getId());
        glActivity.setActivityDetail(updateDto.getActivityDetail());
        glActivity.setActivityDescription(updateDto.getActivityDescription());
        glActivity.setActivityLocation(updateDto.getActivityLocation());
        glActivity.setActivityStartTime(updateDto.getActivityStartTime());
        glActivity.setActivityEndTime(updateDto.getActivityEndTime());
        glActivity.setLongitude(updateDto.getLongitude());
        glActivity.setLatitude(updateDto.getLatitude());
        glActivity.setLogo(updateDto.getLogo());
        glActivity.setName(updateDto.getName());
        glActivity.setUpdated(LocalDateTime.now());
        glActivity.setRegistrationDeadline(updateDto.getRegistrationDeadline());
        glActivityDao.updateById(glActivity);
        //保存图片
        for (String item:updateDto.getUrlList()) {
            glActivityDetail.setDetailImgUrl(item);
            glActivityDetail.setActivityId(updateDto.getId());
            glActivityDetailDao.updateByActiceId(glActivityDetail);
        }
    }
}
