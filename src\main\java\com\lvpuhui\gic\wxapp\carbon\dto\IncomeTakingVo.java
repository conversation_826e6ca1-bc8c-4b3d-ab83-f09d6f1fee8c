package com.lvpuhui.gic.wxapp.carbon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "收入提现返回值")
public class IncomeTakingVo {

    /**
     * 可提现金额
     */
    @Schema(description = "可提现金额")
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 不可提现金额
     */
    @Schema(description = "不可提现金额")
    private BigDecimal notTakingAmount = BigDecimal.ZERO;

    /**
     * 可提现时间
     */
    @Schema(description = "可提现时间")
    private String takingDate;
}