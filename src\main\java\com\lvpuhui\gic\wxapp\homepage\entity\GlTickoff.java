package com.lvpuhui.gic.wxapp.homepage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 打卡数据 (用户扫码)(GlSceneCategory)实体类
 * <AUTHOR>
 * @since 2022年5月5日19:17:10
 */
@Data
public class GlTickoff extends Model<GlTickoff> {

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 打卡类别ID
     */
    private Long typeId;

    /**
     * 手机号码密文
     */
    private String mobileSha256;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 门店名称
     */
    private String outletsName;

    /**
     * 门店维度
     */
    private Double outletsLatitude;

    /**
     * 门店经度
     */
    private Double outletsLongitude;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 唯一标识(用于处理并发打卡导致刷积分问题)
     */
    private String onlyCerts;

    /**
     * 打卡位置地图类型 0:高德 1:百度
     */
    private Integer mapType;
}
