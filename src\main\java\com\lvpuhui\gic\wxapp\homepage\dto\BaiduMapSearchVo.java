package com.lvpuhui.gic.wxapp.homepage.dto;

import lombok.Data;

import java.util.List;

/**
 * 百度地图搜索返回值VO
 * 详细请查看官网api:https://lbsyun.baidu.com/index.php?title=webapi/guide/webservice-placeapi
 * <AUTHOR>
 * @since 2022年05月10日 15:56:00
 */
@Data
public class BaiduMapSearchVo {

    /**
     * 本次API访问状态，如果成功返回0，如果失败返回其他数字。（见服务状态码）
     */
    private Integer status;

    /**
     * 对API访问状态值的英文说明，如果成功返回"ok"，并返回结果字段，如果失败返回错误说明。
     */
    private String message;

    /**
     * 当返回结果为城市列表时，result_type=city_type
     * 当返回结果为普通poi时，result_type=poi_type
     * 当返回结果为行政区划数据时，result_type=region_type
     * 当返回结果为门址数据时，result_type=address_type
     */
    private String result_type;

    /**
     * 返回值对象集合
     */
    private List<ResultsBean> results;

    /**
     * 返回值对象
     */
    @Data
    public static class ResultsBean {

        /**
         * poi名称
         */
        private String name;

        /**
         * poi经纬度坐标
         */
        private LocationBean location;

        /**
         * poi地址信息
         */
        private String address;

        /**
         * 所属省份
         */
        private String province;

        /**
         * 所属城市
         */
        private String city;

        /**
         * 所属区县
         */
        private String area;

        /**
         * 街景图id
         */
        private String street_id;

        /**
         * poi电话信息
         */
        private String telephone;

        /**
         * 是否有详情页：1有，0没有
         */
        private Integer detail;

        /**
         * poi的唯一标示，可用于详情检索
         */
        private String uid;

        /**
         * poi经纬度坐标
         */
        @Data
        public static class LocationBean {
            /**
             * 纬度值
             */
            private double lat;

            /**
             * 经度值
             */
            private double lng;
        }
    }
}
