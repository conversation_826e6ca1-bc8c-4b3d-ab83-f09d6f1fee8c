package com.lvpuhui.gic.wxapp.carbonlife.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbonlife.dto.CarbonLifeVo;
import com.lvpuhui.gic.wxapp.carbonlife.service.CarbonLifeService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tags(value = {@Tag(name = "低碳生活1.0")})
@RestController
@RequestMapping("")
public class CarbonLifeController {

    @Resource
    private CarbonLifeService carbonLifeService;

    @PassToken
    @Operation(summary = "低碳生活接口", description = "低碳生活接口")
    @GetMapping("/carbon_life")
    public R<List<CarbonLifeVo>> carbonLife() {
        List<CarbonLifeVo> carbonLifeVos = carbonLifeService.carbonLife();
        return R.ok(carbonLifeVos);
    }
}
