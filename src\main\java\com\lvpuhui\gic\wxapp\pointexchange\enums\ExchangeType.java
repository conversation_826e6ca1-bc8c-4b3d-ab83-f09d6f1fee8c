package com.lvpuhui.gic.wxapp.pointexchange.enums;

import lombok.Getter;

/**
 * 兑换凭证状态枚举
 * <AUTHOR>
 * @since 2022年05月05日 18:20:00
 */
@Getter
public enum ExchangeType {
    NORMAL(0,"正常商品"),
    RANK(1,"排行商品"),

    ACCUMULATE_RANK(2,"累积排行商品"),
    ;

    private Integer state;

    private String describe;

    ExchangeType(Integer state, String describe) {
        this.state = state;
        this.describe = describe;
    }
}
