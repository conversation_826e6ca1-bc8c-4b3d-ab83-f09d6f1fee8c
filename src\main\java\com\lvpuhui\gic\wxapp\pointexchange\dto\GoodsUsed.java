package com.lvpuhui.gic.wxapp.pointexchange.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 已兑换商品列表PO
 * <AUTHOR>
 * @since 2022年05月06日 17:56:00
 */
@NoArgsConstructor
@Data
public class GoodsUsed {

    /**
     * 兑换码ID
     */
    private Long id;

    /**
     * 商品封面图
     */
    private String coverImage;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品价值
     */
    private String price;

    /**
     * 商品需要兑换的积分
     */
    private Integer point;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 兑换时间
     */
    private Date created;
}
