package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlQuestionAnswerRecordsDao;
import com.lvpuhui.gic.wxapp.homepage.dao.GlQuestionDao;
import com.lvpuhui.gic.wxapp.homepage.dto.QuestionFinish;
import com.lvpuhui.gic.wxapp.homepage.dto.QuestionFinishDto;
import com.lvpuhui.gic.wxapp.homepage.dto.QuestionList;
import com.lvpuhui.gic.wxapp.homepage.dto.QuestionListDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlQuestion;
import com.lvpuhui.gic.wxapp.homepage.entity.GlQuestionAnswerRecords;
import com.lvpuhui.gic.wxapp.homepage.enums.VideoState;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.homepage.service.GlQuestionService;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Available;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 小程序问卷 服务实现类
 * <AUTHOR>
 * @since 2022-06-13
 */
@Service
public class GlQuestionServiceImpl extends ServiceImpl<GlQuestionDao, GlQuestion> implements GlQuestionService {

    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlQuestionAnswerRecordsDao glQuestionAnswerRecordsDao;

    @Resource
    private GlPointsService glPointsService;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Override
    public List<QuestionList> questionList(QuestionListDto questionListDto) {
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,questionListDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        LambdaQueryWrapper<GlQuestion> glQuestionQuery = Wrappers.lambdaQuery();
        glQuestionQuery.eq(GlQuestion::getState, VideoState.PUBLISH.getState());
        glQuestionQuery.orderByDesc(GlQuestion::getSequence);
        List<GlQuestion> glQuestions = baseMapper.selectList(glQuestionQuery);
        if(CollUtil.isEmpty(glQuestions)){
            return Lists.newArrayList();
        }
        List<Long> questionIds = glQuestions.stream().map(GlQuestion::getId).collect(Collectors.toList());
        LambdaQueryWrapper<GlQuestionAnswerRecords> glQuestionAnswerRecordsQuery = Wrappers.lambdaQuery();
        glQuestionAnswerRecordsQuery.select(GlQuestionAnswerRecords::getAppletQuestionId);
        glQuestionAnswerRecordsQuery.in(GlQuestionAnswerRecords::getAppletQuestionId,questionIds);
        glQuestionAnswerRecordsQuery.eq(GlQuestionAnswerRecords::getMobileSha256,glUser.getMobileSha256());
        List<Object> questionIdObjects = glQuestionAnswerRecordsDao.selectObjs(glQuestionAnswerRecordsQuery);
        List<Long> questionRecordVIds = Lists.newArrayListWithCapacity(questionIds.size());
        if(CollUtil.isNotEmpty(questionIdObjects)){
            List<Long> videoExitsIds = questionIdObjects.stream().map(o -> (Long) o).collect(Collectors.toList());
            questionRecordVIds.addAll(videoExitsIds);
        }
        String imagePrefix = glAppletConfigService.getImagePrefix();
        List<QuestionList> questionLists = Lists.newArrayListWithCapacity(glQuestions.size());
        for (GlQuestion glQuestion : glQuestions) {
            QuestionList questionList = new QuestionList();
            questionList.setId(glQuestion.getId());
            questionList.setJumpUrl(glQuestion.getJumpUrl());
            questionList.setCoverImage(imagePrefix + glQuestion.getCoverImage());
            questionList.setQuestionId(glQuestion.getQuestionId());
            questionList.setName(glQuestion.getName());
            questionList.setPoint(glQuestion.getPoint());
            questionList.setAvailable(Available.NO.getState());
            if(questionRecordVIds.contains(glQuestion.getId())){
                questionList.setAvailable(Available.YES.getState());
            }
            questionLists.add(questionList);
        }
        return questionLists;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QuestionFinish questionFinish(QuestionFinishDto questionFinishDto) {
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,questionFinishDto.getMobileSha256());
        glUserQuery.eq(GlUser::getOpenId,questionFinishDto.getOpenId());
        glUserQuery.last("limit 1");
        GlUser glUser = glUserDao.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        GlQuestion glQuestion = baseMapper.selectById(questionFinishDto.getAppletQuestionId());
        if(Objects.isNull(glQuestion)
                || !VideoState.PUBLISH.getState().equals(glQuestion.getState())){
            throw new GicWxAppException("您填写的问卷已取消或不存在");
        }
        LambdaQueryWrapper<GlQuestionAnswerRecords> glQuestionAnswerRecordsQuery = Wrappers.lambdaQuery();
        glQuestionAnswerRecordsQuery.eq(GlQuestionAnswerRecords::getAppletQuestionId,questionFinishDto.getAppletQuestionId());
        glQuestionAnswerRecordsQuery.eq(GlQuestionAnswerRecords::getMobileSha256,glUser.getMobileSha256());
        Integer recordCount = glQuestionAnswerRecordsDao.selectCount(glQuestionAnswerRecordsQuery);
        if(Objects.nonNull(recordCount) && recordCount > 0){
            throw new GicWxAppException("您已填写过此问卷,感谢您的支持！");
        }
        GlQuestionAnswerRecords glQuestionAnswerRecords = new GlQuestionAnswerRecords();
        BeanUtil.copyProperties(questionFinishDto,glQuestionAnswerRecords);
        glQuestionAnswerRecords.setPoint(glQuestion.getPoint());
        glQuestionAnswerRecords.setCreated(new Date());
        try {
            glQuestionAnswerRecordsDao.insert(glQuestionAnswerRecords);
        }catch (DuplicateKeyException e){
            e.printStackTrace();
            throw new GicWxAppException("重复填写问卷不能获取积分,请您填写其他问卷");
        }
        baseMapper.updateWriteCount(glQuestion.getId());

        glPointsService.grantQuestion(glQuestionAnswerRecords,glQuestion.getCompanyId(),glQuestion.getCompanyName(),glUser.getMobileSha256(),glQuestion.getId());

        QuestionFinish questionFinish = new QuestionFinish();
        questionFinish.setPoint(glQuestion.getPoint());
        return questionFinish;
    }
}
