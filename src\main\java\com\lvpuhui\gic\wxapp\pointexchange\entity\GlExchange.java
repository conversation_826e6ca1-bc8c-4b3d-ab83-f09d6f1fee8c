package com.lvpuhui.gic.wxapp.pointexchange.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 商品兑换明细表(GlExchange)实体类
 * <AUTHOR>
 * @since 2022年5月5日19:14:19
 */
@Data
public class GlExchange extends Model<GlExchange> {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 兑换码
     */
    private String code;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 使用积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 兑换人
     */
    private Long creator;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 兑换类型 0:正常商品 1:排行领取商品
     */
    private Integer exchangeType;

    /**
     * 领取的批次号(截止日期),主要用于区分是否修改了截止时间,一旦修改后,同一个商品同一个人满足条件可以再次领取
     */
    private String batchNo;
}
